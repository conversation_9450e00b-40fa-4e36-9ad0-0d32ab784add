<?php

defined('BASEPATH') OR exit('No direct script access allowed');

$route['default_controller'] = 'Dashboard';
$route['404_override'] = 'Erro_404'; // Redirecionando o erro 404 para a pagina 404
$route['translate_uri_dashes'] = FALSE;

$route['logout'] = 'login/logout';

# Clientes
$route['customers'] = 'Clientes/listar_clientes';
$route['update-status-user'] = 'Clientes/update_status_user';
$route['impersonate'] = 'Clientes/impersonate';
$route['customer-payments'] = 'Clientes/customer_payments';
$route['check-payment-customer'] = 'Clientes/check_payment_customer';
$route['insert-payment-customer'] = 'Clientes/inser_payment_customer';
$route['customer-timelime'] = 'Clientes/customer_timeline';
$route['insert-timeline'] = 'Clientes/insert_history_timeline';
$route['check-shipping'] = 'Clientes/check_shipping';
$route['client-details/(:num)'] = 'Clientes/client_details/$1';

# Orders
$route['orders'] = 'Orders/list_orders';
$route['get-orders-datatable'] = 'Orders/get_orders_datatable';
$route['check-invoice-validate'] = 'Orders/check_invoice_validate';
$route['order-details/(:num)'] = 'Orders/order_details/$1';
$route['update-purchased-order'] = 'Orders/update_purchased_order/$1';

# Invoices
$route['seller-invoices'] = 'Invoices/list_invoices';
$route['generate-invoice'] = 'Invoices/generate_invoice';
$route['get-invoices-bighub'] = 'Invoices/get_invoices_bighub';
$route['filter-invoices-bighub'] = 'Invoices/filter_invoices_bighub';

# Get invoices order sellers
$route['get-invoice-order'] = 'Invoices/download_bulk_invoices';
$route['download-invoices'] = 'Invoices/download_invoices';
$route['init-download-invoices'] = 'Invoices/init_download_invoices';
$route['get-invoice'] = 'Invoices_public/get_invoice_bulk';



# Filters
#$route['filters'] = 'Filters';
$route['seller-filter'] = 'Filters/seller_filter';
$route['filter-seller'] = 'Filters/filter';
$route['filter-month'] = 'Filters/filter_month';
$route['update-order'] = 'Filters/update_order'; 
$route['remove-payment-order'] = 'Filters/remove_payment_order'; 


# Calculos Financeiros
$route['get-orders'] = 'FlowSales/get_orders';

#Payments
$route['pay-seller'] = 'Payments/pay_the_seller';

#Marketplaces
$route['marketplaces-filter'] = 'Marketplace/marketplaces_filter';
$route['result-filter-marketplaces'] = 'Marketplace/filter_channel';
$route['values-marketplaces'] = 'Marketplace/values_marketplaces';
$route['insert-marketplace-receipt'] = 'Marketplace/insert_marketplace_receipt';
$route['cancel-receipt/(:num)'] = 'Marketplace/cancel_receipt/$1';



$route['list-clients-pending'] = 'Clientes/listar_clientes_pendente_aprovacao';
$route['data-client/(.*)'] = 'Clientes/dados/$1';
$route['approve-client/(.*)'] = 'Clientes/approve_client/$1';
$route['list-user-products/(.*)'] = 'Clientes/list_user_products/$1';

$route['product-details'] = 'Clientes/details_user_products';

$route['send-revision'] = 'Clientes/send_product_to_revision';
$route['send-approval'] = 'Clientes/send_product_to_approval';
$route['change-status'] = 'Clientes/change_status_product';


# Produtos
$route['list-products-approval'] = 'Products/list_products_to_approval';

$route['list-products-pending'] = 'Products/listar_produtos_pendentes';
$route['data-products/(.*)'] = 'Products/dados/$1';





#Rest
$route['get-orders-seller'] = 'Rest/getorders_data_client';


$route['qrcode'] = 'Qrcode';
$route['gerar'] = 'Invoices/gerar_faturas';

$route['up-orders'] = 'Orders/updateOrdersCountry';

