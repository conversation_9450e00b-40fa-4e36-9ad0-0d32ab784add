<?php

class MotorCB_model extends CI_Model {

	function __construct() {

		parent::__construct();
	}

	private function motor_calculo_e_busca($seller_id, $ini_date, $end_date)
	{
			if (!isset($state)) {
				$state = ['RECEIVED', 'SHIPPING', 'SHIPPED'];
			}
			$state_canceled = ['CANCELED', 'CLOSED'];

			$total_orders = $this->get_orders_by_seller($seller_id, null, null, $state, null, $ini_date, $end_date);
			$total_orders_canceled = $this->get_orders_by_seller($seller_id, null, null, $state_canceled, null, $ini_date, $end_date);
			#$total_orders_all = $this->get_orders_by_seller($seller_id, null, null, null, null, $ini_date, $end_date);

			$data['total_orders'] = $total_orders;
			$data['total_orders_canceled'] = $total_orders_canceled;
			#$data['total_orders_all'] = $total_orders_all;

			# Vai buscar os dados do vendedor
			$seller = $this->_getSeller($seller_id);
			$data['seller'] = $seller;

			############# Tratamento valores BIGHUB - SELLER - CUSTOMER #############

			$orders = $total_orders['result'];

			# Atraves do IBAN identifico a origem do vendedor
			$seller_country = $seller->bank_number ? substr($seller->bank_number, 0, 2) : 'Error';

			# Se não existir um IBAN ele vai tentar buscar através do NIF
			if ($seller_country == 'Error' || $seller_country != 'PT' || $seller_country != 'ES') {
				if ($this->_verificarLetra($seller->nif)) {
					$seller_country = 'ES';
				} else {
					$seller_country = 'PT';
				}
			}
			$data['pais_vendedor'] = $seller_country;

			$total_valor_de_repasse_ao_vendedor = 0;
			$soma_dos_ivas_vies = 0;
			$soma_dos_ivas = 0;


			if (isset($orders) && !empty($orders)) {
				foreach ($orders as $key => $order) {

					# Imposto PT e ES
					$percentual_pt = 23;
					$percentual_es = 21;

					$data_customer = json_decode($order->customer);

					$costumer_country = $data_customer->shipping_address->country;

					$bighub_country = 'ES';
					$orders[$key]->origens = [
						'localidade_bighub' => $bighub_country,
						'localidade_vendedor' => $seller_country,
						'localidade_comprador' => $costumer_country,
					];

					$valor_venda_retirando_a_comissao = $order->total_price - $order->total_commission;
					$orders[$key]->financeiro = [
						'valor_venda' => $order->total_price,
						'comissao_marketplace' => $order->total_commission,
						'valor_venda_retirando_a_comissao' => round($valor_venda_retirando_a_comissao, 2),
					];


					if ($seller_country == 'PT' && $costumer_country == 'ES' || $costumer_country == 'IT') {

						# Se o vendedor PT enviar para PT será cobrado 23%
						$total_do_iva = ($valor_venda_retirando_a_comissao * $percentual_pt) / 100;
						$total_menos_iva_23 = $valor_venda_retirando_a_comissao - $total_do_iva;

						if ($order->state != 'CLOSED') {
							$soma_dos_ivas_vies += $total_do_iva;
							$soma_dos_ivas += $total_do_iva;
						}

						# if cliente marcado aplicar os 5 
						$orders[$key]->financeiro += [
							'remove_5_euros' => true, #true
							'iva_vies' => true
						];

						$orders[$key]->financeiro += [
							'valor_cobrado_de_iva' => round($total_do_iva, 2),
							'valor_retirando_o_iva' => round($total_menos_iva_23, 2),
							'texto_detalhe_repasse' => round($total_menos_iva_23, 2).' - 5 = '.round($total_menos_iva_23 - 5, 2),
							'valor_de_repasse_ao_vendedor' => round($total_menos_iva_23 - 5, 2),
							'tipo_iva' => 'Vies'
						];

						$total_valor_de_repasse_ao_vendedor += $total_menos_iva_23 - 5;
					}


					if (
						$seller_country == 'ES' && $costumer_country == 'ES' ||
						$seller_country == 'ES' && $costumer_country == 'PT' ||
						$seller_country == 'PT' && $costumer_country == 'PT'
					) {

						# Se o vendedor PT enviar para PT será cobrado 23%
						$total_do_iva = ($valor_venda_retirando_a_comissao * $percentual_pt) / 100;
						$total_menos_iva_21 = $valor_venda_retirando_a_comissao - $total_do_iva;

						$soma_dos_ivas_vies += 0;
						$soma_dos_ivas += $total_do_iva;

						$orders[$key]->financeiro += [
							'remove_5_euros' => false, #true
							'iva_vies' => false
						];

						$orders[$key]->financeiro += [
							'apresenta_valor_venda_sem_iva_21' => round($total_menos_iva_21, 2),
							'valor_cobrado_de_iva' => round($total_do_iva, 2),
							'valor_venda_retirando_a_comissao' => round($valor_venda_retirando_a_comissao, 2),
							'texto_detalhe_repasse' => round($total_menos_iva_21, 2).' + '.round($total_do_iva, 2).' = '.round($valor_venda_retirando_a_comissao, 2),
							'valor_de_repasse_ao_vendedor' => round($valor_venda_retirando_a_comissao, 2),
							'tipo_iva' => 'Total'
						];

						$total_valor_de_repasse_ao_vendedor += $valor_venda_retirando_a_comissao;
					}
				}


				$total_valor_venda = 0;
				$total_das_comissoes = 0;
				$valor_venda_retirando_a_comissao = 0;
				$iva_normal = 0;
				$iva_vies = 0;
				$repasse_ao_seller = 0;
				$total_ja_pago = 0;

				# Preparando a resposta com todos os dados neceessários para cada vendedor.
				foreach ($orders as $key => $order) {
					$total_valor_venda += $order->financeiro['valor_venda'];
					$total_das_comissoes += $order->financeiro['comissao_marketplace'];
					$valor_venda_retirando_a_comissao += $order->financeiro['valor_venda_retirando_a_comissao'];
					$iva_normal += $order->financeiro['valor_cobrado_de_iva'];
					$repasse_ao_seller += $order->financeiro['valor_de_repasse_ao_vendedor'];

					if($order->paid == 1){
						$total_ja_pago += $order->financeiro['valor_de_repasse_ao_vendedor'];
					}

					if($order->financeiro['iva_vies'] == true){
						$iva_vies += $order->financeiro['valor_cobrado_de_iva'];
					}
				}

				$resp = [
					'quantidade_ordens_canceladas' => $total_orders_canceled['count'],
					'total_em_ordens_canceladas' => round($total_orders_canceled['total_price'], 2),
					'quantidade_ordens_sem_canceladas' => count($orders),
					'total_valor_venda' => round($total_valor_venda, 2),
					'total_das_comissoes' => - round($total_das_comissoes, 2),
					'valor_venda_retirando_a_comissao' =>  round($valor_venda_retirando_a_comissao, 2),
					'iva_normal' =>  round($iva_normal, 2),
					'iva_vies' =>  round($iva_vies, 2),
					'repasse_ao_seller' =>  round($repasse_ao_seller, 2),
					'total_ja_pago' =>  round($total_ja_pago, 2),
					'falta_pagar' =>  round($repasse_ao_seller - $total_ja_pago, 2),
					
				];

			} else {
				echo 'no orders';
			}
			// echo json_encode($data);
			// die();

			return $resp;

		
	}
	
}
