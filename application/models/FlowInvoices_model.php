<?php

class FlowInvoices_model extends CI_Model
{
	# contantes
	const SKU_PREFIX = 'BIGHUB';
	const USER_ID = 33;
	const URL_PUSHTOQUEUE = 'https://app.bighub.store/queue/';
	const URL_GETINVOICES= 'https://thor.bighub.store/get-invoice';

	public function bulk_get_invoice($data, $user_id = null)
	{
		$bulkName = 'Download Invoices';

		if($user_id){
			$bulkName = 'Download Invoices - Seller:' . $user_id;
		}

		$job = [
			'name' => $bulkName,
			'url' => self::URL_GETINVOICES,
			'method' => 'POST',
			'headers' => [
				'Accept' => 'application/json',
				'Content-Type' => 'application/json'
			],
			'options' => [
				'removeOnComplete' => 100000,
				'delay' => 1000,
				'concurrency' => 1,
				'limiter' => [
					'max'=> 1,
					'duration' => 1000
				]
			],
			'data' => $data
		];

		$url_api = self::URL_PUSHTOQUEUE . "downloads-seller-invoices/job";
		$handle  = curl_init($url_api);
		$request_headers = [];
		$request_headers[] = 'Accept: application/json';
		$request_headers[] = 'Content-Type: application/json';
		$request_headers[] = 'x-apikey: a57ed2dc-f0e3-4883-a3e5-959cc8af4c54';

		curl_setopt($handle, CURLOPT_POST, true);
		curl_setopt($handle, CURLOPT_POSTFIELDS, json_encode($job));
		curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);

		curl_exec($handle);
	}

}
