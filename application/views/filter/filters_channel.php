<div class="container-fluid py-4">

  <div class="row">
    <div class="col-lg-6 mt-lg-0 mt-4 margin-filter">

    </div>
    <div class="col-lg-6 mt-lg-0 mt-4"></div>
  </div>


  <div class="row">

    <div class="col-lg-3 mt-4 mt-lg-0">
      <div class="card card-body mb-3" id="profile">
        <div class="row">
          <div class="col-12 font-info-dashboard-date">
            Channel<br><b><?= $channel ?></b><br>
            <small class="font-info-dashboard">Total geral incluindo canceladas - <b><?= FormatarValor($vendas_geral['total']) ?></b></small>
          </div>
        </div>
      </div>

      <div class="card card-body mb-3" id="profile">
        <div class="row">
          <div class="col-12 font-info-dashboard-date">
            Filtragem entre as datas <br><b><?= $ini_date ?></b> a <b><?= $end_date ?></b><br>
            
          </div>
        </div>
      </div>

      <div class="card card-body mb-3" id="profile">
        <div class="row">
          <div class="col-12 font-info-dashboard-date">
              <div class="card-header p-3 pt-2 bg-transparent">
                <div class="text-end pt-1">
                  <p class="text-sm mb-0 text-capitalize font-info-dashboard">Total Geral Vendas s/ canceladas</p>
                  <h4 class="mb-0 font-value-dashboard"><span class="font-euro-symbol">€</span> <?= FormatarValor($vendas['total']) ?></h4>
                  <small class="font-info-dashboard">Quantidade - <?= $vendas['count'] ?></small>
                  
                </div>
              </div>
          </div>
        </div>
      </div>

      <div class="card card-body mb-3" id="profile">
        <div class="row">
          <div class="col-12 font-info-dashboard-date">
              <div class="card-header p-3 pt-2 bg-transparent">
              <div class="text-end pt-1">
                  <p class="text-sm mb-0 text-capitalize font-info-dashboard">Comissões marketplace</p>
                  <h4 class="mb-0 font-value-dashboard"><span class="font-euro-symbol">€</span> - <?= FormatarValor($vendas['comission_mkt']) ?></h4>
                </div>
              </div>
          </div>
        </div>
      </div>

      <div class="card card-body mb-3" id="profile">
        <div class="row">
          <div class="col-12 font-info-dashboard-date">
              <div class="card-header p-3 pt-2 bg-transparent">
              <div class="text-end pt-1">
                  <p class="text-sm mb-0 text-capitalize font-info-dashboard">A receber do marketplace</p>
                  <h4 class="mb-0 font-value-dashboard color-received-mkt"><span class="font-euro-symbol">€</span> <?= FormatarValor($vendas['total'] - $vendas['comission_mkt']) ?></h4>
                </div>
              </div>
          </div>
        </div>
      </div>

      <div class="card card-body mb-3" id="profile">
        <div class="row">
          <div class="col-12 font-info-dashboard-date">
              <div class="card-header p-3 pt-2 bg-transparent">
                <div class="text-end pt-1">
                  <p class="text-sm mb-0 text-capitalize font-info-dashboard">Vendas Canceladas</p>
                  <h4 class="mb-0 color-red font-value-dashboard"><span class="font-euro-symbol">€</span> <?= FormatarValor($canceladas['total']) ?></h4>
                  <small class="font-info-dashboard">Quantidade - <?= $canceladas['count'] ?></small>
                </div>
              </div>
          </div>
        </div>
      </div>

    </div>

  

    <div class="col-lg-6 mt-4 mt-lg-0">
      <div class="col-12">
        <div class="row">
        
        </div>
      </div>
    </div>

  </div>

  <div class="row mt-4">
    <div class="col-6"></div>
    <div class="col-6"></div>
  </div>
</div>