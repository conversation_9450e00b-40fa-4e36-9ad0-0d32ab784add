<div class="container-fluid py-4">

  <div class="row">
    <div class="col-lg-3 mt-4 mt-lg-0">
      <div class="card card-body mb-3" id="profile">
        <div class="row">
          <div class="col-12 text-sm t-c">
            Filtragem entre as datas <br><b><?= $ini_date ?></b> a <b><?= $end_date ?></b><br>
            <hr class="hr-normal">
            <div class="card_total ">
              <p class="text-sm mb-0 text-capitalize font-info-dashboard">Total Vendas - ( </span> <?= $total_orders['count'] ? $total_orders['count'] : '0.00' ?> )</p>
              <h4 class="mb-0 font-value-dashboard"><span class="font-euro-symbol"></span> <?= FormatarValor($total_orders['total_price']) ?></h4>
            </div>
            <hr class="hr-normal">
            <div class="text-sm">
              Total geral das vendas ( <?= $total_orders_all['count'] ?> ) - <?= FormatarValor($total_orders_all['total_price']) ?>
            </div>
            <div class="text-sm color-red mt-1">
              Cancelamentos ( <?= $total_orders_canceled['count'] ?> ) - <?= FormatarValor($total_orders_canceled['total_price']) ?>
            </div>
          </div>
        </div>
      </div>

      <div class="card card-body">
        <div class="row justify-content-center align-items-center">
          <div class="col-sm-auto col-4">
            <!-- <div class="avatar avatar-xl position-relative">
              <img src="../../../assets/img/bruce-mars.jpg" alt="bruce" class="w-100 rounded-circle shadow-sm">
            </div> -->
          </div>
          <div class="col-sm-auto col-8 my-auto">
            <div class="h-100">
              <h6 class="mb-1 font-weight-bolder">
                <?= $seller->name ?>
              </h6>
              <hr class="color-hr">
              <p class="mb-0 mt-1 font-weight-normal text-sm">
                <b>Email:</b> <?= $seller->email ?>
              </p>
              <p class="mb-0 mt-1 font-weight-normal text-sm">
                <b>Iban:</b> <?= $seller->bank_number ?>
              </p>
              <p class="mb-0 mt-1 font-weight-normal text-sm">
                <b>Contato:</b> <?= $seller->prefix_number ?> <?= $seller->phone_number ?>
              </p>
              <p class="mb-0 mt-1 font-weight-normal text-sm">
                <b>Doc number:</b> <?= $seller->nif ?>
              </p>
              <hr class="color-hr">
              <p class="mb-0 mt-1 font-weight-normal text-sm">
                <b>Cadastrado dia:</b> <?= date('d/m/Y', strtotime($seller->create_at)) ?><br>
                <b>País Vendedor:</b> <?= $pais_vendedor ?>
              </p>
            </div>
          </div>
          <div class="col-sm-auto ms-sm-auto mt-sm-0 mt-3 d-flex">
            <!-- <label class="form-check-label mb-0">
              <small id="profileVisibility">
                Switch to invisible
              </small>
            </label>
            <div class="form-check form-switch ms-2 my-auto">
              <input class="form-check-input" type="checkbox" id="flexSwitchCheckDefault23" checked onchange="visible()">
            </div> -->
          </div>
          <!-- <div class="w-50 text-end">
            <button class="btn btn-outline-secondary mb-3 mb-md-0 ms-auto" type="button" name="button">Inactive Seller</button>
            <button class="btn bg-gradient-danger mb-0 ms-2" type="button" name="button">Delete Account</button>
          </div> -->
        </div>
      </div>
      <div class="card mt-3 esconder" id="marketplaces-values">
        <div class="card-header pb-0 p-3">
          <div class="d-flex align-items-center">
            <h6 class="mb-0">Marketplaces</h6>
            <button type="button" class="btn btn-icon-only btn-rounded btn-outline-secondary mb-0 ms-2 btn-sm d-flex align-items-center justify-content-center ms-auto" data-bs-toggle="tooltip" data-bs-placement="bottom" title="See the consumption per room">
              <i class="fas fa-info"></i>
            </button>
          </div>
        </div>
        <div class="card-body p-3">
          <div class="row">
            <div class="col-12">
              <div class="table-responsive">
                <table class="table align-items-center mb-0">
                  <tbody>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $worten_pt_filter['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Worten PT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($worten_pt_filter['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $worten_es_filter['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Worten ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($worten_es_filter['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"> <?= $carrefour_es_filter['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Carrefour ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($carrefour_es_filter['total_price']) ?></span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"> <?= $mediamarket_filter['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Media Market</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"><?= FormatarValor($mediamarket_filter['total_price']) ?></span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"><?= $fnac_pt_filter['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Fnac PT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($fnac_pt_filter['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"><?= $fnac_es_filter['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Fnac ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"><?= FormatarValor($fnac_es_filter['total_price']) ?></span>
                      </td>
                    </tr>

                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>

      <div class="card mt-3 t-c" id="edit-resumo-order">
        <div class="card-header pb-0 p-3">
          <div class="d-flex align-items-center">
            <h6 class="mb-0">Efetuar o seguinte pagamento</h6>
          </div>
        </div>

        <div class="card-body">
          <b><?= $ini_date ?></b> a <b><?= $end_date ?></b><br>
          <h5 id="resumo-ordens-a-pagar">0,00</h5>
          Vendedor: <b><?= $seller->name ?></b><br>
          Data Pagamento: <?= date('d/m/Y'); ?>

          <!-- <div class="col-12 text-sm" id="pay-day"></div><br> -->
          <hr class="color-hr">
          <div class="input-group input-group-dynamic mt-3">
            <label class="form-label">Observação</label>
            <input type="text" class="form-control w-100" aria-describedby="emailHelp1" name="obs_remessa" id="obs_remessa">
          </div>

          <div class="button-row mt-4 t-c">
            <div class="spinner-border text-default esconder" role="status" id="loading-resume">
              <span class="sr-only">Loading...</span>
            </div>
            <button type="button" class="btn btn-primary btn-lg" id="sendPayment">Pagar ao Vendedor</button>
          </div>
        </div>


      </div>

      <div class="card mt-3 esconder" id="edit-order">
        <div class="card-header pb-0 p-3">
          <div class="d-flex align-items-center">
            <h6 class="mb-0">Marcar a order como paga</h6>
          </div>
        </div>

        <div class="card-body">
          <div class="col-12 text-sm" id="order-id"></div>
          <div class="col-12 text-sm" id="pay-day"></div><br>
          <div class="input-group input-group-dynamic mt-3">
            <label class="form-label">Observação</label>
            <input type="text" class="form-control w-100" aria-describedby="emailHelp" name="obs" id="obs">
          </div>

          <div class="button-row d-flex mt-4">
          <div class="c-loader esconder" id="loader"></div> <button class="btn bg-gradient-dark ms-auto mb-0 js-btn-next" onclick="gravar_informacoes()" id="button-rec">Gravar</button>
          </div>
        </div>

      </div>

    </div>

    <div class="col-lg-9 mt-4 mt-lg-0">
      <div class="col-12">
        <div class="row">

          <div class="col-lg-3 col-md-6 col-sm-6 mt-lg-0 mt-4">
            <div class="card  mb-2">
              <div class="card-header p-3 pt-2 bg-transparent">

                <div class="text-end pt-1">
                  <p class="text-sm mb-0 text-capitalize font-info-dashboard">Comissões dos Marketplaces</p>
                  <h4 class="mb-0 font-value-dashboard"><span class="font-euro-symbol"></span> - <?= FormatarValor($total_orders['total_commission']) ?></h4>
                </div>
              </div>
              <hr class="horizontal my-0 dark">

            </div>
          </div>
          <div class="col-lg-2 col-md-6 col-sm-6 mt-lg-0 mt-4">
            <div class="card ">
              <div class="card-header p-3 pt-2 bg-transparent">

                <div class="text-end pt-1">
                  <p class="text-sm mb-0 text-capitalize font-info-dashboard">Receber MKT</p>
                  <h4 class="mb-0 font-value-dashboard color-received-mkt"><span class="font-euro-symbol"></span> <?= FormatarValor($total_orders['total_price'] - $total_orders['total_commission']) ?></h4>
                </div>
              </div>
              <hr class="horizontal my-0 dark">

            </div>
          </div>
          <div class="col-lg-2 col-md-6 col-sm-6 mt-lg-0 mt-4">
            <div class="card ">
              <div class="card-header p-3 pt-2 bg-transparent">

                <div class="text-end pt-1">
                  <p class="text-sm mb-0 text-capitalize font-info-dashboard">IVA - Total</p>
                  <h4 class="mb-0 font-value-dashboard "><span class="font-euro-symbol"></span> <?= FormatarValor($calculos_de_valores_geral['soma_dos_ivas']) ?></h4>
                </div>
              </div>
              <hr class="horizontal my-0 dark">

            </div>
          </div>
          <div class="col-lg-2 col-md-6 col-sm-6 mt-lg-0 mt-4">
            <div class="card ">
              <div class="card-header p-3 pt-2 bg-transparent">

                <div class="text-end pt-1">
                  <p class="text-sm mb-0 text-capitalize font-info-dashboard">IVA - Vies </p>
                  <h4 class="mb-0 font-value-dashboard "><span class="font-euro-symbol"></span> <?= FormatarValor($calculos_de_valores_geral['soma_dos_ivas_vies']) ?></h4>
                </div>
              </div>
              <hr class="horizontal my-0 dark">

            </div>
          </div>

          <div class="col-lg-3 col-md-6 col-sm-6 mt-lg-0 mt-4">
            <div class="card">
              <div class="card-body p-3 position-relative">
                <div class="row">
                  <div class="col-6">
                    <div class="dropdown text-start">
                      <span class="text-sm text-start text-info font-weight-bolder mt-auto mb-0" id="total-pago"><span class="font-euro-symbol"></span> 0.00</span>
                      <span class="text-start f12-db">Pago ao vendedor</span>
                    </div>
                  </div>

                  <div class="col-6 text-end">
                    <p class="text-sm mb-1 text-capitalize font-weight-bold">Pagar Vendedor </p>
                    <h5 class="font-weight-bolder mb-0">
                      <?= FormatarValor($calculos_de_valores_geral['repasse_ao_vendedor']) ?>
                    </h5>
                  </div>

                </div>
              </div>
            </div>
          </div>

          <!-- <div class="col-lg-3 col-md-6 col-sm-6 mt-lg-0 mt-4">
            <div class="card ">
              <div class="card-header p-3 pt-2 bg-transparent">

                <div class="text-end pt-1">
                  <p class="text-sm mb-0 text-capitalize font-info-dashboard">Pagar Vendedor </p>
                  <h4 class="mb-0 font-value-dashboard color-received-mkt"><span class="font-euro-symbol"></span> <?= FormatarValor($calculos_de_valores_geral['repasse_ao_vendedor']) ?></h4>
                </div>
              </div>
              <hr class="horizontal my-0 dark">

            </div>
          </div> -->

        </div>
        <div class="row mt-2">

          <div class="col-lg-3 col-md-6 col-sm-6 mt-lg-0 mt-4">
            <div class="card  mb-2 background-box">
              <div class="card-header p-3 pt-2 bg-transparent">

                <div class="text-end pt-1">
                  <p class="text-sm mb-0 text-capitalize font-info-dashboard">Comissão BIGhub 15%</p>
                  <h4 class="mb-0 font-value-dashboard"><span class="font-euro-symbol"></span> - <?= FormatarValor($calculos_de_valores_geral['comissao_15_por_cento']) ?></h4>

                </div>
              </div>
              <hr class="horizontal my-0 dark">

            </div>
          </div>

          <div class="col-lg-3 col-md-6 col-sm-6 mt-lg-0 mt-4">
            <div class="card  mb-2 background-box">
              <div class="card-header p-3 pt-2 bg-transparent">

                <div class="text-end pt-1">
                  <p class="text-sm mb-0 text-capitalize font-info-dashboard">Lucro cobrando 15% </p>
                  <h4 class="mb-0 font-value-dashboard"><span class="font-euro-symbol"></span> 0.00</h4>

                </div>
              </div>
              <hr class="horizontal my-0 dark">

            </div>
          </div>


          <div class="col-lg-3 col-md-6 col-sm-6 mt-lg-0 mt-4">
            <div class="card">
              <div class="card-body p-3 position-relative">
                <div class="row">
                  <div class="col-5">
                    
                  </div>

                  <div class="col-7 text-end">
                    <p class="text-sm mb-1 text-capitalize font-weight-bold">Valor Faturado</p>
                    <h5 class="font-weight-bolder mb-0" id="valor-faturado">0.00</h5>
                  </div>

                </div>
              </div>
            </div>
          </div>

          <div class="col-lg-3 col-md-6 col-sm-6 mt-lg-0 mt-4">
            <div class="card">
              <div class="card-body p-3 position-relative">
                <div class="row">
                  <div class="col-5">
                    <div class="dropdown text-start">
                      <span class="text-sm text-start text-danger font-weight-bolder mt-auto mb-0" id="saldo-devedor"><span class="font-euro-symbol"></span> 0.00</span>
                      <span class="text-start f12-db">Saldo devedor</span>
                    </div>
                  </div>

                  <div class="col-7 text-end">
                    <p class="text-sm mb-1 text-capitalize font-weight-bold">Total a pagar </p>
                    <h5 class="font-weight-bolder mb-0" id="ordens-a-pagar">0.00</h5>
                  </div>

                </div>
              </div>
            </div>
          </div>

        </div>
      </div>


      <div class="col-12 mt-3">
        <div class="card">

          <div class="table-responsive">
            <table class="table table-flush" id="datatable-clients">
              <thead class="thead-light">
                <tr>
                  <th></th>
                  <th>Order Id</th>
                  <th>Canal</th>
                  <th>Valor - Comissão MKT</th>
                  <th>Valor - IVA </th>
                  <th>Repasse Seller</th>
                  <th>Data pagamento</th>
                  <th>Actions</th>
                </tr>
              </thead>
              <tbody>
                <?php foreach ($total_orders['result'] as $order) { #var_dump($order->financeiro['valor_venda']);
                ?>
                  <tr class="<?= $order->paid == 1 ? 'order-paid' : '' ?>">

                    <?php
                    if ($order->financeiro['remove_5_euros']) {
                      $value_vendedor =  ($order->financeiro['valor_de_repasse_ao_vendedor'] - 5);
                    } else {
                      $value_vendedor = $order->financeiro['valor_de_repasse_ao_vendedor'];
                    }
                    ?>

                    <td>
                      <div class="item <?= $order->paid == 1 ? 'esconder' : '' ?>" data-id="<?= $order->paid == 0 ? $order->guid : '' ?>">
                        <input type="hidden" class="valor-pago" id="input-hidden-<?= $order->guid ?>" value="<?= ($value_vendedor && $order->paid == 0)  ? $value_vendedor : 0.00 ?>">
                        <input type="hidden" class="valor" id="input-hidden-<?= $order->guid ?>" value="<?= ($value_vendedor && $order->paid == 1)  ? $value_vendedor : 0.00 ?>">
                        <input type="checkbox" class="checkbox" value="<?= $order->guid ?>" onclick="modifyLocalStorageValue('<?= $order->guid ?>',<?= $value_vendedor ?>)">
                      </div>
                    </td>

                    <td class="text-sm font-weight-normal">
                      <?= date("d/m/Y", strtotime($order->created_date)) ?> -
                      <?php
                      $state_neuter = ['SHIPPING'];
                      $state_positive = ['RECEIVED', 'TO_COLLECT', 'SHIPPED'];
                      $state_negative = ['WAITING_ACCEPTANCE', 'WAITING_DEBIT', 'WAITING_DEBIT_PAYMENT', 'CLOSED', 'REFUNDED', 'CANCELED', 'REFUSED', 'AUTO_REFUSED', 'AUTO_CLOSED'];
                      if (in_array($order->state, $state_positive)) { ?>
                        <span class="badge-success badge-sm state-new"><?= $order->state ?></span>
                      <?php } elseif (in_array($order->state, $state_negative)) { ?>
                        <span class="badge-sm badge-negative state-new"><?= $order->state ?></span>
                      <?php } elseif (in_array($order->state, $state_neuter)) { ?>
                        <span class="badge-sm badge-neuter state-new"><?= $order->state ?></span>
                      <?php } ?> <br>
                      <?php echo $order->reference ?>
                    </td>


                    <!-- <td class="text-sm font-weight-normal"><?php echo count(json_decode($order->items)) ?></td> -->
                    <td class="text-sm font-weight-normal"><?php echo $order->channel ?>/<?php echo $order->country ?><br>
                      <b>Destino:</b><?= $order->origens['localidade_comprador'] ?>
                    </td>

                    <td class="text-sm font-weight-normal"><?= $order->financeiro['valor_venda'] ?> - <?= $order->financeiro['comissao_marketplace'] ?> = <b><?= $order->financeiro['valor_venda_retirando_a_comissao'] ?></b>
                    </td>

                    <td class="text-sm font-weight-normal"><?php echo $order->financeiro['valor_cobrado_de_iva'] ?><br>
                      Iva: <b><?= $order->financeiro['tipo_iva'] ?></b>
                    </td>

                    <td class="text-sm font-weight-normal"><b>
                        <?php
                        if ($order->financeiro['remove_5_euros']) {
                          echo $order->financeiro['valor_de_repasse_ao_vendedor'] . ' -5 = ' . ($order->financeiro['valor_de_repasse_ao_vendedor'] - 5);
                        } else {
                          echo $order->financeiro['valor_de_repasse_ao_vendedor'];
                        }
                        ?>
                      </b>
                    </td>

                    <td class="text-sm font-weight-normal font-11-important"><?= $order->payment_date ? date_mysql_to_human($order->payment_date) . '<br>' . $order->obs : '' ?></td>
                    <td class="text-sm font-weight-normal">
                      <a class="btn bg-gradient-dark btn-sm new-button-2 <?= $order->paid == 1 ? 'esconder' : '' ?>" onclick="get_order('<?= $order->guid ?>', '<?= $order->reference ?>')" data-bs-toggle="modal" data-bs-target="#modal-dados-order">Pagar</a>
                      <?php if (!empty($order->payment_date)) { ?>
                        <a class="btn bg-gradient-primary btn-sm new-button-2" onclick="excluir('<?= $order->guid ?>')" data-bs-toggle="modal" data-bs-target="#modal-dados-order">Excluir pagamento</a>
                      <?php } ?>
                    </td>
                  </tr>


                <?php } ?>
              </tbody>
            </table>
          </div>
        </div>
      </div>



    </div>

  </div>

  <div class="row mt-4">
    <div class="col-6"></div>
    <div class="col-6"></div>
  </div>
</div>


<script>
  function get_order(guid, reference) {

    const marketplaces = document.getElementById('marketplaces-values');
    marketplaces.classList.add('esconder');

    const editResumo = document.getElementById('edit-resumo-order');
    editResumo.classList.add('esconder');

    const editOrder = document.getElementById('edit-order');
    editOrder.classList.remove('esconder');

    const orderIdElement = document.getElementById('order-id');
    orderIdElement.textContent = 'OrderID - ' + reference;

    const botaoGravar = document.getElementById('button-rec');
    botaoGravar.setAttribute('onclick', `gravar_informacoes("${guid}")`);

    var obs = document.getElementById("obs");
    obs.focus();

    const dataElement = document.getElementById('pay-day');
    const dataAtual = new Date();

    const dia = String(dataAtual.getDate()).padStart(2, '0');
    const mes = String(dataAtual.getMonth() + 1).padStart(2, '0');
    const ano = dataAtual.getFullYear();

    const dataFormatada = `${dia}/${mes}/${ano}`;

    dataElement.textContent = 'Data pagamento - ' + dataFormatada;
  }

  function gravar_informacoes(guid_id) {
    const loader = document.getElementById('loader');
    loader.classList.remove('esconder');

    const obs = document.getElementById('obs');

    $.ajax({
      url: base_url + 'update-order',
      type: 'GET',
      dataType: 'json',
      data: {
        guidId: guid_id,
        obs: obs.value
      },
      success: function(response) {
        location.reload();
      },
      error: function(xhr, status, error) {
        // Lógica de tratamento de erros
        console.error(xhr.responseText);
      }
    });
  }

  function excluir_payment(guidId) {
    const loader = document.getElementById('loader');
    loader.classList.remove('esconder');

    $.ajax({
      url: base_url + 'remove-payment-order',
      type: 'GET',
      dataType: 'json',
      data: {
        guidId: guidId
      },
      success: function(response) {
        location.reload();
      },
      error: function(xhr, status, error) {
        // Lógica de tratamento de erros
        console.error(xhr.responseText);
      }
    });
  }

  function excluir(guidId) {

    var resposta = window.confirm("Deseja prosseguir com o cancelamento do pagamento?");
    if (resposta) {
      excluir_payment(guidId);
    }
  }
</script>



<script>
  // Função para armazenar IDs no localStorage
  function armazenarIDsNoLocalStorage() {
    var payment_button = document.getElementById("sendPayment");
    payment_button.disabled = false;

    var itens = document.querySelectorAll('.item');
    var ids = [];

    itens.forEach(function(item) {
      var id = item.getAttribute('data-id');
      if (id) {
        ids.push(id);
      }
    });

    // Armazenar os IDs no localStorage
    localStorage.setItem('ids', JSON.stringify(ids));

    var valorNoLocalStorage = localStorage.getItem("ids");
      if (valorNoLocalStorage === "[]") {
        payment_button.disabled = true; 
      }

  }

  // Chamar a função para armazenar os IDs
  armazenarIDsNoLocalStorage();

  // Marcar todos checkboxes com IDs do localStorage
  function marcarCheckboxesComLocalStorage() {
    var idsNoLocalStorage = JSON.parse(localStorage.getItem('ids'));
    var checkboxes = document.querySelectorAll('.checkbox');

    checkboxes.forEach(function(checkbox) {
      var value = checkbox.value;
      if (idsNoLocalStorage.includes(value)) {
        checkbox.checked = true;
      }
    });
  }
  marcarCheckboxesComLocalStorage();


  //Função para somar os valores e salvar no localStorage
  function calcularESalvar() {
    let total = 0;
    let total_pago = 0;

    // Seleciona todos os inputs ocultos com a classe "valor" dentro da tabela
    const inputsValores = document.querySelectorAll('#datatable-clients .item .valor');
    const inputsValoresPago = document.querySelectorAll('#datatable-clients .item .valor-pago');

    // Percorre os inputs e soma os valores
    inputsValores.forEach(input => {
      total_pago += parseFloat(input.value);
    });

    inputsValoresPago.forEach(input => {
      total += parseFloat(input.value);
    });

    // Salva o total no localStorage
    localStorage.setItem('total_pago', parseFloat(total.toFixed(2)));
    localStorage.setItem('total_orders', parseFloat(total.toFixed(2)));

    const elementoH4 = document.getElementById('ordens-a-pagar');
    elementoH4.textContent = total.toLocaleString('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });

    const resumoPagar = document.getElementById('resumo-ordens-a-pagar');
    resumoPagar.textContent = total.toLocaleString('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });

    const totalPago = document.getElementById('total-pago');
    totalPago.textContent = total_pago.toLocaleString('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });

    const valorFaturado = document.getElementById('valor-faturado');
    valorFaturado.textContent = total_pago.toLocaleString('pt-BR', {
      minimumFractionDigits: 2,
      maximumFractionDigits: 2
    });
  }
  calcularESalvar();


  function modifyLocalStorageValue(newValue, value) {
    var payment_button = document.getElementById("sendPayment");
    payment_button.disabled = false;
    
    // Verificar se o localStorage é suportado pelo navegador
    if (typeof(Storage) !== "undefined") {
      // Obter o valor atual do localStorage
      let storedValue = localStorage.getItem("ids");
      let valueArray = storedValue ? JSON.parse(storedValue) : [];

      // Verificar se o valor já está presente no array
      const index = valueArray.indexOf(newValue);
      if (index !== -1) {
        valueArray.splice(index, 1);
        atualizarValorLocalStorage('total_pago', value, 'subtracao');

      } else {
        valueArray.push(newValue);
        atualizarValorLocalStorage('total_pago', value, 'soma');
      }

      // Atualizar o valor no localStorage
      localStorage.setItem("ids", JSON.stringify(valueArray));

      var valorNoLocalStorage = localStorage.getItem("ids");
      if (valorNoLocalStorage === "[]") {
        payment_button.disabled = true; 
      }

    } else {
      console.log("LocalStorage não suportado pelo navegador.");
    }
  }

  function atualizarValorLocalStorage(chave, valor, operacao) {
    // Verifica se a chave existe no localStorage
    if (localStorage.getItem(chave) !== null) {
      // Obtém o valor atual da chave do localStorage
      var valorAtual = parseFloat(localStorage.getItem(chave));

      // Verifica a operação a ser realizada
      if (operacao === 'soma') {
        valorAtual += parseFloat(valor);
      } else if (operacao === 'subtracao') {
        valorAtual -= parseFloat(valor);
      } else {
        console.log('Operação inválida. Use "soma" ou "subtracao".');
        return null;
      }

      // Atualiza o valor no localStorage
      localStorage.setItem(chave, valorAtual);

      localStorage.setItem('total_pago', parseFloat(valorAtual.toFixed(2)));

      const elementoH4 = document.getElementById('ordens-a-pagar');
      elementoH4.textContent = valorAtual.toLocaleString('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      const resumoPagar = document.getElementById('resumo-ordens-a-pagar');
      resumoPagar.textContent = valorAtual.toLocaleString('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      var valorFixo = parseFloat(localStorage.getItem('total_orders'));
      var calc = valorFixo - valorAtual;
      const saldoDevedor = document.getElementById('saldo-devedor');
      saldoDevedor.textContent = calc.toLocaleString('pt-BR', {
        minimumFractionDigits: 2,
        maximumFractionDigits: 2
      });

      return valorAtual;
    } else {
      return null; // Chave não encontrada no localStorage
    }
  }
</script>

<script>
  document.getElementById("sendPayment").addEventListener("click", function() {

    this.disabled = true;

    const obsRemessa = document.getElementById("obs_remessa").value;
    // Extrai os parâmetros da URL
    const urlParams = new URLSearchParams(window.location.search);
    const seller = urlParams.get("seller");
    const iniDate = urlParams.get("ini_date");
    const endDate = urlParams.get("end_date");

    // Pega os dados do LocalStorage
    const dadosLocalStorage = {};
    for (let i = 0; i < localStorage.length; i++) {
      const chave = localStorage.key(i);
      const valor = localStorage.getItem(chave);
      dadosLocalStorage[chave] = valor;
    }

    const idsString = localStorage.getItem("ids");
    const idsArray = JSON.parse(idsString);


    // Combina os valores dos parâmetros da URL com os valores do LocalStorage
    const dadosCombinados = {
      ...dadosLocalStorage,
      seller: seller,
      ini_date: iniDate,
      end_date: endDate,
      obs_remessa: obsRemessa,
      ids: idsArray
    };

    const endpointURL = base_url + "pay-seller";
    const divLoading = document.getElementById("loading-resume");
    const desejaAvancar = window.confirm("Deseja prosseguir com o pagamento de " + dadosCombinados.total_pago + " ao Seller " + dadosCombinados.seller + ", o pagamento é referente ao filtro de ordens do dia " + formatarData(dadosCombinados.ini_date) + " a " + formatarData(dadosCombinados.end_date) + ".");
    
    if (desejaAvancar) {
      divLoading.classList.remove("esconder");
      // Configuração para a requisição POST
      const requestOptions = {
        method: "POST",
        headers: {
          "Content-Type": "application/json",
        },
        body: JSON.stringify(dadosCombinados),
      };

      // Envia os dados para o endpoint
      fetch(endpointURL, requestOptions)
        .then(response => response.json())
        .then(data => {
          divLoading.classList.add("esconder");
          console.log("Dados enviados com sucesso:", data);
          this.disabled = false;
          //location.reload();
        })
        .catch(error => {
          console.error("Erro ao enviar os dados:", error);
        });
    }else{
      this.disabled = false;
    }
    return;
  });

  function formatarData(data) {
    const partes = data.split('-'); // Divide a data em partes
    if (partes.length === 3) {
      const novaData = `${partes[2]}/${partes[1]}/${partes[0]}`;
      return novaData;
    }
    return data; // Retorna a data no formato original se algo der errado
  }
</script>