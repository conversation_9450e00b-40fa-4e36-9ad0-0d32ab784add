<div class="container-fluid py-4">
    <div class="row">
        <div class="col-12">
            <div class="row">

                <div class="col-lg-4 col-md-6 col-sm-6 mt-lg-0 mt-4">
                    <div class="card">
                        <div class="card-body p-3 position-relative">
                            <div class="row">
                                <div class="col-8">
                                    <div class="dropdown text-start">
                                        <span class="text-start f12-db display-ib">Total em Vendas</span><span class="text-sm text-start text-info font-weight-bolder mt-auto mb-0" id="total-pago"><span class="font-euro-symbol"></span> <?= FormatarValor($details_orders['total_geral']['total_geral_vendas']) ?></span><br>
                                        <span class="text-start f12-db display-ib">Comissões Marketplaces</span><span class="text-sm text-start text-danger font-weight-bolder mt-auto mb-0" id="total-pago"><span class="font-euro-symbol"></span>- <?= FormatarValor($details_orders['total_geral']['total_geral_comissoes']) ?></span><br>
                                    </div>
                                </div>
                                <div class="col-4 text-end">
                                    <p class="text-sm mb-1 text-capitalize font-weight-bold">Recebemos MKT</p>
                                    <h5 class="font-weight-bolder mb-0 text-info">
                                        <?= FormatarValor($details_orders['total_geral']['total_recebimento_mkt']) ?>
                                    </h5>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>
                
                <div class="col-lg-4 col-md-6 col-sm-6 mt-lg-0 mt-4">
                    <div class="card">
                        <div class="card-body p-3 position-relative">
                            <div class="row">
                                <div class="col-7">
                                    <div class="dropdown text-start">
                                        <span class="text-start f12-db display-ib">Iva Normal</span><span class="text-sm text-start font-weight-bolder mt-auto mb-0" id="total-pago"><span class="font-euro-symbol"></span> <?= FormatarValor($details_orders['total_geral']['total_iva_normal']) ?></span><br>
                                        <span class="text-start f12-db display-ib">Iva Vies</span><span class="text-sm text-start font-weight-bolder mt-auto mb-0" id="total-pago"><span class="font-euro-symbol"></span> <?= FormatarValor($details_orders['total_geral']['total_iva_vies']) ?></span><br>
                                    </div>
                                </div>
                                <div class="col-5 text-end">
                                    <p class="text-sm mb-1 text-capitalize font-weight-bold">Total somatória dos 5 €</p>
                                    <h5 class="font-weight-bolder mb-0 text-info">
                                        <?= FormatarValor($details_orders['total_geral']['total_5_euros_descontados']) ?>
                                    </h5>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 col-sm-6 mt-lg-0 mt-4">
                    <div class="card">
                        <div class="card-body p-3 position-relative">
                            <div class="row">
                                <div class="col-8">
                                    <div class="dropdown text-start">
                                    <span class="text-start f12-db display-ib">Repasse aos Vendedores</span><span class="text-sm text-start text-danger font-weight-bolder mt-auto mb-0" id="total-pago"><span class="font-euro-symbol"></span> <?= FormatarValor($details_orders['total_geral']['total_repasse_ao_seller']) ?></span><br>
                                        <span class="text-start f12-db display-ib">Pago aos vendedores</span><span class="text-sm text-start text-info font-weight-bolder mt-auto mb-0" id="total-pago"><span class="font-euro-symbol"></span>  - <?= FormatarValor($details_orders['total_geral']['total_ja_pago_aos_vendedores']) ?></span><br>

                                       

                                    </div>
                                </div>
                                <div class="col-4 text-end">
                                    <p class="text-sm mb-1 text-capitalize font-weight-bold">Valores Pendentes</p>
                                    <h5 class="font-weight-bolder mb-0 text-danger">
                                    <?= FormatarValor($details_orders['total_geral']['total_pendente_de_pagamento']) ?>
                                    </h5>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>

                <!-- <div class="col-lg-3 col-md-6 col-sm-6 mt-lg-0 mt-4">
            <div class="card ">
              <div class="card-header p-3 pt-2 bg-transparent">

                <div class="text-end pt-1">
                  <p class="text-sm mb-0 text-capitalize font-info-dashboard">Pagar Vendedor </p>
                  <h4 class="mb-0 font-value-dashboard color-received-mkt"><span class="font-euro-symbol"></span> <?= FormatarValor($calculos_de_valores_geral['repasse_ao_vendedor']) ?></h4>
                </div>
              </div>
              <hr class="horizontal my-0 dark">

            </div>
          </div> -->

            </div>
        </div>
    </div>

    <div class="row">


        <div class="col-lg-12 mt-4 mt-lg-0">

            <div class="col-12 mt-4">
                <div class="card mb-4">
                    <div class="card-header pb-0 p-3">
                        <h6>Resumo de vendedores entre as datas <?= $details_orders['datas']['texto_data'] ?></h6>
                    </div>
                    <div class="card-body px-0 pt-0 pb-2">
                        <div class="table-responsive p-0">
                            <table class="table align-items-center mb-0" id="datatable-clients">
                                <thead>
                                    <tr>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Vendedor</th>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2">Total valor - Comissão</th>
                                        <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Iva Normal</th>
                                        <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Iva Vies</th>
                                        <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Repasse ao Vendedor</th>
                                        <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Pago</th>
                                        <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Pendente</th>
                                        <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Ação</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($details_orders['orders'] as $order) { #var_dump($order); 
                                    ?>
                                        <tr>
                                            <td>
                                                <div class="d-flex px-3 py-1">
                                                    <div class="d-flex flex-column justify-content-center">
                                                        <h6 class="mb-0 text-sm">ID <?= $order['id_vendedor'] ?> - <?= $order['nome_vendedor'] ?></h6>
                                                        <p class="text-sm font-weight-normal text-secondary mb-0"><span class="text-success font-weight-bold"><?= $order['quantidade_ordens_sem_canceladas'] ?></span> <?= $order['quantidade_ordens_sem_canceladas'] > 1 ? 'ordens' : 'ordem' ?> - <span class="text-danger font-weight-bold"><?= $order['quantidade_ordens_canceladas'] ?></span> canceladas</p>
                                                    </div>
                                                </div>
                                            </td>
                                            <td>
                                                <p class="text-sm font-weight-normal mb-0"><?= FormatarValor($order['total_valor_venda']) ?> - <?= FormatarValor($order['total_das_comissoes']) ?> = <?= FormatarValor($order['valor_venda_retirando_a_comissao']) ?> </p>
                                            </td>
                                            <td class="align-middle text-center text-sm">
                                                <p class="text-sm font-weight-normal mb-0"><?= FormatarValor($order['iva_normal']) ?> </p>
                                            </td>
                                            <td class="align-middle text-center text-sm">
                                                <p class="text-sm font-weight-normal mb-0"><?= FormatarValor($order['iva_vies']) ?> </p>
                                            </td>
                                            <td class="align-middle text-center text-sm">
                                                <p class="text-sm font-weight-normal mb-0"><?= FormatarValor($order['repasse_ao_seller']) ?> </p>
                                            </td>
                                            <td class="align-middle text-center text-sm">
                                                <p class="text-sm font-weight-normal mb-0"><span class="text-info font-weight-bold"><?= FormatarValor($order['total_ja_pago']) ?></span> </p>
                                            </td>
                                            <td class="align-middle text-center text-sm">
                                                <p class="text-sm font-weight-normal mb-0"><span class="text-danger font-weight-bold"><?= FormatarValor($order['falta_pagar']) ?></span> </p>
                                            </td>
                                            <td class="align-middle text-center text-sm">
                                                <a class="btn bg-gradient-dark btn-sm new-button-2" href="<?= base_url() ?>filter-seller?ini_date=<?= urlencode($details_orders['datas']['data_ini']) ?>&end_date=<?= urlencode($details_orders['datas']['data_fim']) ?>&seller=<?= $order['id_vendedor'] ?>">Detalhes</a>
                                            </td>

                                        </tr>

                                    <?php } ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>




        </div>

    </div>

    <div class="row mt-4">
        <div class="col-6"></div>
        <div class="col-6"></div>
    </div>
</div>