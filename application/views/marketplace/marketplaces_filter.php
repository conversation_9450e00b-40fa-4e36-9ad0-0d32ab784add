<div class="container-fluid my-3 py-3">
    <div class="row mb-5">
        <div class="col-lg-9 mt-lg-0">

            <div class="card mt-4" id="basic-info">
                <div class="card-header">
                    <h5>Filtrar Ordens para gerar fatura</h5>
                </div>
                <form id="filterForm" action="<?= base_url('result-filte-orders')?>" method="GET">
                    <div class="card-body pt-0">
                        <div class="row">
                            <div class="col-6">
                                <div class="input-group input-group-static">
                                    <label>Data Inicial</label>
                                    <input class="form-control datetimepicker font-16" type="text" name="ini_date" data-input>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="input-group input-group-static">
                                    <label>Data Final</label>
                                    <input class="form-control datetimepicker font-16" type="text" name="end_date" data-input>
                                </div>
                            </div>
                        </div>

                        <!-- <div class="row">
                            <div class="col-md-8 align-self-center">
                                <label class="form-label mt-4 ms-0">Vendendor</label>
                                <select class="form-control" name="seller" id="choices-seller">
                                    <option value="">Selecione um vendedor</option>
                                    <?php foreach ($clients as $client){ ?>
                                        <option value="<?= $client->id ?>"><?= $client->id ?> - <?= $client->name ?></option>
                                    <?php } ?> 
                                </select>
                            </div>
                            <div class="col-md-4">
                                <label class="form-label mt-4 ms-0">Buscar por todos os Vendedores</label>
                                <div class="form-check form-switch ms-1" id="div-check-filter">
                                    <input class="form-check-input" type="checkbox" id="flexSwitchCheckDefault" onclick="alert_filter(1)" name="filter">
                                </div>
                            </div>
                        </div> -->

                        <div class="row">
                            <div class="col-md-6 align-self-center">
                            </div>
                            <div class="col-md-6 align-self-center button-filter">
                                <button type="submit" class="btn btn-primary btn-lg">Filtrar Vendas</button>
                            </div>
                        </div>

                        <div class="row esconder" id="alert-filter">
                            <div class="col-md-8 align-self-center">
                                Ativou o <b>FILTRO COMPLETO</b>, o filtro consiste em pegar a data inicial e Data final escolhidas e calcular as vendas de todos os vendedores dentro desse periodo.
                            </div>
                            
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-lg-3"></div>
    </div>
</div>

<script>
    function alert_filter(value){
        const alert_filter = document.getElementById('alert-filter');
    
        if(value === 1){
            alert_filter.classList.remove('esconder');
            changeOnClickFunction('alert_filter(0)')
        }else{
            alert_filter.classList.add('esconder');
            changeOnClickFunction('alert_filter(1)')
        }  
    }

    function changeOnClickFunction(newfunction) {
        var checkbox = document.getElementById("flexSwitchCheckDefault");
        checkbox.setAttribute("onclick", newfunction);
    }
</script>