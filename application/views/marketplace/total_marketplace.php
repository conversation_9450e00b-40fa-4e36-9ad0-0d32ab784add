<div class="container-fluid py-4">
<div class="row">
        <div class="col-12">
            <div class="row">
               
                <div class="col-lg-8 col-md-6 col-sm-6 mt-lg-0 mt-4">
                    <div class="card">
                        <div class="card-body p-3 position-relative">
                            <div class="row">
                                <div class="col-9">
                                <div class="card-header pb-0 p-3">
                                        <h6>Vendas nos Marketplaces entre as datas <?= $datas['texto_data'] ?></h6>
                                    </div>
                                    <!-- <div class="dropdown text-start">
                                        <span class="text-start f12-db display-ib">Iva Normal</span><span class="text-sm text-start font-weight-bolder mt-auto mb-0" id="total-pago"><span class="font-euro-symbol"></span>  <?= FormatarValor(0,00) ?></span><br>
                                        <span class="text-start f12-db display-ib">Iva Vies</span><span class="text-sm text-start font-weight-bolder mt-auto mb-0" id="total-pago"><span class="font-euro-symbol"></span>  <?= FormatarValor(0,00) ?></span><br>
                                    </div> -->
                                </div>
                                <div class="col-3 text-end">
                                    <p class="text-sm mb-1 text-capitalize font-weight-bold">Quantidade de Ordens</p>
                                    <h5 class="font-weight-bolder mb-0 text-info">
                                    <?= $total_geral['quantidade_itens'] ?>
                                    </h5>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>

                <div class="col-lg-4 col-md-6 col-sm-6 mt-lg-0 mt-4">
                    <div class="card">
                        <div class="card-body p-3 position-relative">
                            <div class="row">
                                <div class="col-8">
                                    <div class="dropdown text-start">
                                        <span class="text-start f12-db display-ib">Total em Vendas</span><span class="text-sm text-start text-info font-weight-bolder mt-auto mb-0" id="total-pago"><span class="font-euro-symbol"></span> <?= FormatarValor($total_geral['total_geral_vendas']) ?></span><br>
                                        <span class="text-start f12-db display-ib">Comissões Marketplaces</span><span class="text-sm text-start text-danger font-weight-bolder mt-auto mb-0" id="total-pago"><span class="font-euro-symbol"></span>-  <?= FormatarValor($total_geral['total_geral_comissoes']) ?></span><br>
                                    </div>
                                </div>
                                <div class="col-4 text-end">
                                    <p class="text-sm mb-1 text-capitalize font-weight-bold">Recebemos MKT</p>
                                    <h5 class="font-weight-bolder mb-0 text-info">
                                        <?= FormatarValor($total_geral['total_recebimento_mkt']) ?>
                                    </h5>
                                </div>

                            </div>
                        </div>
                    </div>
                </div>

            </div>
        </div>
    </div>
   
    <div class="row">
        <div class="col-lg-12 mt-4 mt-lg-0">

            <div class="col-12 mt-4">
                <div class="card mb-4">
                    
                    <div class="card-body px-0 pt-0 pb-2">
                        <div class="table-responsive p-0">
                            <table class="table align-items-center mb-0" id="datatable-clients">
                                <thead>
                                    <tr>
                                        <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Channel</th>
                                        <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Quantidade Ordens</th>
                                        <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Total Vendas</th>
                                        <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Total comissões</th>
                                        <th class="text-center text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Total a receber</th>
                                    </tr>
                                </thead>
                                <tbody>
                                    <?php foreach ($vendas_geral as $channel) { #var_dump($channel); die();  
                                    ?>
                                        <tr>
                                            <td>
                                                <p class="text-sm font-weight-normal mb-0 text-uppercase"><b><?=$channel['channel'] ?></b> </p>
                                            </td>
                                            <td class="align-middle text-center text-sm">
                                                <p class="text-sm font-weight-normal mb-0"><b><?= $channel['data']['count'] ?></b></p>
                                            </td>
                                            <td class="align-middle text-center text-sm">
                                                <p class="text-sm font-weight-normal mb-0"><span class="text-default font-weight-bold"><?= FormatarValor($channel['data']['total_price']) ?></span></p>
                                            </td>
                                            <td class="align-middle text-center text-sm">
                                                <p class="text-sm font-weight-normal mb-0"><span class="text-danger font-weight-bold">- <?= FormatarValor($channel['data']['total_commission']) ?></span></p>
                                            </td>
                                            <td class="align-middle text-center text-sm">
                                                <p class="text-sm font-weight-normal mb-0"><span class="text-info font-weight-bold"><?= FormatarValor($channel['data']['a_receber']) ?></span></p>
                                            </td>
                                        </tr>

                                    <?php } ?>
                                </tbody>
                            </table>
                        </div>
                    </div>
                </div>
            </div>




        </div>

    </div>

    <div class="row mt-4">
        <div class="col-6"></div>
        <div class="col-6"></div>
    </div>
</div>