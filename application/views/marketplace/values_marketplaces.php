<div class="container-fluid my-3 py-3">
    <div class="row mb-5">
        <div class="col-lg-6 mt-lg-0">

            <div class="card" id="basic-info">
                <div class="card-header">
                    <h5>Recebimentos Marketplaces</h5>
                </div>
                <form id="filterForm" action="<?= base_url('insert-marketplace-receipt') ?>" method="POST">
                    <div class="card-body pt-0">
                        <div class="row">
                            <div class="col-8">
                                <div class="input-group input-group-static">
                                    <label>Data Pagamento</label>
                                    <input class="form-control datetimepicker font-16" type="text" name="date" data-input>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-8 align-self-center">
                                <label class="form-label mt-4 ms-0">Marketplace</label>
                                <select class="form-control" name="marketplace" id="choices-seller">
                                    <option value="">Selecione um marketplace</option>
                                    <?php foreach ($marketplaces as $marketplace) { ?>
                                        <option value="<?= $marketplace->id ?>"><?= $marketplace->id ?> - <?= $marketplace->name ?></option>
                                    <?php } ?>
                                </select>
                            </div>
                        </div>

                        <div class="row mt-4">
                            <div class="col-8">
                                <div class="input-group input-group-dynamic">
                                    <label class="form-label">Valor Recebido</label>
                                    <input class="multisteps-form__input form-control" type="text" name="value" id="moedaInput" />
                                </div>
                            </div>
                        </div>

                        <script>
                            // Seleciona o campo de entrada
                            const moedaInput = document.getElementById('moedaInput');

                            // Adiciona um ouvinte de evento para o evento 'input'
                            moedaInput.addEventListener('input', function() {
                                // Remove todos os caracteres não numéricos
                                let valor = this.value.replace(/\D/g, '');

                                // Formata o valor como uma moeda (XX.XX,XX)
                                valor = (parseFloat(valor) / 100).toLocaleString('pt-BR', {
                                    minimumFractionDigits: 2,
                                    maximumFractionDigits: 2
                                });

                                // Atualiza o valor no campo de entrada
                                this.value = valor;
                            });
                        </script>

                        <div class="row mt-4">
                            <div class="col-8">
                                <label class="form-label">Pagamento referente a ordens</label>
                                <textarea id="meuTextarea" name="ordens" rows="5" cols="60">  </textarea>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 align-self-center">
                            </div>
                            <div class="col-md-6 align-self-center button-filter">
                                <button type="submit" class="btn btn-primary btn-lg">Gravar Recebimento</button>
                            </div>
                        </div>

                    </div>
                </form>
            </div>
        </div>

        <div class="col-lg-6 mt-lg-0">
            <div class="card">
                <!-- Card header -->
                <div class="card-header">
                    <h5 class="mb-0">Recebimentos</h5>
                    <p class="text-sm mb-0">
                        Listagem dos recebimentos
                    </p>
                </div>
                <div class="table-responsive">
                    <table class="table table-flush" id="datatable-clients">
                        <thead class="thead-light">
                            <tr>
                                <th>Data Recebimento</th>
                                <th>Canal</th>
                                <th>Valor Recebido</th>
                                <th>Actions</th>
                            </tr>
                        </thead>
                        <tbody>
                            <?php foreach ($payments as $payment) { #var_dump($order->purchased);;
                            ?>
                                <tr>
                                    <td class="text-sm font-weight-normal"><?= date("d/m/Y", strtotime($payment->date_received)) ?></td>
                                    <td class="text-sm font-weight-normal"><?= $payment->cod_marketplace ?></td>
                                    <td class="text-sm font-weight-normal"><?= $payment->amount_received ?></td>


                                    <td class="text-sm font-weight-normal">
                                        <a class="link-modal-dados-order btn btn-danger btn-sm new-button" href="<?= base_url('cancel-receipt/' . $payment->id) ?>">Anular</a>
                                    </td>
                                </tr>
                            <?php } ?>
                        </tbody>
                    </table>
                </div>
            </div>
        </div>
    </div>

</div>