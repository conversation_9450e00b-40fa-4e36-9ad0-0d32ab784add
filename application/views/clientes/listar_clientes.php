<div class="container-fluid py-4">
	<div class="row mt-4">
		<div class="col-12">
			<div class="card">
				<!-- Card header -->
				<div class="card-header">
					<h5 class="mb-0">Listar Clientes</h5>
					<p class="text-sm mb-0">
						Abaixo a lista de todos os clientes ativos e por ativar.
					</p>
				</div>
				<div class="table-responsive">
					<table class="table table-flush" id="datatable-clients">
						<thead class="thead-light">
							<tr>
								<th>User ID</th>
								<th>IBAN</th>
								<th>Nome</th>
								<th>Email</th>
								<th>Data Cadastro</th>
								<th>Ativo</th>
								<th>Detalhes</th>
							</tr>
						</thead>
						<tbody>
							<?php foreach ($clients as $client) { #var_dump($client); die();
							?>
								<tr>
									<td class="text-sm font-weight-normal"><?php echo $client->id_client ?></td>
									<td class="text-sm font-weight-normal"><?php echo $client->bank_number ?? 'NO IBAN'  ?></td>
									<td class="text-sm font-weight-normal"><?php echo $client->name ?></td>
									<td class="text-sm font-weight-normal"><?php echo strtolower($client->email) ?></td>
									<td class="text-sm font-weight-normal"><?php echo date("d/m/Y", strtotime($client->create_at))  ?></td>
									<td class="text-sm font-weight-normal">

										<?php if ($client->user_active == 1) { ?>
											<span id='user_<?= $client->user_token ?>' class="badge-success userativo_<?= $client->user_token ?> badge-sm">Ativo</span>
											<a class="btn btn-primary btn-sm new-button" id="button_user_<?= $client->user_token ?>" onclick="updateStatusUser('<?= $client->user_token ?>', 'inactive')">Inativar</a>
										<?php } else { ?>
											<span id='user_<?= $client->user_token ?>' class="badge-sm badge-secondary userinativo_<?= $client->user_token ?>">Por ativar</span>
											<a class="btn btn-warning btn-sm new-button" id="button_user_<?= $client->user_token ?>" onclick="updateStatusUser('<?= $client->user_token ?>', 'active')">Ativar</a>
										<?php } ?>
									</td>

									<!-- <td>
										<div class="form-check form-switch ps-0 ms-auto my-auto">
											<?php if ($client->delivery_by == 'bighub') {
												$checked = 'checked';
												$action = 1;
											} else {
												$checked = '';
												$action = 0;
											} ?>
											<input class="form-check-input mt-1 ms-auto portes-checked-<?= $client->id_client ?>" <?= $checked ?> type="checkbox" onclick="checkPortes(<?php echo $client->id_client ?>, <?= $action ?>)">
										</div>
									</td> -->

									<td>

										<a class="link-modal-dados-cliente btn btn-info btn-sm new-button" onclick="setIdClient(<?= $client->id_client ?>)" href="<?= base_url() ?>client-details/<?php echo $client->id_client ?>" data-bs-toggle="modal" data-bs-target="#modal-dados-cliente">Detalhes</a>
										<?php if ($client->id_tbl_company != null) { ?>
											<span class="font-10">Iban</span>
										<?php } ?>
									</td>

									<!-- <td class="text-sm font-weight-normal">
										<a class="btn btn-info btn-sm new-button" data-bs-toggle="tooltip" data-bs-original-title="Impersonate" onclick="chamarCurlImpersonate('<?= $client->user_token ?>')">
											<i class="material-icons text-secondary position-relative text-lg color-icon-impersonate">visibility</i>
										</a>

										<a class="btn btn-dark btn-sm new-button" href="<?= base_url('customer-timelime?user=' . $client->id_client) ?>">Timeline</a>
									</td> -->

								</tr>
							<?php } ?>
						</tbody>
					</table>
				</div>
			</div>
		</div>
	</div>
</div>

<div class="modal fade" id="modal-dados-cliente" tabindex="-1" role="dialog" aria-labelledby="modal-default" aria-hidden="true">
	<div class="modal-dialog modal-lg modal-dialog-centered modal-" role="document">
		<div class="modal-content">
			<div class="modal-header">
				<h6 class="modal-title font-weight-normal" id="modal-title-default">Dados do Seller</h6>
				<button type="button" class="btn-close text-dark" data-bs-dismiss="modal" aria-label="Close">
					<span aria-hidden="true">×</span>
				</button>
			</div>
			<div class="modal-body">

			</div>

			<div class="modal-footer">
				<button type="button" class="btn btn-link  ml-auto" data-bs-dismiss="modal">Close</button>
			</div>
		</div>
	</div>

</div>