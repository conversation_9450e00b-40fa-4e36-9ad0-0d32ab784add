<div class="container-fluid">
	<div class="row">
		<?php //var_dump($seller); 
		?>
		<div class="card-header p-3 pb-0">
			<div class="row">
				<div class="col-md-7">
					<div class="d-flex justify-content-between align-items-center">
						<div class="w-100">



							<?php if ($seller->user_active == 1) { ?>
								<span class="badge-success badge-sm">Ativo</span>
							<?php } else { ?>
								<span class="badge-danger badge-sm">Inativo</span>
							<?php } ?>

							<h6 class="mt-3">Id Seller : <?= $seller->id_client ?> </h6>


							<hr class="color-hr">
							<h6>Nome : <?= $seller->name ?></h6>
							<h6>Email: <?= strtolower($seller->email) ?></h6>

							<h6>Fone: (<?= $seller->prefix_number ?>) <?= $seller->phone_number ?></h6>
							<h6>Morada: <?= $seller->address ?>, <?= $seller->number ?> - <?= $seller->complement ?></h6>
							<h6>Código Postal: <?= $seller->postal_code ?></h6>
							<h6>Distrito: <?= $seller->district ?> /  <?= $seller->parish ?> </h6>
							<h6>País: <?= $seller->country ?></h6>
							<hr class="color-hr">
							<h6>Nif: <?= $seller->nif ?></h6>
							<h6>Iban: <?= $seller->bank_number ?? 'No Iban' ?></h6>
							<hr class="color-hr">
							<h6>Cadastrado : <?= date("d/m/Y H:i:s", strtotime($seller->create_at)); ?> - Ultima atualização : <?= date("d/m/Y H:i:s", strtotime($seller->updated_at)); ?></h6>
						</div>
						<!-- <a href="javascript:;" class="btn bg-gradient-dark ms-auto mb-0">Invoice</a> -->
					</div>
				</div>
				<div class="col-md-5">

				<h6 class="mt-3">Gestão dos portes:</h6>
					<div class="card card-body border card-plain border-radius-lg d-flex align-items-center flex-row">
					<div class="spinner-border spinner-border-sm mx-3 esconder" id="spinner-delivery" role="status">
							<span class="sr-only">Loading...</span>
						</div>	
					<h6 class="mb-0" id="text-value-shipping-<?= $seller->id_client ?>"><?= $seller->delivery_by ?></h6>
						
						<div class="form-check form-switch ps-0 ms-auto my-auto">
							<?php if ($seller->delivery_by == 'bighub') {
								$checked = 'checked';
								$action = 1;
							} else {
								$checked = '';
								$action = 0;
							} ?>
							<input class="form-check-input mt-1 ms-auto portes-checked-<?= $seller->id_client ?>" <?= $checked ?> type="checkbox" onclick="checkPortes(<?php echo $seller->id_client ?>, <?= $action ?>)">
						</div>
					</div>

					<h6 class="mt-3">Total de Orders até a data <?= date("d/m/Y") ?> </h6>
					<div class="card card-body border card-plain border-radius-lg d-flex align-items-center flex-row">
						<!-- <img class="w-10 me-3 mb-0" src="<?= base_url() ?>assets/v02/img/logos/mastercard.png" alt="logo"> -->
						<h6 class="mb-0"> <?= $total_orders ?></h6>
					</div>

					<h6 class="mt-3">Ações:</h6>
					<div class="card card-body border card-plain border-radius-lg d-flex align-items-center flex-row">
						<a class="btn btn-info btn-sm new-button" data-bs-toggle="tooltip" data-bs-original-title="Impersonate" onclick="chamarCurlImpersonate('<?= $seller->user_token ?>')">
							Impersonate
						</a>
						<a class="btn btn-dark btn-sm new-button mx-3" href="<?= base_url('customer-timelime?user=' . $seller->id_client) ?>">Timeline</a>
						<?php if ($seller->id_tbl_company != null) { ?>
							<a class="btn btn-info btn-sm new-button" onclick="getDataIban('<?= $seller->user_token ?>', '<?= $seller->id_tbl_company ?>')">Comprovativo Iban</a>
						<?php } ?>
					</div>

				</div>
			</div>
		</div>
	</div>
</div>