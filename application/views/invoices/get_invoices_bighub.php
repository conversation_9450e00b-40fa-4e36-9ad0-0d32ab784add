<div class="container-fluid my-3 py-3">
    <div class="row mb-5">
        <div class="col-lg-9 mt-lg-0">

            <div class="card mt-4" id="basic-info">
                <div class="card-header">
                    <h5>Filtrar ordens para gerar fatura Moloni</h5>
                </div>
                <form id="filterForm" action="<?= base_url('filter-invoices-bighub')?>" method="GET">
                    <div class="card-body pt-0">
                        <div class="row">
                            <div class="col-6">
                                <div class="input-group input-group-static">
                                    <label>Data Inicial</label>
                                    <input class="form-control datetimepicker font-16" type="text" name="ini_date" data-input>
                                </div>
                            </div>
                            <div class="col-6">
                                <div class="input-group input-group-static">
                                    <label>Data Final</label>
                                    <input class="form-control datetimepicker font-16" type="text" name="end_date" data-input>
                                </div>
                            </div>
                        </div>

                        <div class="row">
                            <div class="col-md-6 align-self-center">
                            </div>
                            <div class="col-md-6 align-self-center button-filter">
                                <button type="submit" class="btn btn-primary btn-lg">Filtrar Vendas</button>
                            </div>
                        </div>
                    </div>
                </form>
            </div>
        </div>

        <div class="col-lg-3"></div>
    </div>
</div>

<script>
    function alert_filter(value){
        const alert_filter = document.getElementById('alert-filter');
    
        if(value === 1){
            alert_filter.classList.remove('esconder');
            changeOnClickFunction('alert_filter(0)')
        }else{
            alert_filter.classList.add('esconder');
            changeOnClickFunction('alert_filter(1)')
        }  
    }

    function changeOnClickFunction(newfunction) {
        var checkbox = document.getElementById("flexSwitchCheckDefault");
        checkbox.setAttribute("onclick", newfunction);
    }
</script>