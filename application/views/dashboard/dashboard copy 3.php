<div class="container-fluid py-4">

  <!-- Cards de Resumo -->
  <div class="row">
    <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
      <div class="card">
        <div class="card-header p-3 pt-2">
          <div class="icon icon-lg icon-shape bg-gradient-primary shadow-primary text-center border-radius-xl mt-n4 position-absolute">
            <i class="material-icons opacity-10">shopping_cart</i>
          </div>
          <div class="text-end pt-1">
            <p class="text-sm mb-0 text-capitalize">Vendas Hoje</p>
            <h4 class="mb-0"><span class="font-euro-symbol">€</span><?= $totalSalesDay ?></h4>
          </div>
        </div>
        <hr class="dark horizontal my-0">
        <div class="card-footer p-3">
          <p class="mb-0"><span class="text-success text-sm font-weight-bolder"><?= $quantity_sales_day ?></span> pedidos hoje</p>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
      <div class="card">
        <div class="card-header p-3 pt-2">
          <div class="icon icon-lg icon-shape bg-gradient-warning shadow-warning text-center border-radius-xl mt-n4 position-absolute">
            <i class="material-icons opacity-10">payments</i>
          </div>
          <div class="text-end pt-1">
            <p class="text-sm mb-0 text-capitalize">Comissões Hoje</p>
            <h4 class="mb-0"><span class="font-euro-symbol">€</span><?= $totalCommissionDay ?></h4>
          </div>
        </div>
        <hr class="dark horizontal my-0">
        <div class="card-footer p-3">
          <p class="mb-0">Comissões dos marketplaces</p>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-sm-6 mb-xl-0 mb-4">
      <div class="card">
        <div class="card-header p-3 pt-2">
          <div class="icon icon-lg icon-shape bg-gradient-primary shadow-primary text-center border-radius-xl mt-n4 position-absolute">
            <i class="material-icons opacity-10">shopping_cart</i>
          </div>
          <div class="text-end pt-1">
            <p class="text-sm mb-0 text-capitalize">Vendas do Mês</p>
            <h4 class="mb-0"><span class="font-euro-symbol">€</span><?= $totalSalesMonth ?></h4>
          </div>
        </div>
        <hr class="dark horizontal my-0">
        <div class="card-footer p-3">
          <p class="mb-0"><span class="text-success text-sm font-weight-bolder"><?= $quantity_sales_month ?></span> pedidos este mês</p>
        </div>
      </div>
    </div>
    <div class="col-xl-3 col-sm-6">
      <div class="card">
        <div class="card-header p-3 pt-2">
          <div class="icon icon-lg icon-shape bg-gradient-warning shadow-warning text-center border-radius-xl mt-n4 position-absolute">
            <i class="material-icons opacity-10">payments</i>
          </div>
          <div class="text-end pt-1">
            <p class="text-sm mb-0 text-capitalize">Comissões do Mês</p>
            <h4 class="mb-0"><span class="font-euro-symbol">€</span><?= $totalCommissionMonth ?></h4>
          </div>
        </div>
        <hr class="dark horizontal my-0">
        <div class="card-footer p-3">
          <p class="mb-0">Total de comissões do mês</p>
        </div>
      </div>
    </div>
  </div>

  <div class="row mt-4">
    <div class="col-lg-6 mt-4 mt-lg-0">
      <div class="card">
        <div class="card-header pb-0 p-3">
          <div class="d-flex align-items-center">
            <h6 class="mb-0">Vendas Marketplaces - HOJE - <?= date_mysql_to_human($data_dia) ?></h6>
            <button type="button" class="btn btn-icon-only btn-rounded btn-outline-secondary mb-0 ms-2 btn-sm d-flex align-items-center justify-content-center ms-auto" data-bs-toggle="tooltip" data-bs-placement="bottom" title="See the consumption per room">
              <i class="fas fa-info"></i>
            </button>
          </div>
        </div>
        <div class="card-body p-3">
          <div class="row">
            <div class="col-6">
              <div class="table-responsive">
                <table class="table align-items-center mb-0">
                  <thead>
                    <tr>
                      <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Marketplace</th>
                      <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2 text-end">Valor</th>
                    </tr>
                  </thead>
                  <tbody>



                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"> <?= $manomano_es_day['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Manomano</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($manomano_es_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"> <?= $bulevip_es_day['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Bulevipe ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($bulevip_es_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"> <?= $pixmania_es_day['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Pixmania ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($pixmania_es_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"> <?= $pixmania_fr_day['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Pixmania FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($pixmania_fr_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3 elcorteringles-color"> <?= $elcorteingles_es_day['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">ElCorteIngl</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($elcorteingles_es_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"> <?= $rakuten_fr_day['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Rakuten FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($rakuten_fr_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"> <?= $empik_pl_day['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Empik PL</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($empik_pl_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"> <?= $rueducommerce_fr_day['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Rueducomm FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($rueducommerce_fr_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"> <?= $perfumesclub_pt_day['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Perf.Club PT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($perfumesclub_pt_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"> <?= $bigbang_si_day['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">BigBang SI</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($bigbang_si_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"><?= $eleclerc_fr_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">E.Leclerc FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($eleclerc_fr_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"><?= $backmarket_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Backmarket All</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($backmarket_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $clubefashion_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Clube Fashion PT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($clubefashion_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"><?= $aliexpress_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">AliExpress</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($aliexpress_day['total_price']) ?> </span>
                      </td>
                    </tr>



                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $conrad_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Conrad</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($conrad_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $venteunique_pt_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Vente Unique PT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($venteunique_pt_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $venteunique_es_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Vente Unique ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($venteunique_es_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $venteunique_be_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Vente Unique BE</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($venteunique_be_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $venteunique_de_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Vente Unique DE</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($venteunique_de_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $venteunique_fr_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Vente Unique FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($venteunique_fr_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $venteunique_it_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Vente Unique IT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($venteunique_it_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $venteunique_nl_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Vente Unique NL</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($venteunique_nl_day['total_price']) ?> </span>
                      </td>
                    </tr>


                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $macway_fr_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Macway FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($macway_fr_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $tiendanimal_es_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Tienda Animal ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($tiendanimal_es_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $stockly_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Stockly</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($stockly_day['total_price']) ?> </span>
                      </td>
                    </tr>


                  </tbody>
                </table>
              </div>
            </div>


            <div class="col-6">
              <div class="table-responsive">
                <table class="table align-items-center mb-0">
                  <thead>
                    <tr>
                      <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Marketplace</th>
                      <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2 text-end">Valor</th>
                    </tr>
                  </thead>
                  <tbody>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"> <?= $worten_pt_day['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Worten PT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($worten_pt_day['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"> <?= $worten_es_day['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Worten ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($worten_es_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"> <?= $worten_pt_day_2['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Worten PT - A2</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($worten_pt_day_2['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"> <?= $worten_es_day_2['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Worten ES - A2</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($worten_es_day_2['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"> <?= $carrefour_es_day['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Carrefour ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($carrefour_es_day['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"> <?= $carrefour_es_day_2['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Carrefour ES - A2</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($carrefour_es_day_2['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"> <?= $carrefour_fr_day['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Carrefour FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($carrefour_fr_day['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"> <?= $mediamarkt_es_day['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Media Market ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($mediamarkt_es_day['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"> <?= $mediamarkt_es_day_2['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Media Market ES - A2</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($mediamarkt_es_day_2['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"> <?= $mediamarkt_de_day['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Media Market DE</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($mediamarkt_de_day['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"><?= $fnac_pt_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Fnac PT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($fnac_pt_day['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"><?= $fnac_es_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Fnac ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($fnac_es_day['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"><?= $fnac_fr_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Fnac FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($fnac_fr_day['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $eprice_it_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Eprice</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($eprice_it_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"><?= $pccomp_pt_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">PC Componente PT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($pccomp_pt_day['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"><?= $pccomp_es_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">PC Componente ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($pccomp_es_day['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"><?= $pccomp_it_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">PC Componente IT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($pccomp_it_day['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"><?= $pccomp_fr_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">PC Componente FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($pccomp_fr_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $makro_pt_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Makro PT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($makro_pt_day['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $makro_es_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Makro ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($makro_es_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $leroy_es_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Leroy Merlin ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($leroy_es_day['total_price']) ?> </span>
                      </td>
                    </tr>


                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $phonehouse_es_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Phone House ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($phonehouse_es_day['total_price']) ?> </span>
                      </td>
                    </tr>


                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $cdiscount_fr_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Cdiscount FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($cdiscount_fr_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $cdiscount_fr_day_2['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Cdiscount FR - A2</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($cdiscount_fr_day_2['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $miravia_es_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Miravia ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($miravia_es_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $kaufland_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Kaufland DE</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($kaufland_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $planetahuerto_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Planeta Huerto ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($planetahuerto_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $quirumedes_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Quirumedes ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($quirumedes_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $shopapotheke_de_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Shop Apotheke DE</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($shopapotheke_de_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $shopapotheke_at_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Shop Apotheke AT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($shopapotheke_at_day['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $conforama_day['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Conforama FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($conforama_day['total_price']) ?> </span>
                      </td>
                    </tr>

                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>

    <div class="col-lg-6 mt-4 mt-lg-0">
      <div class="card">
        <div class="card-header pb-0 p-3">
          <div class="d-flex align-items-center">
            <h6 class="mb-0">Vendas Marketplaces do MÊS - ( <?= date('m') ?> )</h6>
            <button type="button" class="btn btn-icon-only btn-rounded btn-outline-secondary mb-0 ms-2 btn-sm d-flex align-items-center justify-content-center ms-auto" data-bs-toggle="tooltip" data-bs-placement="bottom" title="See the consumption per room">
              <i class="fas fa-info"></i>
            </button>
          </div>
        </div>
        <div class="card-body p-3">
          <div class="row">
            <div class="col-6 mt-4">
              <div class="table-responsive">
                <table class="table align-items-center mb-0">
                  <thead>
                    <tr>
                      <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7">Marketplace</th>
                      <th class="text-uppercase text-secondary text-xxs font-weight-bolder opacity-7 ps-2 text-end">Valor</th>
                    </tr>
                  </thead>
                  <tbody>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"> <?= $manomano_es_month['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Manomano</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($manomano_es_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"> <?= $bulevip_es_month['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Bulevipe ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($bulevip_es_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"> <?= $pixmania_es_month['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Pixmania ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($pixmania_es_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"> <?= $pixmania_fr_month['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Pixmania FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($pixmania_fr_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3 elcorteringles-color"> <?= $elcorteingles_es_month['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">El Corte Inglés</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($elcorteingles_es_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"> <?= $rakuten_fr_month['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Rakuten FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($rakuten_fr_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"> <?= $empik_pl_month['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Empik PL</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($empik_pl_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"> <?= $rueducommerce_fr_month['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Rueducomm. FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($rueducommerce_fr_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"> <?= $perfumesclub_pt_month['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Perf.Club PT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($perfumesclub_pt_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"> <?= $bigbang_si_month['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">BigBang SI</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($bigbang_si_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"><?= $eleclerc_fr_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">E.Leclerc FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($eleclerc_fr_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"><?= $backmarket_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Backmarket All</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($backmarket_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $clubefashion_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Clube Fashion PT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($clubefashion_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"><?= $aliexpress_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">AliExpress</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($aliexpress_month['total_price']) ?> </span>
                      </td>
                    </tr>


                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $conrad_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Conrad</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($conrad_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $venteunique_pt_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Vente Unique PT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($venteunique_pt_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $venteunique_es_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Vente Unique ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($venteunique_es_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $venteunique_be_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Vente Unique BE</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($venteunique_be_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $venteunique_de_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Vente Unique DE</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($venteunique_de_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $venteunique_fr_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Vente Unique FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($venteunique_fr_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $venteunique_it_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Vente Unique IT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($venteunique_it_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $venteunique_nl_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Vente Unique NL</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($venteunique_nl_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $macway_fr_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Macway FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($macway_fr_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $tiendanimal_es_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Tienda Animal ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($tiendanimal_es_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $stockly_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Stockly</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($stockly_month['total_price']) ?> </span>
                      </td>
                    </tr>

                  </tbody>
                </table>
              </div>
            </div>
            <div class="col-6">
              <div class="table-responsive">
                <table class="table align-items-center mb-0">
                  <tbody>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"> <?= $worten_pt_month['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Worten PT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($worten_pt_month['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"> <?= $worten_es_month['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Worten ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($worten_es_month['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"> <?= $worten_pt_month_2['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Worten PT - A2</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($worten_pt_month_2['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"> <?= $worten_es_month_2['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Worten ES - A2</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($worten_es_month_2['total_price']) ?> </span>
                      </td>
                    </tr>


                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $carrefour_es_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Carrefour ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($carrefour_es_month['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $carrefour_es_month_2['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Carrefour ES - A2</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($carrefour_es_month_2['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $carrefour_fr_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Carrefour FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($carrefour_fr_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $mediamarkt_es_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Media Market ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($mediamarkt_es_month['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $mediamarkt_es_month_2['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Media Market ES - A2</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($mediamarkt_es_month_2['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $mediamarkt_de_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Media Market DE</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($mediamarkt_de_month['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"> <?= $fnac_pt_month['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Fnac PT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($fnac_pt_month['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"> <?= $fnac_es_month['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Fnac ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($fnac_es_month['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"> <?= $fnac_fr_month['count'] ?></span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Fnac FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($fnac_fr_month['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $eprice_it_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Eprice</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($eprice_it_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"><?= $pccomp_pt_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">PC Componente PT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($pccomp_pt_month['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"><?= $pccomp_es_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">PC Componente ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($pccomp_es_month['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"><?= $pccomp_it_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">PC Componente IT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($pccomp_it_month['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-warning me-3"><?= $pccomp_fr_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">PC Componente FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($pccomp_fr_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $makro_pt_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Makro PT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($makro_pt_month['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $makro_es_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Makro ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($makro_es_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $leroy_es_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Leroy Merlin ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($leroy_es_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $phonehouse_es_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Phone House ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($phonehouse_es_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $cdiscount_fr_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Cdiscount FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($cdiscount_fr_month['total_price']) ?> </span>
                      </td>
                    </tr>
                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $cdiscount_fr_month_2['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Cdiscount FR - A2</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($cdiscount_fr_month_2['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-info me-3"><?= $miravia_es_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Miravia ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($miravia_es_month['total_price']) ?> </span>
                      </td>
                    </tr>


                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $kaufland_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Kaufland DE</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($kaufland_month['total_price']) ?> </span>
                      </td>
                    </tr>


                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $planetahuerto_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Planeta Huerto ES</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($planetahuerto_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $quirumedes_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Quirumedes</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($quirumedes_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $shopapotheke_de_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Shop Apotheke DE</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($shopapotheke_de_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $shopapotheke_at_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Shop Apotheke AT</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($shopapotheke_at_month['total_price']) ?> </span>
                      </td>
                    </tr>

                    <tr>
                      <td>
                        <div class="d-flex px-2 py-0">
                          <span class="badge bg-gradient-danger me-3"><?= $conforama_month['count'] ?> </span>
                          <div class="d-flex flex-column justify-content-center">
                            <h6 class="mb-0 text-sm">Conforama FR</h6>
                          </div>
                        </div>
                      </td>
                      <td class="align-middle rigth-important text-sm">
                        <span class="text-xs font-info-dashboard"> <?= FormatarValor($conforama_month['total_price']) ?> </span>
                      </td>
                    </tr>

                  </tbody>
                </table>
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  </div>


  <div class="row mt-4">
    <div class="col-6">
      <div class="card mb-4">
        <div class="card-header pb-0">
          <h6>Vendedores / Valores - HOJE - <?= date_mysql_to_human($data_dia) ?></h6>
        </div>
        <div class="card-body px-0 pt-0 pb-2">
          <div class="table-responsive p-0">
            <table class="table align-items-center mb-0">
              <thead>
                <tr>
                  <th>Seller</th>
                  <th>Orders</th>
                  <th>Total</th>
                  <th>Channels</th>
                </tr>
              </thead>
              <tbody>
                <?php foreach ($bandages as $bandage) { #var_dump($order); die();
                ?>
                  <tr>
                    <td>
                      <div class="d-flex px-3 py-1">
                        <p class="text-sm font-weight-normal mb-0"><?= $bandage->user_id ?></p>
                      </div>
                    </td>
                    <td>
                      <p class="text-sm font-weight-normal mb-0"><?= $bandage->total_orders ?> </p>
                    </td>
                    <td>
                      <p class="text-sm font-weight-normal mb-0">€ <?= $bandage->total_sales ?> </p>
                    </td>
                    <td>
                      <p class="text-sm font-weight-normal mb-0"><?= $bandage->channels ?></p>
                    </td>
                  </tr>
                <?php } ?>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
    <div class="col-6">
      <div class="card mb-4">
        <div class="card-header pb-0">
          <h6>Ultimás 5 vendas</h6>
        </div>
        <div class="card-body px-0 pt-0 pb-2">
          <div class="table-responsive p-0">
            <table class="table align-items-center mb-0">
              <thead>
                <tr>
                  <th>Data/Hora</th>
                  <th>Channel</th>
                  <th>OrderID</th>
                  <th>Seller</th>
                  <th>Valor</th>
                </tr>
              </thead>
              <tbody>
                <?php foreach ($orders as $order) { #var_dump($order); die();
                ?>
                  <tr>
                    <td>
                      <div class="d-flex px-3 py-1">
                        <p class="text-sm font-weight-normal mb-0"><?php echo date("d/m/Y H:i", strtotime($order->created_date)) ?></p>
                      </div>
                    </td>

                    <td>
                      <p class="text-sm font-weight-normal mb-0"><b><?php echo $order->channel ?>/<?php echo $order->country ?></b></p>
                    </td>
                    <td>
                      <p class="text-sm font-weight-normal mb-0"><?php echo $order->reference ?></p>
                    </td>
                    <td>
                      <p class="text-sm font-weight-normal mb-0"><?php echo $order->user_id ?></p>
                    </td>
                    <td>
                      <p class="text-sm font-weight-normal mb-0">€ <?php echo $order->total_price ?> </p>
                    </td>
                  </tr>
                <?php } ?>
              </tbody>
            </table>
          </div>
        </div>
      </div>
    </div>
  </div>
</div>