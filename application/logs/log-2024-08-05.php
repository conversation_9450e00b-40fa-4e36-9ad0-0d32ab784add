<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2024-08-05 14:45:59 --> Severity: error --> Exception: Cannot use object of type stdClass as array /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 132
ERROR - 2024-08-05 14:57:47 --> Severity: error --> Exception: Cannot use object of type stdClass as array /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 112
ERROR - 2024-08-05 14:58:15 --> Severity: error --> Exception: Cannot use object of type stdClass as array /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 112
ERROR - 2024-08-05 14:58:57 --> Severity: error --> Exception: Cannot use object of type stdClass as array /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 112
ERROR - 2024-08-05 14:59:39 --> Severity: error --> Exception: Cannot use object of type stdClass as array /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 112
ERROR - 2024-08-05 15:00:58 --> Severity: error --> Exception: Cannot use object of type stdClass as array /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 112
ERROR - 2024-08-05 15:05:42 --> Severity: Warning --> array_map() expects parameter 1 to be a valid callback, function 'convertObjectToArray' not found or invalid function name /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 288
ERROR - 2024-08-05 15:05:53 --> Severity: Warning --> array_map() expects parameter 1 to be a valid callback, function 'convertObjectToArray' not found or invalid function name /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 288
ERROR - 2024-08-05 15:06:02 --> Severity: Warning --> array_map() expects parameter 1 to be a valid callback, function 'convertObjectToArray' not found or invalid function name /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 288
ERROR - 2024-08-05 15:06:13 --> Severity: Warning --> array_map() expects parameter 1 to be a valid callback, function 'convertObjectToArray' not found or invalid function name /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 288
ERROR - 2024-08-05 15:06:23 --> Severity: Warning --> array_map() expects parameter 1 to be a valid callback, function 'convertObjectToArray' not found or invalid function name /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 288
ERROR - 2024-08-05 15:06:31 --> Severity: Warning --> array_map() expects parameter 1 to be a valid callback, function 'convertObjectToArray' not found or invalid function name /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 288
ERROR - 2024-08-05 15:06:40 --> Severity: Warning --> array_map() expects parameter 1 to be a valid callback, function 'convertObjectToArray' not found or invalid function name /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 288
ERROR - 2024-08-05 15:09:26 --> Severity: Warning --> array_map() expects parameter 1 to be a valid callback, function 'convertObjectToArray' not found or invalid function name /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 287
ERROR - 2024-08-05 15:15:14 --> Severity: error --> Exception: Call to undefined function convertObjectToArray() /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 288
ERROR - 2024-08-05 15:15:55 --> Severity: Warning --> array_map() expects parameter 1 to be a valid callback, function 'convertObjectToArray' not found or invalid function name /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 287
