<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2025-04-11 14:27:43 --> Severity: error --> Exception: Too few arguments to function Invoices::generate_invoice(), 0 passed in /home/<USER>/Projects/BigHub/thor/system/core/CodeIgniter.php on line 532 and exactly 1 expected /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 107
ERROR - 2025-04-11 14:35:40 --> Severity: error --> Exception: Too few arguments to function Invoices::generate_invoice(), 0 passed in /home/<USER>/Projects/BigHub/thor/system/core/CodeIgniter.php on line 532 and exactly 1 expected /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 107
ERROR - 2025-04-11 14:37:11 --> Severity: error --> Exception: Too few arguments to function Invoices::generate_invoice(), 0 passed in /home/<USER>/Projects/BigHub/thor/system/core/CodeIgniter.php on line 532 and exactly 1 expected /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 107
ERROR - 2025-04-11 14:39:24 --> Severity: error --> Exception: Too few arguments to function Invoices::generate_invoice(), 0 passed in /home/<USER>/Projects/BigHub/thor/system/core/CodeIgniter.php on line 532 and exactly 1 expected /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 107
ERROR - 2025-04-11 14:46:37 --> Severity: error --> Exception: Call to undefined method Invoices::_getDataDashboard() /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 109
ERROR - 2025-04-11 14:54:38 --> Severity: Warning --> array_map() expects parameter 1 to be a valid callback, function 'convertObjectToArray' not found or invalid function name /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 363
