<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2024-07-24 12:30:32 --> Severity: Notice --> Undefined variable: orders /home/<USER>/Projects/BigHub/thor/application/views/orders/list_orders.php 30
ERROR - 2024-07-24 12:30:32 --> Severity: Warning --> Invalid argument supplied for foreach() /home/<USER>/Projects/BigHub/thor/application/views/orders/list_orders.php 30
ERROR - 2024-07-24 12:40:38 --> Severity: error --> Exception: Call to undefined method Orders::getStateClass() /home/<USER>/Projects/BigHub/thor/application/controllers/Orders.php 423
ERROR - 2024-07-24 12:40:59 --> Severity: error --> Exception: Call to undefined method Orders::getStateClass() /home/<USER>/Projects/BigHub/thor/application/controllers/Orders.php 423
ERROR - 2024-07-24 12:43:40 --> Severity: error --> Exception: Call to undefined method Orders::getStateClass() /home/<USER>/Projects/BigHub/thor/application/controllers/Orders.php 423
ERROR - 2024-07-24 12:44:28 --> Severity: error --> Exception: Call to undefined method Orders::getStateClass() /home/<USER>/Projects/BigHub/thor/application/controllers/Orders.php 424
ERROR - 2024-07-24 13:33:31 --> Severity: Notice --> Undefined property: stdClass::$client_name /home/<USER>/Projects/BigHub/thor/application/controllers/Orders.php 445
ERROR - 2024-07-24 13:33:31 --> Severity: Notice --> Undefined property: stdClass::$client_name /home/<USER>/Projects/BigHub/thor/application/controllers/Orders.php 445
ERROR - 2024-07-24 13:33:31 --> Severity: Notice --> Undefined property: stdClass::$client_name /home/<USER>/Projects/BigHub/thor/application/controllers/Orders.php 445
ERROR - 2024-07-24 13:33:31 --> Severity: Notice --> Undefined property: stdClass::$client_name /home/<USER>/Projects/BigHub/thor/application/controllers/Orders.php 445
ERROR - 2024-07-24 13:33:31 --> Severity: Notice --> Undefined property: stdClass::$client_name /home/<USER>/Projects/BigHub/thor/application/controllers/Orders.php 445
ERROR - 2024-07-24 13:33:31 --> Severity: Notice --> Undefined property: stdClass::$client_name /home/<USER>/Projects/BigHub/thor/application/controllers/Orders.php 445
ERROR - 2024-07-24 13:33:31 --> Severity: Notice --> Undefined property: stdClass::$client_name /home/<USER>/Projects/BigHub/thor/application/controllers/Orders.php 445
ERROR - 2024-07-24 13:33:31 --> Severity: Notice --> Undefined property: stdClass::$client_name /home/<USER>/Projects/BigHub/thor/application/controllers/Orders.php 445
ERROR - 2024-07-24 13:33:31 --> Severity: Notice --> Undefined property: stdClass::$client_name /home/<USER>/Projects/BigHub/thor/application/controllers/Orders.php 445
ERROR - 2024-07-24 13:33:31 --> Severity: Notice --> Undefined property: stdClass::$client_name /home/<USER>/Projects/BigHub/thor/application/controllers/Orders.php 445
ERROR - 2024-07-24 13:45:24 --> Query error: Unknown column 'ord.client_name' in 'field list' - Invalid query: SELECT `ord`.`id`, `ord`.`reference`, `ord`.`client_name`, `ord`.`created_date`, `ord`.`items`, `ord`.`channel`, `ord`.`country`, `ord`.`user_id`, `ord`.`state`, `ord`.`total_price`, IFNULL(inv.id, 0) as isinvoice, `inv`.`id_invoice`
FROM `tbl_orders` `ord`
LEFT JOIN `tbl_invoices` `inv` ON `ord`.`id` = `inv`.`order_id`
ORDER BY `ord`.`id` DESC, `id` ASC
 LIMIT 10
ERROR - 2024-07-24 13:46:11 --> Query error: Unknown column 'ord.client_name' in 'field list' - Invalid query: SELECT `ord`.`id`, `ord`.`reference`, `ord`.`client_name`, `ord`.`created_date`, `ord`.`items`, `ord`.`channel`, `ord`.`country`, `ord`.`user_id`, `ord`.`state`, `ord`.`total_price`, IFNULL(inv.id, 0) as isinvoice, `inv`.`id_invoice`
FROM `tbl_orders` `ord`
LEFT JOIN `tbl_invoices` `inv` ON `ord`.`id` = `inv`.`order_id`
ORDER BY `ord`.`id` DESC, `id` ASC
 LIMIT 10
