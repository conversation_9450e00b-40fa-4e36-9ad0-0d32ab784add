<?php defined('BASEPATH') OR exit('No direct script access allowed'); ?>

ERROR - 2024-09-16 12:16:22 --> Severity: Warning --> array_map() expects parameter 1 to be a valid callback, function 'convertObjectToArray' not found or invalid function name /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 323
ERROR - 2024-09-16 12:16:31 --> Severity: Warning --> array_map() expects parameter 1 to be a valid callback, function 'convertObjectToArray' not found or invalid function name /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 323
ERROR - 2024-09-16 12:16:40 --> Severity: Warning --> array_map() expects parameter 1 to be a valid callback, function 'convertObjectToArray' not found or invalid function name /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 323
ERROR - 2024-09-16 12:16:49 --> Severity: Warning --> array_map() expects parameter 1 to be a valid callback, function 'convertObjectToArray' not found or invalid function name /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 323
ERROR - 2024-09-16 12:16:57 --> Severity: Warning --> array_map() expects parameter 1 to be a valid callback, function 'convertObjectToArray' not found or invalid function name /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 323
ERROR - 2024-09-16 12:17:06 --> Severity: Warning --> array_map() expects parameter 1 to be a valid callback, function 'convertObjectToArray' not found or invalid function name /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 323
ERROR - 2024-09-16 12:17:14 --> Severity: Warning --> array_map() expects parameter 1 to be a valid callback, function 'convertObjectToArray' not found or invalid function name /home/<USER>/Projects/BigHub/thor/application/controllers/Invoices.php 323
