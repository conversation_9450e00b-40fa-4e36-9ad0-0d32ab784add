<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class Payments extends MY_Controller {

	public function __construct()
	{
		parent::__construct();
	}

	
	public function pay_the_seller()
	{
		$data = $this->_getDataDashboard();	

		$data_user = $this->_getDataDashboard();

		$http_post = file_get_contents('php://input');

        if (isset($http_post) && !empty($http_post)) {

            $info_order = json_decode($http_post,  true);

			# Gravar na tabela tbl_seller_payment_summary todos os dados da remessa
			$orders_id = $info_order['ids'];

			$data_orders = [
				'id_user_thor' => $data_user['user_id_thor'],
				'orders_id' => json_encode($orders_id),
				'quantity_orders' => count($orders_id),
				'id_seller' => $info_order['seller'],
				'ini_date' => date_human_to_mysql($info_order['ini_date']),
				'end_date' => date_human_to_mysql($info_order['end_date']),
				'total_orders' => $info_order['total_orders'],
				'total_paid' => $info_order['total_pago'],
				'total_pending' => $info_order['total_orders'] - $info_order['total_pago'],
				'obs_paid' => $info_order['obs_remessa'],
				'pay_day' => date('Y-m-d')
			];

			if($this->db->insert('tbl_seller_payment_summary', $data_orders)){
				$lastInsertedId = $this->db->insert_id();
				# Depois da gravação marcar mtodos as ordens como pagas e suas respectivas data	
				foreach ($orders_id as $key => $order){

					$data_paid = [
						'paid' => 1,
						'order_guid' => $order,
						'payment_date' => date('Y-m-d'),
						'payment_obs' => 'Pago em remessa de orders',
						'seller_payment_summary_id' => $lastInsertedId
					];

					$this->db->insert('tbl_orders_payment', $data_paid);
				}

				$resp = [
					'msg' => 'orders save success',
					'status' => 'success'
				];
				echo json_encode($resp);
			} else {
                $resp = [
					'msg' => 'orders save error',
					'status' => 'error'
				];
				echo json_encode($resp);
            }
		}	
	}
	
}
