<?php

defined('BASEPATH') OR exit('No direct script access allowed');

use BaconQrCode\Renderer\ImageRenderer;
use BaconQrCode\Renderer\Image\SvgImageBackEnd;
use BaconQrCode\Renderer\RendererStyle\RendererStyle;
use BaconQrCode\Writer;

# REFERENCIAS PARA USAR A BIBLIOTECA
# https://php.com.br/52?codigos-qrcode-com-bacon


class Qrcode extends MY_Controller {

	public function __construct() {
		parent::__construct();
	}

	public function index() {

		$data = $this->_getDataDashboard();

		$renderer = new ImageRenderer(
			new RendererStyle(400),
			new SvgImageBackEnd()
		);

		$writer = new Writer($renderer);

		$cod_qrcode = '2@l5Yid92upc5IPl/avJKsUGebo6WcGpz/VCDTNTPaFYvt7fYPejazDuSi2wxnCmT9GzvXthIH/45ytw==,SyGBBY68YlQq/UWrVsoJ7oYW/Y2oK8bTwjxfi0ampUE=,46mefYINN0TCzXA+uointUw6iqaLLKUKV/EV9vGdH0Q=,BxW/z1czFZ/W8L32cyT9mfrL3BTR3BLPvWrHfwgYIEw=,1';
		//Exibe o QR code na tela
		#$writer->writeFile($cod_qrcode, 'qrcode.png');

		$data['qrcode_img'] = $writer->writeString($cod_qrcode);

		$data['view'] = 'qrcode/gerar';
		$this->load->view('includes/template', $data);
    }



}
