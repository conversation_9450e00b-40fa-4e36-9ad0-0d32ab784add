<?php
defined('BASEPATH') or exit('No direct script access allowed');

class <PERSON>loni extends CI_Controller
{
    public function __construct()
    {
        parent::__construct();
        $this->load->model("Moloni_model");

        $this->clientId = 'bighub';
        $this->clientSecret = '486b0698-b704-4534-82c6-b8eb76ca985f';
        if (base_url() == 'https://prd-mkp.bighub.store/') {
            $this->urlAuthorize = 'https://prd-mkp.bighub.store/moloni/authorize';
        } else {
            $this->urlAuthorize = 'https://pre-mkp.bighub.store/moloni/authorize';
        }
    }
    public function index()
    {
        $response = array("status" => "invalid_request");
        echo json_encode($response);
    }
    public function authorize()
    {
        if ($_GET) {
            $code = $this->input->get('code', TRUE);

            $curl = curl_init();

            curl_setopt_array($curl, [
                CURLOPT_URL => "https://api.moloni.es/v1/auth/grant",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => "grantType=authorization_code&apiClientId=$this->clientId&clientSecret=$this->clientSecret&code=$code",
                CURLOPT_HTTPHEADER => [
                    "Content-Type: application/x-www-form-urlencoded"
                ],
            ]);

            $response = curl_exec($curl);
            $json = json_decode($response, true);
            $err = curl_error($curl);

            curl_close($curl);

            if ($err) {
                $response = array("error" => $err);
                echo json_encode($response);
            } else {
                $data['accessToken'] = $json['accessToken'];
                $data['accessTokenExpiracy'] =  $json['accessTokenExpiracy'];
                $data['refreshToken'] =  $json['refreshToken'];
                $data['refreshTokenExpiracy'] =  $json['refreshTokenExpiracy'];
                $this->Moloni_model->new('moloni', $data);
                echo "success";
            }
        } else {
            $link = "https://api.moloni.es/v1/auth/authorize?apiClientId=$this->clientId&redirectUri=" . urlencode($this->urlAuthorize);
            echo "<a href='$link'>Generate</a>";
        }
    }
    private function token()
    {
        $code = @$this->Moloni_model->checkToken();

        if (empty($code)) {
            return 'expired_authorization';
        } else {
            $curl = curl_init();

            curl_setopt_array($curl, [
                CURLOPT_URL => "https://api.moloni.es/v1/auth/grant",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => "grantType=refresh_token&refreshToken=$code&apiClientId=$this->clientId&clientSecret=$this->clientSecret",
                CURLOPT_HTTPHEADER => [
                    "Content-Type: application/x-www-form-urlencoded"
                ],
            ]);

            $response = curl_exec($curl);
            $json = json_decode($response, true);
            $err = curl_error($curl);

            curl_close($curl);

            if ($err) {
                return 'expired_authorization';
            } else {
                $data['accessToken'] = @$json['accessToken'];
                $data['accessTokenExpiracy'] =  @$json['accessTokenExpiracy'];
                $data['refreshToken'] =  @$json['refreshToken'];
                $data['refreshTokenExpiracy'] =  @$json['refreshTokenExpiracy'];
                if ($data) {
                    $this->Moloni_model->token($data);
                    return $data['accessToken'];
                } else {
                    return 'expired_authorization';
                }
            }
        }
    }
    public function create()
    {
        // $headers = getallheaders();
        // if (!isset($headers['Authorization']) || $headers['Authorization'] !== 'Y2tBvVwO0uWb7G3') {
        //     echo "Error, invalid authorization!";
        //     exit;
        // }

        $http_post = file_get_contents('php://input');
        if (isset($http_post) && !empty($http_post)) {
            $json_post = json_decode($http_post,  true);
        } else {
            $response = array("status" => "invalid_request");
            echo json_encode($response);
        }

        $user = $json_post['user'];

        $vat = $json_post['vat'];
        $number = $json_post['number'];
        $name = $json_post['name'];
        $address = $json_post['address'];
        $city = $json_post['city'];
        $zipCode = $json_post['zipCode'];
        $country = $json_post['country'];

        $dataNew['user'] = $user;
        $dataNew['vat'] = $vat;
        $this->Moloni_model->new('invoices', $dataNew);

        $idReturn = $this->db->insert_id();

        $existing = $this->Moloni_model->check($vat, 'invoices', 'vat');

        if (isset($existing['customerId']) && $existing['customerId'] !== null && $json_post['vat'] !== '*********') {
            $dataNew['customerId'] = $existing['customerId'];
            $this->Moloni_model->update($dataNew, $idReturn, 'invoices', 'id');
        } else {
            $this->createUser($vat, $number, $name, $address, $city, $zipCode, $country, $idReturn);
        }

        foreach ($json_post['products'] as $product) {
            $send = $this->createProduct($product['reference'], $product['title'], $product['price']);
            $return = $send;
            $rest[] = $return;
        }

        $dataNew['productId'] = json_encode($rest);
        $this->Moloni_model->update($dataNew, $idReturn, 'invoices', 'id');

        $end = $this->createInvoice($idReturn);

        $responsEnd = array("id" => $end);
        echo json_encode($responsEnd);
    }
    private function createUser($vat = null, $number = null, $name = null, $address = null, $city = null, $zipCode = null, $country = null, $idReturn = null)
    {
        $token = $this->token();

        if ($token != 'expired_authorization') {
            $number = $number . '-' . date('YmdHis');

            if ($country == 'ES') {
                $country = 70;
                $language = 2;
            } else {
                $country = 1;
                $language = 1;
            }

            $validation = $this->nif($vat);

            if ($validation == 'false') {
                $vat = '*********';
            }

            if ($vat == '*********') {
                $vat = null;
            } else {
                $vat = $vat;
            }

            $query = [
                'query' => '
                    mutation($companyId: Int!, $data: CustomerInsert!) {
                        customerCreate(companyId: $companyId, data: $data) {
                            errors { field msg }
                            data {
                                customerId
                                vat
                                number
                                name
                            }
                        }
                    }
                ',
                'variables' => [
                    'companyId' => 2151,
                    'data' => [
                        'vat' => $vat,
                        'number' => $number,
                        'name' => $name,
                        'address' => $address,
                        'city' => $city,
                        'zipCode' => $zipCode,
                        'countryId' => $country,
                        'languageId' => $language
                    ],
                ],
            ];

            $postfields = json_encode($query);

            $curl = curl_init();

            curl_setopt_array($curl, [
                CURLOPT_URL => "https://api.moloni.es/v1",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => $postfields,
                CURLOPT_HTTPHEADER => [
                    "Authorization: Bearer $token",
                    "Content-Type: application/json"
                ],
            ]);

            $response = curl_exec($curl);
            $err = curl_error($curl);

            curl_close($curl);

            if ($err) {
                $dataNew['customerId'] = $err;

                $this->Moloni_model->update($dataNew, $idReturn, 'invoices', 'id');

                return $err;
            } else {
                $customerId = json_decode($response, true);

                if (isset($customerId['data']['customerCreate']['errors']) && !empty($customerId['data']['customerCreate']['errors'])) {
                    $dataNew['customerId'] = $response;
                } else {
                    $dataNew['customerId'] = $customerId['data']['customerCreate']['data']['customerId'];
                }

                $this->Moloni_model->update($dataNew, $idReturn, 'invoices', 'id');
            }
        }
    }
    private function createProduct($reference = null, $title = null, $price = null)
    {
        $token = $this->token();

        if ($token != 'expired_authorization') {
            $reference = $reference . '-' . date('YmdHis');

            $query = [
                'query' => '
                    mutation($companyId: Int!, $data: ProductInsert!) {
                        productCreate(companyId: $companyId, data: $data) {
                            errors { field msg }
                            data {
                                productId
                                measurementUnitId
                                type
                                reference
                                name
                                price
                            }
                        }
                    }
                ',
                'variables' => [
                    'companyId' => 2151,
                    'data' => [
                        'productCategoryId' => 97814,
                        'measurementUnitId' => 9305,
                        'type' => 1,
                        'reference' => $reference,
                        'name' => $title,
                        'price' => $price,
                        'taxes' => [
                            [
                            "taxId" => 54391,
                            "value" => 21,
                            "ordering" => 1,
                            "cumulative" => false
                            ]
                        ],
                    ],
                ],
            ];

            $postfields = json_encode($query);

            $curl = curl_init();

            curl_setopt_array($curl, [
                CURLOPT_URL => "https://api.moloni.es/v1",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => $postfields,
                CURLOPT_HTTPHEADER => [
                    "Authorization: Bearer $token",
                    "Content-Type: application/json"
                ],
            ]);

            $response = curl_exec($curl);
            $err = curl_error($curl);

            curl_close($curl);

            if ($err) {
                return $err;
            } else {
                $productId = json_decode($response, true);

                if (isset($productId['data']['productCreate']['data']['productId']) && !empty($productId['data']['productCreate']['data']['productId'])) {
                    return $productId['data']['productCreate']['data']['productId'];
                } else {
                    return $response;
                }
            }
        }
    }
    private function createInvoice($idReturn = null)
    {
        $token = $this->token();

        if ($token != 'expired_authorization') {
            $currentDate = new DateTime();

            $existing = $this->Moloni_model->check($idReturn, 'invoices', 'id');

            $products = json_decode($existing['productId']);

            $query = [
                'query' => '
                    mutation($companyId: Int!, $data: InvoiceInsert!) {
                        invoiceCreate(companyId: $companyId, data: $data) {
                            errors { field msg }
                            data {
                                documentId
                            }
                        }
                    }
                ',
                'variables' => [
                    'companyId' => 2151,
                    'data' => [
                        'customerId' => (int) $existing['customerId'],
                        'date' => $currentDate->format('Y-m-d\TH:i:s.u\Z'),
                        'documentSetId' => 2938,
                        'expirationDate' => $currentDate->format('Y-m-d'),
                        'status' => 1,
                        'products' => array_map(function ($product) {
                            return [
                                'productId' => $product,
                                'ordering' => 1,
                                'qty' => 1
                            ];
                        }, $products),
                    ],
                ],
            ];

            $postfields = json_encode($query, JSON_PRETTY_PRINT);

            $curl = curl_init();

            curl_setopt_array($curl, [
                CURLOPT_URL => "https://api.moloni.es/v1",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => $postfields,
                CURLOPT_HTTPHEADER => [
                    "Authorization: Bearer $token",
                    "Content-Type: application/json"
                ],
            ]);

            $response = curl_exec($curl);
            $err = curl_error($curl);

            curl_close($curl);

            if ($err) {
                $dataNew['documentId'] = $err;
                
                $this->Moloni_model->update($dataNew, $idReturn, 'invoices', 'id');

                return $err;
            } else {
                $documentId = json_decode($response, true);

                if (isset($documentId['data']['invoiceCreate']['errors']) && !empty($documentId['data']['invoiceCreate']['errors'])) {
                    $dataNew['documentId'] = $response;

                    $this->Moloni_model->update($dataNew, $idReturn, 'invoices', 'id');

                    return $response;
                } else {
                    $dataNew['documentId'] = $documentId['data']['invoiceCreate']['data']['documentId'];

                    $this->Moloni_model->update($dataNew, $idReturn, 'invoices', 'id');

                    $this->invoiceGetPDF($documentId['data']['invoiceCreate']['data']['documentId'], $idReturn);

                    return $documentId['data']['invoiceCreate']['data']['documentId'];                
                }
            }
        }
    }
    private function invoiceGetPDF($id = null, $idReturn = null)
    {
        $token = $this->token();

        if ($token != 'expired_authorization') {
            $query = [
                'query' => 'mutation invoiceGetPDF($companyId: Int!,$documentId: Int!)
            {
                invoiceGetPDF(companyId: $companyId,documentId: $documentId)
            }',
                'variables' => [
                    'companyId' => 2151,
                    'documentId' => (int) $id,
                ],
            ];
    
            $postfields = json_encode($query, JSON_PRETTY_PRINT);
    
            $curl = curl_init();
    
            curl_setopt_array($curl, [
                CURLOPT_URL => "https://api.moloni.es/v1",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => $postfields,
                CURLOPT_HTTPHEADER => [
                    "Authorization: Bearer $token",
                    "Content-Type: application/json"
                ],
            ]);
    
            $response = curl_exec($curl);
            $err = curl_error($curl);
    
            curl_close($curl);
    
            if ($err) {
                $dataNew['pdf'] = $err;
    
                $this->Moloni_model->update($dataNew, $idReturn, 'invoices', 'id');
            } else {
                $dataNew['pdf'] = $response;
    
                $this->Moloni_model->update($dataNew, $idReturn, 'invoices', 'id');
    
                sleep(6);
    
                $this->invoiceGetPDFToken($id, $idReturn);
            }
        }
    }
    private function invoiceGetPDFToken($id = null, $idReturn = null)
    {
        $token = $this->token();

        if ($token != 'expired_authorization') {
            $query = [
                'query' => 'query invoiceGetPDFToken($documentId: Int!)
                {
                    invoiceGetPDFToken(documentId: $documentId)
                    {
                        data
                        {
                            token
                            filename
                            path
                        }
                        errors
                        {
                            field
                            msg
                        }
                    }
                }',
                'variables' => [
                    'companyId' => 2151,
                    'documentId' => (int) $id,
                ],
            ];
    
            $postfields = json_encode($query, JSON_PRETTY_PRINT);
    
            $curl = curl_init();
    
            curl_setopt_array($curl, [
                CURLOPT_URL => "https://api.moloni.es/v1",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => $postfields,
                CURLOPT_HTTPHEADER => [
                    "Authorization: Bearer $token",
                    "Content-Type: application/json"
                ],
            ]);
    
            $response = curl_exec($curl);
            $err = curl_error($curl);
    
            curl_close($curl);
    
            $json = json_decode($response, true);
    
            if ($err) {
                $dataNew['pdf'] = $err;
    
                $this->Moloni_model->update($dataNew, $idReturn, 'invoices', 'id');
            } else {
                if (isset($json['data']['invoiceGetPDFToken']['data']['path']) && !empty($json['data']['invoiceGetPDFToken']['data']['path'])) {
                    $link = 'https://mediaapi.moloni.org' . $json['data']['invoiceGetPDFToken']['data']['path'] . '?jwt=' . $json['data']['invoiceGetPDFToken']['data']['token'];
    
                    $nameFile = $id . '.pdf';
    
                    $localFile = APPPATH . '../invoices/' . $nameFile;
    
                    $pdfContent = file_get_contents($link);
    
                    file_put_contents($localFile, $pdfContent);
    
                    if (file_exists($localFile)) {
                        $dataNew['pdf'] = base_url() . 'invoices/' . $nameFile;
                    } else {
                        $dataNew['pdf'] = 'error_save_pdf';
                    }
    
                    $this->Moloni_model->update($dataNew, $idReturn, 'invoices', 'id');
                } else {
                    $dataNew['pdf'] = $response;
    
                    $this->Moloni_model->update($dataNew, $idReturn, 'invoices', 'id');
                }
            }
        }
    }
    private function nif($vat = null)
    {
        $curl = curl_init();

        curl_setopt_array($curl, [
            CURLOPT_URL => "https://www.nif.pt/?json=1&q=$vat&key=8b11c83a58cd7080aad7ab47f7a3dae4",
            CURLOPT_RETURNTRANSFER => true,
            CURLOPT_ENCODING => "",
            CURLOPT_MAXREDIRS => 10,
            CURLOPT_TIMEOUT => 30,
            CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
            CURLOPT_CUSTOMREQUEST => "GET",
            CURLOPT_POSTFIELDS => "",
            CURLOPT_COOKIE => "PHPSESSID=dgop03hhnnn1dl17pbj0i7s7t7",
        ]);

        $response = curl_exec($curl);
        $err = curl_error($curl);

        curl_close($curl);

        if ($err) {
            return $err;
        } else {
            $json = json_decode($response, true);
            return ($json['is_nif'] ? 'true' : 'false');
        }
    }
    public function vies($vat = null, $country = null)
    {
        $token = $this->token();

        if ($token != 'expired_authorization') {
            $query = [
                'query' => 'query viesCheck($countryISO: String!,$vat: String!)
                {
                    viesCheck(countryISO: $countryISO,vat: $vat) {
                        data
                        {
                            name
                            address
                            zipCode
                            city
                            countryCode
                        }
                        errors
                        {
                            field
                            msg
                        }
                    }
                }',
                'variables' => [
                    'countryISO' => $country,
                    'vat' => $vat,
                ],
            ];
    
            $postfields = json_encode($query, JSON_PRETTY_PRINT);
    
            $curl = curl_init();
    
            curl_setopt_array($curl, [
                CURLOPT_URL => "https://api.moloni.es/v1",
                CURLOPT_RETURNTRANSFER => true,
                CURLOPT_ENCODING => "",
                CURLOPT_MAXREDIRS => 10,
                CURLOPT_TIMEOUT => 30,
                CURLOPT_HTTP_VERSION => CURL_HTTP_VERSION_1_1,
                CURLOPT_CUSTOMREQUEST => "POST",
                CURLOPT_POSTFIELDS => $postfields,
                CURLOPT_HTTPHEADER => [
                    "Authorization: Bearer $token",
                    "Content-Type: application/json"
                ],
            ]);
    
            $response = curl_exec($curl);
            $err = curl_error($curl);
    
            curl_close($curl);
    
            if ($err) {
                echo $err;
            } else {
                echo $response;
            }
        }
    }
}