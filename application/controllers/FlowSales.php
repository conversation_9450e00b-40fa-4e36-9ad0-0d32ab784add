<?php

defined('BASEPATH') or exit('No direct script access allowed');

class FlowSales extends CI_Controller
{
	public function __construct()
	{
		parent::__construct();
		//$this->load->model('FlowProducts_model');
		// $this->load->model('FlowWorten_model');
		// $this->load->model('FlowOrders_model');
	}

	public function index()
	{
		echo 'Access denied..';
	}

	private function calcularPorcentagem($valorParcial, $valorTotal) {
		$porcentagem = ($valorParcial / $valorTotal) * 100;
		return $porcentagem;
	}


	# Verificar que ainda falta a tela pincipal do projeto e seu valores.
	# Aplicacao dos daos par porcentagem setada.

	public function get_orders(){

		$this->db->select('*');
		$this->db->where('channel', 'worten');
		$this->db->where('country', 'PT');
		$this->db->where('id', '588923');
		$this->db->order_by('id', 'desc');
		#$this->db->limit('20');
		$orders = $this->db->get('tbl_orders')->result();

		foreach($orders as $key => $order){

			# Quantidade de itens da venda
			$itens_venda = count(json_decode($order->items));
			$marketplace = $order->channel;
			$default_shipping_price = 5;

			# Seller ID
			$seller_id = $order->user_id;
			
			if ($marketplace == 'worten') {
				if ($itens_venda > 1) {
					var_dump('mais de 1');
					die();
				} else {
					$items = json_decode($order->items);
					$total_taxes = 0;
					foreach ($items as $item){
		
						$taxes = $item->taxes[0]->amount ?? 0;
						$total_price_no_taxes = $item->total_price - $taxes;

						$item_values = [
							'price_unit' => round($total_price_no_taxes, 2),
							'taxes_amount' => $item->taxes[0]->amount ?? 0,
							'total_price' => $item->total_price,
							'total_commission' => $item->total_commission,
							'quantity_product' => $item->quantity
						];

						$total_taxes = $total_taxes + $taxes;

						# seller product 
						// echo json_encode($order);
						// die();
					}

					$total_no_shipping = $order->total_price - $default_shipping_price;
					$porcentagem = $this->calcularPorcentagem($order->total_commission, $order->total_price);

					$data_order = [
						'Order ID' => $order->reference,
						'total_order_sem_taxas '=> round($order->total_price - $total_taxes, 2),
						'total_taxas '=> $total_taxes,
						'total_order' => $order->total_price,
						
						'total_comissão_marketplace' => $order->total_commission,
						'porcentagem_da_comissao_cobrada_marketplace' => round($porcentagem),
						'total_repassado_pelo_marketplace' => round($order->total_price - $order->total_commission, 2),
						'---------1º Cenário de Repasse ERRADO-----------------------------------------',
						'1_total_que_vendedor_quer_receber' => round($total_no_shipping, 2),
						'total_que_vendedor_vai_receber_tirando_15%_marketplace' => round($total_no_shipping - ($total_no_shipping * 0.15), 2),
						'total_que_vendedor_vai_receber_tirando_15%_marketplace_+_portes_5€' => round($total_no_shipping - ($total_no_shipping * 0.15) + 5, 2),
						'---------2º Cenário de Repasse------------------------------------------------',
						'2_total_que_vendedor_quer_receber' => round($total_no_shipping, 2),
						'adicionamos_os_5€_ao_valor_de_venda' => round($total_no_shipping + 5, 2),
						'total_que_vendedor_vai_receber_tirando_15%_marketplace_do_valor_total' => round(($total_no_shipping + 5)- (($total_no_shipping + 5) * 0.15), 2),
						'lucro_da_BIGhub_ao_retirar_15%_do_marketplace' => round(($order->total_price - $order->total_commission) - (($total_no_shipping + 5)- (($total_no_shipping + 5) * 0.15)), 2),
						'------------------------------------------------------------------------------',
						'items '=> $item_values,
					];

					echo json_encode($data_order);
					die();
				}
			}
		}


		die();
	}


	public function set_order_user($manual = false)
	{
		$valor_adicional_portes = 5;

		# Vai buscar todas as vendas que ainda não existem user
		$orders_no_user = $this->FlowOrders_model->curl_get_orders_no_user();

		if (!isset($orders_no_user->status)) {

			foreach ($orders_no_user as $order) {

				$shipping_price = $order->responseFirst->shipping_price;
				$marketplace = $order->marketplace;

				$users_id = [];
				if ($marketplace == 'worten') {
					if ($order->products > 1) {
						$products = $order->response->order_lines;
						$origin = $order->code_marketplace->code;

						foreach ($products as $prod) {

							# Existem 2 ou mais itens na ordem , será preciso validar se os itens pertence ao mesmo franqueado
							$ean = substr($prod->offer_sku, 6);
							$price = round($this->aplicarImposto($prod->total_price, $origin) - $valor_adicional_portes, 2);
							// $real_price = $price - $shipping_price;

							$id_marketplace = $this->get_cod_marketplace($order->code_marketplace);

							$this->db->select('*');
							$this->db->from('tbl_offers_reports');
							$this->db->where('ean', $ean);
							$this->db->where('price', $price);
							$this->db->where('id_marketplace', $id_marketplace);
							$this->db->order_by('id', 'desc');
							$this->db->limit(1);
							$found_seller = $this->db->get()->row();

							if (isset($found_seller)) {
								$users_id[] = $found_seller->user_id;
							}
						}

						# Valida se os itens são do mesmo vendedor
						$iguais = $this->validarValoresIguais($users_id);
		
						if ($iguais) {
							$user_id = $users_id[0];
						} else {
							$user_id = 5000;
						}
					} else {
						# Preparando os dados da buscar
						$ean = substr($order->offer_sku, 6);
						$price = $order->price - $valor_adicional_portes;
						$real_price = $price - $shipping_price;
						$id_marketplace = $this->get_cod_marketplace($order->code_marketplace);
						

						$this->db->select('*');
						$this->db->from('tbl_offers_reports');
						$this->db->where('ean', $ean);
						$this->db->where('price', $real_price);
						$this->db->where('id_marketplace', $id_marketplace);
						$this->db->order_by('id', 'desc');
						$this->db->limit(1);
						$found_seller = $this->db->get()->row();
						// echo json_encode($found_seller);
						// die();

						$user_id = 3000;
						if (isset($found_seller)) {
							$user_id = $found_seller->user_id;
						} else {
							var_dump('Order número:' . $order->order_id);
						}
					}
				}

				if ($marketplace == 'carrefour') {

					if ($order->products > 1) {
						$products = $order->response->order_lines;
						$origin = $order->code_marketplace->code;

						foreach ($products as $prod) {

							# Existem 2 ou mais itens na ordem , será preciso validar se os itens pertence ao mesmo franqueado
							$ean = substr($prod->offer_sku, 6);
							$price = round($this->aplicarImposto($prod->total_price, $origin) - $valor_adicional_portes, 2);

							$id_marketplace = $this->get_cod_marketplace($order->code_marketplace);

							$item = $prod->responseFirst->order_lines;
							$quantity = $item[0]->quantity;
							if($quantity > 1){
								$price = ($order->price / $quantity ) - $valor_adicional_portes;
							}

							$this->db->select('*');
							$this->db->from('tbl_offers_reports');
							$this->db->where('ean', $ean);
							$this->db->where('price', $price);
							$this->db->where('id_marketplace', $id_marketplace);
							$this->db->order_by('id', 'desc');
							$this->db->limit(1);
							$found_seller = $this->db->get()->row();

							if (isset($found_seller)) {
								$users_id[] = $found_seller->user_id;
							}
						}

						# Valida se os itens são do mesmo vendedor
						$iguais = $this->validarValoresIguais($users_id);
		
						if ($iguais) {
							$user_id = $users_id[0];
						} else {
							$user_id = 5000;
						}
					} else {
						
						# Preparando os dados da buscar
						$ean = substr($order->offer_sku, 6);
						$price = $order->price - $valor_adicional_portes;
						$id_marketplace = $this->get_cod_marketplace($order->code_marketplace);

						$item = $order->responseFirst->order_lines;
						$quantity = $item[0]->quantity;
						if($quantity > 1){
							$price = ($order->price / $quantity ) - $valor_adicional_portes;
						}

						$this->db->select('*');
						$this->db->from('tbl_offers_reports');
						$this->db->where('ean', $ean);
						$this->db->where('price', $price);
						$this->db->where('id_marketplace', $id_marketplace);
						$this->db->order_by('id', 'desc');
						$this->db->limit(1);
						$found_seller = $this->db->get()->row();
						#var_dump($found_seller);

						// echo json_encode($found_seller);
						// die();

						$user_id = 3000;
						if (isset($found_seller)) {
							$user_id = $found_seller->user_id;
						} else {
							var_dump('Order número:' . $order->order_id);
						}
					}
				}

				# Atualiza a order com o user_id
				$data_update = [
					'ean' => $ean,
					'tbl_id' => $order->tbl_id,
					'user_id' => $user_id
				];

				$this->FlowOrders_model->update_order_user($data_update);

				if ($user_id != 3000 || $user_id != 5000 || $user_id != null) {
					// var_dump($user_id, 'disparara email');
					// die();
					$this->send_email($user_id);
					var_dump('order número:' . $order->order_id . 'encaminhada para ID:' . $user_id);
				}else{
					echo json_encode(('error_dev'));
				}
				die();
				// var_dump($user_id.' - '.$order->order_id);

			}
			var_dump('order número:' . $order->order_id . ' Não funcionou - encaminhada para ID:' . $user_id);
			echo 'Set order user success.';
			return;
		}
		echo 'Orders not found';
	}

	private function aplicarImposto($price, $origin)
	{
		if ($origin === 'WRT_PT_ONLINE') {
			$percentual = 0.23; // 23%
		} elseif ($origin === 'WRT_ES_ONLINE') {
			$percentual = 0.21; // 21%
		} else {
			return $price; // Se o campo origin for diferente, retorna o preço sem alteração
		}

		$valor_com_imposto = $price * (1 + $percentual);
		return $valor_com_imposto;
	}

	# Verifica se valores são iguais
	private function validarValoresIguais($array)
	{
		// Verifica se o array está vazio
		if (empty($array)) {
			return true;
		}

		// Obtém o primeiro valor do array
		$primeiro_valor = reset($array);

		// Verifica se todos os valores são iguais ao primeiro valor
		foreach ($array as $valor) {
			if ($valor !== $primeiro_valor) {
				return false;
			}
		}

		// Se todos os valores são iguais, retorna true
		return true;
	}

	private function send_email($user)
	{
		$data_email = [
			"template" => 'M006',
			"user_id" => $user
		];

		$resp = $this->_curl_send_email($data_email);

		if ($this->db->insert('tbl_log_emails', $data_email)) {
			return [
				'message' => $resp->message
			];
		}
	}

	private function get_cod_marketplace($marketplace)
	{
		switch ($marketplace->code) {
			case "WRT_PT_ONLINE":
				return 2;
				break;
			case "WRT_ES_ONLINE":
				return 3;
				break;
			case "CRF_ES_ONLINE":
				return 4;
				break;	
		}
	}

	private function send_product_marketplace($offer)
	{
		$kuanto_kusta = array(1);
		$worten = array(2, 3);
		$amazon = array(4, 5, 6, 7, 8, 9, 10, 11, 12);

		if (in_array($offer['marketplace'], $kuanto_kusta)) {
			echo "Kuanto Kusta";
		}
		if (in_array($offer['marketplace'], $worten)) {
			$return = $this->FlowWorten_model->send_product_worten($offer);
			$this->send_tbl_offers_reports($return);
			echo "Send Worten";
		}
		if (in_array($offer['marketplace'], $amazon)) {
			echo "Amazon";
		}
	}

	private function send_tbl_offers_reports($data)
	{

		$response = $data['response'];
		$products = $data['offer']['data'];
		$requests = [
			'offer' => $data['offer'],
			'request' => $data['request']
		];

		var_dump($response);

		if ($response != 'no_response') {
			$id_transaction = $data['response']->import_id;
			$lines = [];
			foreach ($products as $prod) {
				$payload = [
					'offer_id' => $prod['offer_id'],
					'id_marketplace' => $prod['id_marketplace'],
					'ean' => $prod['ean'],
					'user_id' => $prod['user_id'],
					'request' => json_encode($requests),
					'response' => json_encode($response),
					'import_id' => $id_transaction
				];
				array_push($lines, $payload);
			}

			if ($this->db->insert_batch('tbl_offers_reports', $lines)) {
				$group = $this->group_by_field($lines, 'import_id');
				$data = [
					'import_id' => $group['import_id'],
					'id_marketplace' => $group['id_marketplace'],
				];
				$this->FlowProducts_model->pushToQueueUpdateState($data);
				return;
			}
		} else {
			$lines = [];
			foreach ($products as $prod) {
				$payload = [
					'offer_id' => $prod['offer_id'],
					'id_marketplace' => $prod['id_marketplace'],
					'ean' => $prod['ean'],
					'user_id' => $prod['user_id'],
					'request' => json_encode($requests),
					'response' => 'no_response',
					'import_id' => 'no_import_id'
				];
				array_push($lines, $payload);
			}

			if ($this->db->insert_batch('tbl_offers_reports', $lines)) {
				return;
			}
		}
	}

	private function get_info_product($cod_mkt)
	{
		$info_product = $this->db->select('*')->where('cod_mkt', $cod_mkt)->get('tbl_user_products')->row();
		return $info_product;
	}


	private function insert_update_offer($ean, $product)
	{
		# Get Offer
		$offer = $this->db->select('*')->where('ean', $ean)->where('id_marketplace', $product->id_marketplace)->get('tbl_offers')->row();

		# Get Info to Product
		$product_found = $this->get_info_product($product->cod_mkt_product);

		# Prepared Product
		$data_product = [
			'ean' => $ean,
			'price' => $product->price,
			'id_marketplace' => $product->id_marketplace,
			'stock' => $product->stock,
			'user_id' => $product_found->user_id,
		];

		# Validate offers
		if (!$offer) {
			if ($this->db->insert('tbl_offers', $data_product)) {
				$data_product['offer_id'] = $this->db->insert_id();
			}
		} else {
			$this->db->where('id', $offer->id);
			if ($this->db->update('tbl_offers', $data_product)) {
				$data_product['offer_id'] = $offer->id;
			}
		}
		return $data_product;
	}


	public function update_state()
	{
		try {
			$http_post = file_get_contents('php://input');

			if (isset($http_post) && !empty($http_post)) {
				$request = json_decode($http_post,  true);

				$kuanto_kusta = array(1);
				$worten = array(2, 3);
				$amazon = array(4, 5, 6, 7, 8, 9, 10, 11, 12);

				if (in_array($request['id_marketplace'], $kuanto_kusta)) {
					echo "Kuanto Kusta";
				}
				if (in_array($request['id_marketplace'], $worten)) {
					$data = [
						'id' => $request['import_id']
					];
					$resp = $this->FlowWorten_model->curl_update_state($data);

					$this->db->where('import_id', $request['import_id']);
					$update = ['state' => $resp->status];
					if ($this->db->update('tbl_offers_reports', $update)) {
						echo "Send Worten Success";
					}
				}
				if (in_array($request['id_marketplace'], $amazon)) {
					echo "Amazon";
				}
			}
		} catch (\Exception $exc) {
			return $this->error([], [
				'message' => $exc->getMessage()
			]);
		}
	}


	private function group_by_field($objects, $field)
	{
		// Agrupa os objetos pelo campo especificado
		$grouped_objects = [];
		foreach ($objects as $object) {
			$field_value = $object[$field];
			if (!isset($grouped_objects[$field_value])) {
				$grouped_objects[$field_value] = $object;
			}
		}

		// Retorna apenas um objeto agrupado pelo campo especificado
		return reset($grouped_objects);
	}

	public function get_ean()
	{
		$result = $this->db->select('*')
			->where('id >', 1)
			->where('ean',  null)
			->limit(20000, 1)
			->get('tbl_products_marktplaces')
			->result();

		foreach ($result as $value) {
			$this->db->select('*');
			$product = $this->db->from('tbl_user_products')->where('cod_mkt', $value->cod_mkt_product)->get()->row();
			if ($product) {

				$data = [
					'ean' => $product->barCode
				];
				$this->db->where('cod_mkt_product', $value->cod_mkt_product);
				$this->db->update('tbl_producs_marktplaces', $data);
				echo 'update ean ' . $product->barCode;
			}
		}
		echo 'fim';
	}

	// private function send_to_queue()
	// {
	// 	var_dump('aqui');
	// 	die();
	// 	try {
	// 		var_dump('enviar para a fila');
	// 		die();
	// 	} catch (\Exception $exc) {
	// 		return $this->error([], [
	// 			'message' => $exc->getMessage()
	// 		]);
	// 	}
	// }	

}
