<?php

ini_set('memory_limit', '4096M'); // Defina o valor de memória desejado, como 1024M para 1GB

defined('BASEPATH') or exit('No direct script access allowed');

class Orders extends MY_Controller
{

	public function __construct()
	{
		parent::__construct();
		$this->load->library('pagination');
	}

	private function formatName($fullName)
	{
		// Remove caracteres especiais e substitui "-" por espaços
		$cleanedName = preg_replace('/[^A-Za-z0-9\s]/', '', str_replace('-', ' ', $fullName));

		// Divide o nome em palavras usando os espaços como delimitadores
		$words = explode(' ', strtolower($cleanedName));  // Convertendo para minúsculas

		// Coloca a primeira letra de cada palavra em maiúscula
		$capitalizedWords = array_map('ucfirst', $words);

		// Junta as palavras novamente em um nome formatado
		$formattedName = implode(' ', $capitalizedWords);

		return $formattedName;
	}

	public function list_orders_oldddd()
	{
		$data = $this->_getDataDashboard();

		$this->db->select('ord.*, IFNULL(inv.id, 0) as isinvoice, inv.id_invoice');
		$this->db->from('tbl_orders ord');
		$this->db->join('tbl_invoices inv', 'ord.id = inv.order_id', 'left');

		#$this->db->where('reference', '4723498-A');
		$this->db->limit(40);
		$this->db->order_by('ord.id', 'desc');
		$orders = $this->db->get()->result();

		foreach ($orders as $key => $order) {
			$customer =  json_decode($order->customer);
			$client_name = $this->formatName($customer->first_name . ' ' . $customer->last_name);
			$orders[$key]->client_name = $client_name;
		}

		// echo json_encode($orders);
		// die();

		$data['orders'] = $orders;
		$data += $this->_getRespAction();

		$data['session'] = 'Orders';
		$data['view'] = 'orders/list_orders';
		$this->load->view('includes/template', $data);
	}

	public function list_orders()
	{
		$data = $this->_getDataDashboard();
		$data += $this->_getRespAction();

		$data['session'] = 'Orders';
		$data['view'] = 'orders/list_orders';
		$this->load->view('includes/template', $data);
	}

	public function order_details($id)
	{
		$data = $this->_getDataDashboard();

		$query = $this->db->where('id', $id)->get('tbl_orders');
		$resp = $query->row();

		$address = $resp->customer;
		$data = json_decode($address, true);

		$billingAddress = $data['billing_address'];
		$shippingAddress = $data['shipping_address'];

		// if($billingAddress == null){
		// 	$billingAddress = [
		// 		'first_name' => 'No name',
		// 		'last_name' => 'No last name',
		// 		'phone' => 'No phone',
		// 		'street_1' => ' No address',
		// 		'zip_code' => 'no zip_code',
		// 		'city' => 'No city'
		// 	];
		// }

		// if($shippingAddress == null){
		// 	$shippingAddress = [
		// 		'first_name' => 'No name',
		// 		'last_name' => 'No last name',
		// 		'phone' => 'No phone',
		// 		'street_1' => ' No address',
		// 		'zip_code' => 'no zip_code',
		// 		'city' => 'No city'
		// 	];
		// }
		$img_default = 'https://cdn.bighub.store/image/product-placeholder.png';
		$image = $img_default;

		#$data['attachment'] = $resp->attachment;
		$itens = json_decode($resp->items);
		$total_taxes = 0;

		$state_neuter = ['SHIPPING'];
		$state_positive = ['RECEIVED', 'TO_COLLECT', 'SHIPPED'];
		$state_negative = ['WAITING_ACCEPTANCE', 'WAITING_DEBIT', 'WAITING_DEBIT_PAYMENT', 'CLOSED', 'REFUNDED', 'CANCELED', 'REFUSED', 'AUTO_REFUSED', 'AUTO_CLOSED'];
		$state_class = 'state-neutra';

		foreach ($itens as $key => $item) {

			$taxes = $item->taxes[0]->amount ?? 0;
			$total_taxes = $total_taxes + $taxes;

			if (in_array($item->state, $state_positive)) {
				$state_class = 'state-positive';
			} elseif (in_array($item->state, $state_negative)) {
				$state_class = 'state-negative';
			} elseif (in_array($item->state, $state_neuter)) {
				$state_class = 'state-neuter';
			}

			if (empty($item->image) || $item->image != 'N/A') {
				$image = $item->image;
			}

			$new_itens[$key] = [
				'title' => $item->title,
				'ean' => $item->ean,
				'sku' => $item->sku,
				'image' => $image,
				'image_real' => $item->image,
				'state' => $item->state,
				'class_state' => $state_class,
				'quantity' => $item->quantity,
				'leadtime_to_ship' => $item->leadtime_to_ship,
				'shipping_price' => $item->shipping_price,
				'taxe_amount' => $taxes,
				'commission_fee' => $item->commission_fee,
				'commission_rate_vat' => $item->commission_rate_vat,
				'total_commission' => $item->total_commission,
				'total_price' => $item->total_price,
				'total_price_no_taxe' => round($item->total_price - $taxes, 2),
				'price_unit' => $item->price_unit,
			];
		}

		$data['items'] = json_decode(json_encode($new_itens));
		$data['total_taxes'] = $total_taxes;

		$data['id_order'] = $id;

		# Seller
		$seller = $this->_getSeller(json_decode($resp->user_id));
		$data['seller'] = $seller;
		$seller_country_iban = $seller->bank_number ? substr($seller->bank_number, 0, 2) : 'Error';
		$data['order_data'] = $resp;

		$data['order_data']->text_iva = 'Error';
		$data['order_data']->text_iva_2 = 'Error';
		$data['order_data']->valor_iva_cobrado = 0.00;

		if ($seller_country_iban == 'Error') {
			if ($this->_verificarLetra($seller->nif)) {
				$seller_country_iban = 'ES';
			} else {
				$seller_country_iban = 'PT';
			}
		}

		if ($seller_country_iban == 'PT') {
			$recebido_marketplace = $resp->total_price - $resp->total_commission;
			$porcentagem = 0.23;
			$data['order_data']->text_iva = 'IVA - 23%';
			$data['order_data']->text_iva_2 = 'C/IVA';
			$data['order_data']->valor_iva_cobrado = $recebido_marketplace * $porcentagem;
		}

		if ($seller_country_iban == 'ES') {
			$porcentagem = 0.21;
			$recebido_marketplace = $resp->total_price - $resp->total_commission;
			$data['order_data']->valor_iva_cobrado = $recebido_marketplace * $porcentagem;
			$data['order_data']->text_iva = '+ IVA - 21%';
			$data['order_data']->text_iva_2 = 'S/IVA';
		}

		$data['seller']->iban = $seller->bank_number;
		$data['seller']->seller_country_iban = $seller_country_iban;

		$data['customer'] = json_decode($resp->customer);
		$data['billing_address'] = $billingAddress;
		$data['shipping_address'] = $shippingAddress;
		$data['order_items'] = count(json_decode($resp->items));

		$data['payment'] = json_decode($resp->payment)->type ?? 'Awaiting payment';


		$data['fatura'] = false;
		$data['link_fatura'] = 'Ainda sem anexo.';

		if ($resp->attachment != null && !empty($resp->attachment)) {

			$raw = $resp->attachment;
			$array = json_decode($raw, true);

			$fatura = $array[0]['file_name'];
			$user = $this->db->where('id', $resp->user_id)->get('tbl_user_clients')->row();

			$token = $this->curl_get_jwttoken_user($user->user_token);

			$data['fatura'] = true;
			$data['link_fatura'] = 'https://app.bighub.store/api/v2/orders/' . $resp->id . '/attachments?file_name=' . $fatura . '&token=' . $token->data->api_token;
		}


		#Shipping BIGhub

		$data['finance'] = json_decode($resp->finance);

		// echo json_encode($data['order_data']);
		// die();


		// // Converte o array para uma string
		// $responseString = implode(array_map("chr", $response));

		// // Desempacota a string convertida
		// $unpackedData = unpack('H*', $responseString);

		$this->load->view('orders/modal-order-details.php', $data);
	}

	private function curl_get_jwttoken_user($user_token)
	{
		$url  = "https://app.bighub.store/api/v1/authorize?app_key=$user_token";

		$handle   = curl_init($url);

		$request_headers     = [];
		$request_headers[] = 'Accept: application/json';
		$request_headers[] = 'Content-Type: application/json';

		curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);
		$response = curl_exec($handle);

		return json_decode($response);
	}

	public function impersonate()
	{
		$token = $this->input->post('user');
		$resp = $this->curl_impersonate($token);
		echo json_encode($resp->data->api_token);
	}

	private function curl_impersonate($user_token)
	{
		$url  = "https://app.bighub.store/api/v1/authorize?app_key=$user_token";

		$handle   = curl_init($url);

		$request_headers     = [];
		$request_headers[] = 'Accept: application/json';
		$request_headers[] = 'Content-Type: application/json';

		curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);
		$response = curl_exec($handle);

		return json_decode($response);
	}

	public function update_purchased_order()
	{
		$order_id = $this->input->post('order');
		$action = $this->input->post('action');

		$this->db->where('id', $order_id);
		$data = [
			'purchased' => $action
		];

		if ($this->db->update('tbl_orders', $data)) {
			$resp = [
				'update' => 'success',
				'action' => $action
			];
			echo json_encode($resp);
		}
	}

	# UPDATE COUNTRY ORDERS
	public function updateOrdersCountry()
	{

		echo 'updateordersCountry';
		die();
		$references = array(
			'46334547-A', '46333647-A', '46331749-A', '46331463-A', '46331369-A', '46331111-A',
			'46330585-A', '4493066-A', '4490261-A', '46322997-A', '46307735-A', '46307693-A', '46307027-A', '46304193-A',
			'46303227-A', '46302663-A', '46301961-A', '46301767-A', '46300731-A', '4417095-A', '46298467-A', '4407422-A',
			'46295161-A', '4392042-A', '4390959-A', '4390789-A', '4390666-A', '4389987-A', '4388905-A',
			'4387923-A', '4387892-A', '4387725-A', '4386765-A', '4387681-A', '4383552-A', '46286183-A', '46286173-A',
			'46286165-A', '46286163-A', '46286159-A', '46286157-A', '46286153-A', '46286149-A', '46286145-A', '46286129-A',
			'46286123-A', '46286121-A', '46286115-A', '46286109-A', '46286107-A', '46286091-A', '46286089-A', '46286051-A',
			'46286031-A', '46286029-A', '46286025-A', '46286023-A', '46286021-A', '46286015-A', '46285999-A', '46285973-A',
			'46285971-A', '46285969-A', '46285965-A', '46285961-A', '46285957-A', '46285951-A', '46285949-A', '46285897-A',
			'46285879-A', '46285869-A', '46285855-A', '46285853-A', '46285835-A', '46285827-A', '4377016-A', '46282959-A',
			'46280153-A', '46279385-A', '46273460-A', '4333448-A', '4327239-A', '4326601-A', '4325112-A', '4321133-A',
			'4312945-A', '46185725-A'
		);



		$this->db->select('*');
		$this->db->from('tbl_orders');
		$this->db->where_in('reference', $references);

		$query = $this->db->get();

		if ($query->num_rows() > 0) {
			foreach ($query->result() as $row) {

				//var_dump($row->reference);
				// echo $row->customer;
				// die();
				// Decodificar o JSON para um array associativo
				$customerData = json_decode($row->customer, true);

				// Verificar e atualizar o campo 'country'
				$billingAddress = $customerData['billing_address'];
				if (isset($billingAddress['country_iso_code'])) {
					$isoCode = strtolower($billingAddress['country_iso_code']);
					if (in_array($isoCode, ['fr', 'france', 'fra'])) {
						$billingAddress['country'] = 'FR';
					} else {
						$billingAddress['country'] = 'PT';
					}
				}

				// Atualizar o JSON com o 'country' modificado
				$customerData['billing_address'] = $billingAddress;

				// Codificar de volta para JSON
				$updatedCustomerJson = json_encode($customerData);

				// echo $updatedCustomerJson;
				// die();

				// Atualizar os dados na base de dados
				// Exemplo usando CodeIgniter para atualizar
				$this->db->set('customer', $updatedCustomerJson);
				$this->db->where('id', $row->id); // Supondo que 'id' seja a chave primária da sua tabela
				$this->db->update('tbl_orders');

				// Exibir ou registrar confirmação de atualização, se necessário
				echo "Dados atualizados para o ID: " . $row->id . "<br>";
			}
		} else {
			echo "Nenhum resultado encontrado.";
		}
	}


	# GET ORDER TO DATATABLE ###############################################################
	public function get_orders_datatable_old()
	{
		$draw = intval($this->input->post("draw"));
		$start = intval($this->input->post("start"));
		$length = intval($this->input->post("length"));
		$search = $this->input->post("search")['value'];
		$order_column = intval($this->input->post("order")[0]['column']);
		$order_dir = $this->input->post("order")[0]['dir'];

		$columns = [
			null,
			'reference',
			'client_name',
			'created_date',
			'items_count',
			'channel_country',
			'user_id',
			'state',
			'total_price',
			null
		];

		$order_by = isset($columns[$order_column]) ? $columns[$order_column] : 'id';
		$order_dir = ($order_dir === 'asc' || $order_dir === 'desc') ? $order_dir : 'desc';

		$this->db->select('ord.id, ord.reference, ord.created_date, ord.items, ord.channel, ord.country, ord.user_id, ord.state, ord.total_price, ord.customer, ord.attachment, ord.invoice_validate, IFNULL(inv.id, 0) as isinvoice, inv.id_invoice');
		$this->db->from('tbl_orders ord');

		if (!empty($search)) {
			$this->db->group_start();
			$this->db->like('ord.reference', $search);
			$this->db->or_like('ord.channel', $search);
			$this->db->group_end();
		}

		$this->db->join('tbl_invoices inv', 'ord.id = inv.order_id', 'left');

		$totalFiltered = $this->db->count_all_results('', FALSE);

		$this->db->limit($length, $start);
		$this->db->order_by($order_by, $order_dir);

		$query = $this->db->get();
		$orders = $query->result();

		foreach ($orders as $key => $order) {
			$customer = json_decode($order->customer);
			$client_name = $this->formatName($customer->first_name . ' ' . $customer->last_name);
			$orders[$key]->client_name = $client_name;
		}

		$data = array();
		foreach ($orders as $order) {
			$items_count = count(json_decode($order->items));
			$channel_country = $order->channel . '/' . $order->country;

			$stateClass = $this->getStateClass($order->state);

			// Generate action buttons
			$actions = "<a class='link-modal-dados-order btn btn-info btn-sm new-button' onclick='setId($order->id)' href='" . base_url() . "order-details/$order->id' data-bs-toggle='modal' data-bs-target='#modal-dados-order'>Detalhes</a>";

			if ($order->channel === 'pccomp' || $order->channel === 'pccompes') {
				if ($order->isinvoice) {
					$actions .= "<a target='_blank' class='link-modal-dados-order btn btn-success btn-sm new-button' style='font-size: 10px;' href='https://prd-mkp.bighub.store/invoices/$order->id_invoice.pdf'>Ver Fatura</a>";
				} else {
					$actions .= "<div id='invoice-loading-$order->id' class='spinner-border esconder' role='status' style='width: 15px; height: 15px;'>
									<span class='sr-only'>Loading...</span>
								 </div>
								 <a id='see-invoice-$order->id' target='_blank' class='link-modal-dados-order btn btn-success btn-sm new-button esconder' style='font-size: 10px;'>Ver Fatura</a>
								 <button id='generate-invoice-$order->id' class='link-modal-dados-order btn btn-primary btn-sm new-button' onclick='generateInvoice($order->id)' style='font-size: 10px;'>Gerar Fatura</button>";
				}
			}

			$data[] = array(
				"id" => "",
				"reference" => $order->reference,
				"client_name" => $order->client_name,
				"created_date" => date("d/m/Y", strtotime($order->created_date)),
				"items_count" => $items_count,
				"channel_country" => $channel_country,
				"user_id" => $order->user_id,
				"state" => "<span class='$stateClass'>$order->state</span>",
				"total_price" => $order->total_price,
				"attachment" => $order->attachment, // Add attachment data
				"validate_invoice" => $order->invoice_validate ? true : false, // Add attachment data
				"actions" => $actions
			);
		}

		$result = array(
			"draw" => $draw,
			"recordsTotal" => $this->get_total_orders(),
			"recordsFiltered" => $totalFiltered,
			"data" => $data
		);

		echo json_encode($result);
	}

	private function get_total_orders()
	{
		$this->db->select("COUNT(*) as num");
		$this->db->from("tbl_orders");
		$query = $this->db->get();
		$result = $query->row();
		return $result->num;
	}

	private function getStateClass($state)
	{
		$statePositive = array('RECEIVED', 'TO_COLLECT', 'SHIPPED');
		$stateNegative = array('WAITING_ACCEPTANCE', 'WAITING_DEBIT', 'WAITING_DEBIT_PAYMENT', 'CLOSED', 'REFUNDED', 'CANCELED', 'REFUSED', 'AUTO_REFUSED', 'AUTO_CLOSED');
		$stateNeuter = array('SHIPPING');

		if (in_array($state, $statePositive)) {
			return 'badge-success badge-sm';
		} else if (in_array($state, $stateNegative)) {
			return 'badge-negative badge-sm';
		} else if (in_array($state, $stateNeuter)) {
			return 'badge-neuter badge-sm';
		}
		return '';
	}

	public function check_invoice_validate()
	{
		$order_id = $this->input->post('order_id');
		$action_value = $this->input->post('action');

		$this->db->where('id', $order_id);

		$data = [
			'invoice_validate' => $action_value
		];

		if ($this->db->update('tbl_orders', $data)) {
			$resp = ['update' => 'success'];
			echo json_encode($resp);
		}
	}

	##########################################################################################

	public function get_orders_datatable()
	{
		$draw = intval($this->input->post("draw"));
		$start = intval($this->input->post("start"));
		$length = intval($this->input->post("length"));
		$search = $this->input->post("search")['value'];
		$order_column = intval($this->input->post("order")[0]['column']);
		$order_dir = $this->input->post("order")[0]['dir'];
	
		$columns = [
			'id',
			'reference',
			'client_name',
			'created_date',
			'items_count',
			'channel_country',
			'user_id',
			'state',
			'total_price',
			'attachment',
			'actions'
		];
	
		$order_by = isset($columns[$order_column]) ? $columns[$order_column] : 'id';
		$order_dir = ($order_dir === 'asc' || $order_dir === 'desc') ? $order_dir : 'desc';
	
		$this->db->select('
			ord.id,
			ord.reference,
			ord.created_date,
			ord.items,
			ord.channel,
			ord.country,
			ord.user_id,
			ord.state,
			ord.total_price,
			ord.attachment,
			ord.invoice_validate,
			IFNULL(inv.id, 0) as isinvoice,
			inv.id_invoice
		');
		$this->db->from('tbl_orders ord');
		$this->db->join('tbl_invoices inv', 'ord.id = inv.order_id', 'left');
	
		// Adicionar filtro para puxar somente pedidos a partir do início do ano corrente
		$startOfYear = date('Y') . '-01-01'; // Obtém a data do primeiro dia do ano corrente
		$this->db->where('ord.created_date >=', $startOfYear);
	
		if (!empty($search)) {
			$this->db->group_start();
			$this->db->like('ord.reference', $search);
			$this->db->or_like('ord.channel', $search);
			$this->db->group_end();
		}
	
		$totalFiltered = $this->db->count_all_results('', FALSE);
	
		$this->db->limit($length, $start);
		$this->db->order_by($order_by, $order_dir);
	
		$query = $this->db->get();
		$orders = $query->result();
	
		$data = array();
		foreach ($orders as $order) {
			$items_count = count(json_decode($order->items));
			$channel_country = $order->channel . '/' . $order->country;
			$stateClass = $this->getStateClass($order->state);
	
			$actions = "<a class='link-modal-dados-order btn btn-info btn-sm new-button' onclick='setId($order->id)' href='" . base_url() . "order-details/$order->id' data-bs-toggle='modal' data-bs-target='#modal-dados-order'>Detalhes</a>";
	
			$allowedChannels = ['pccomp', 'pccompes', 'carrefour', 'pixmania'];

			if (in_array($order->channel, $allowedChannels)) {
				if ($order->isinvoice) {
					$actions .= "<a target='_blank' class='link-modal-dados-order btn btn-success btn-sm new-button' style='font-size: 10px;' href='https://prd-mkp.bighub.store/invoices/$order->id_invoice.pdf'>Ver Fatura</a>";
				} else {
					$actions .= "<div id='invoice-loading-$order->id' class='spinner-border esconder' role='status' style='width: 15px; height: 15px;'>
									<span class='sr-only'>Loading...</span>
								 </div>
								 <a id='see-invoice-$order->id' target='_blank' class='link-modal-dados-order btn btn-success btn-sm new-button esconder' style='font-size: 10px;'>Ver Fatura</a>
								 <button id='generate-invoice-$order->id' class='link-modal-dados-order btn btn-primary btn-sm new-button' onclick='generateInvoice($order->id)' style='font-size: 10px;'>Gerar Fatura</button>";
				}
			}
	
			$data[] = array(
				"id" => $order->id,
				"reference" => $order->reference,
				"created_date" => date("d/m/Y", strtotime($order->created_date)),
				"items_count" => $items_count,
				"channel_country" => $channel_country,
				"user_id" => $order->user_id,
				"state" => "<span class='$stateClass'>$order->state</span>",
				"total_price" => $order->total_price,
				"attachment" => $order->attachment,
				"validate_invoice" => $order->invoice_validate ? true : false,
				"actions" => $actions
			);
		}
	
		$result = array(
			"draw" => $draw,
			"recordsTotal" => $this->get_total_orders(),
			"recordsFiltered" => $totalFiltered,
			"data" => $data
		);
	
		echo json_encode($result);
	}
	
	
}
