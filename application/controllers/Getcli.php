<?php

defined('BASEPATH') OR exit('No direct script access allowed');

class <PERSON><PERSON><PERSON> extends MY_Controller {

	public function __construct() {
		parent::__construct();
		$this->load->model('Getcli_model');
	}

	public function listar_clientes() 
	{
		$data['email_usuario'] = $this->session->userdata('email_usuario');
		$data['codigo_usuario'] = $this->session->userdata('token_usuario');
		$data['nome_usuario'] = $this->session->userdata('nome_usuario');
		
		$this->ancoradb->select('*');
		$this->ancoradb->from('tbl_getcli');
		$data['clientesPesca'] = $this->ancoradb->get()->result();

		$data['listar_clientes_ancora'] = true;
		$data['view'] = 'getcli/listar_clientes';
		$this->load->view('includes/template', $data);
	}


	public function cliente_contactado($id = null)
	{
		if ($this->Getcli_model->salvar_edicao($id)) {
			redirect('listar-clientes-ancora');
		}
	}
}
