<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Marketplace extends MY_Controller
{

	public function __construct()
	{
		parent::__construct();
	}

	public function filter_channel()
	{
		$data = $this->_getDataDashboard();

		$ini_date = date_human_to_mysql($this->input->get('ini_date'));
		$end_date = date_human_to_mysql($this->input->get('end_date'));
		$channel = $this->input->get('channel');

		$data['datas'] = [
			'data_ini' => date_mysql_to_human($ini_date),
			'data_fim' => date_mysql_to_human($end_date),
			'texto_data' => date_mysql_to_human($ini_date).' a '.date_mysql_to_human($end_date)
		];

		if(isset($channel) && !empty($channel)){
			$data['channel'] = strtoupper($channel);
		}

		$states = ['RECEIVED', 'SHIPPING', 'SHIPPED', 'CLOSED'];
		$vendas_geral = $this->get_orders_by_channel($channel, $ini_date, $end_date, $states);
		$groupedData = $this->groupAndSummarizeData($vendas_geral['result']);

		foreach ($groupedData as $key1 => $subArray) {
			foreach ($subArray as $key2 => $dt) {
				$newArray[] = [
					'channel' => $key1 . '-' . $key2,
					'data' => $dt
				];
			}
		}
		
		$data['vendas_geral'] = $newArray;
	
		$states = ['CANCELED'];
		$canceladas = $this->get_orders_by_channel($channel, $ini_date, $end_date, 'CANCELED');
		$groupedDataCanceled = $this->groupAndSummarizeData($canceladas['result']);
		$data['canceladas'] = $groupedDataCanceled;

		
		$total_geral_vendas = 0;
		$total_geral_comissoes = 0;
		$total_recebimento_mkt = 0;
		$quantidade_itens = 0;

		foreach ($newArray as $channel){
			$total_geral_vendas += $channel['data']['total_price'];
			$total_geral_comissoes += $channel['data']['total_commission'];
			$total_recebimento_mkt += $channel['data']['a_receber'];
			$quantidade_itens += $channel['data']['count'];
		}

		$data['total_geral'] = [
			'total_geral_vendas' => round($total_geral_vendas,2),
			'total_geral_comissoes' => round($total_geral_comissoes,2),
			'total_recebimento_mkt' => round($total_recebimento_mkt,2),
			'quantidade_itens' => round($quantidade_itens,2)
		];


		// echo json_encode($data);
		// die();

		$data['view'] = 'marketplace/total_marketplace';
		$this->load->view('includes/template', $data);
	}

	private function groupAndSummarizeData($vendas_geral) {
		$groupedData = [];
	
		foreach ($vendas_geral as $entry) {
			if (isset($entry->channel) && isset($entry->country) && isset($entry->total_price) && isset($entry->total_commission)) {
				$channel = $entry->channel;
				$country = $entry->country;
				$totalPrice = floatval($entry->total_price);
				$totalCommission = floatval($entry->total_commission);
				$totalAreceber = floatval($entry->total_price - $entry->total_commission);

				if (!isset($groupedData[$channel][$country])) {
					$groupedData[$channel][$country] = [
						"total_price" => 0,
						"total_commission" => 0,
						"a_receber" => 0,
						"count" => 0,
					];
				}
	
				$groupedData[$channel][$country]["total_price"] += $totalPrice;
				$groupedData[$channel][$country]["total_commission"] += $totalCommission;
				$groupedData[$channel][$country]["a_receber"] += $totalAreceber;
				$groupedData[$channel][$country]["count"]++;
			}
		}
	
		return $groupedData;
	}
	
	private function get_orders_by_channel($channel = null, $ini_date, $end_date, $states, $country = null, $limit = null)
	{
		$this->db->select('id, user_id, reference, state, channel, country, total_price, total_commission, shipping_time, created_date, purchased');
		$this->db->where('created_date >=', $ini_date . ' 00:00:00');
		$this->db->where('created_date <=', $end_date . ' 23:59:59');

		if ($channel != null) {
			$this->db->where('channel', $channel);
		}

		if ($country != null) {
			$this->db->where('country', $country);
		}

		if ($states != null) {
			$this->db->where_in('state', $states);
		}

		if ($limit != null) {
			$this->db->limit($limit);
		}

		$this->db->order_by('id', 'desc');
		$result = $this->db->get('tbl_orders')->result();

		# Somando todos os valores 
		$totalPrice = 0;
		$totalCommission = 0;

		foreach ($result as $item) {
			$totalPrice += floatval($item->total_price);
			$totalCommission += floatval($item->total_commission);
		}

		$data = [
			'result' => $result,
			'total' => round($totalPrice, 2),
			'comission_mkt' => round($totalCommission, 2),
			'a_receber' => round($totalPrice - $totalCommission, 2),
			'count'  => count($result)
		];

		return $data;
	}

	public function marketplaces_filter()
	{

		$data = $this->_getDataDashboard();

		// $this->db->select('id, name');
		// $all_clients = $this->db->get('tbl_user_clients')->result();
		// $data['clients'] = $all_clients;

		// echo json_encode($all_clients);
		// die();

		$data['view'] = 'marketplace/marketplaces_filter';
		$this->load->view('includes/template', $data);
	}

	public function values_marketplaces()
	{

		$data = $this->_getDataDashboard();

		$this->db->select('id, name');
		$all_marketplaces = $this->db->get('tbl_marketplaces')->result();
		$data['marketplaces'] = $all_marketplaces;

		$resp = $this->db->get('tbl_marketplace_received')->result();
		$data['payments'] = $resp;

		$data['view'] = 'marketplace/values_marketplaces';
		$this->load->view('includes/template', $data);
	}

	public function insert_marketplace_receipt()
	{
		$id_marketplace = $this->input->post('marketplace');

		$this->db->where('id', $id_marketplace);
		$marketplace = $this->db->get('tbl_marketplaces')->row();

		$data_insert = [
			'id_marketplace' => $id_marketplace,
			'cod_marketplace' => $marketplace->bulk_column_csv ?? null,
			'amount_received' => floatval(str_replace(',', '.', str_replace('.', '', $this->input->post('value')))),
			'orders' => $this->input->post('ordens'),
			'date_received' => date_human_to_mysql($this->input->post('date'))
		];

		// echo json_encode($data_insert);
		// die();

		if($this->db->insert('tbl_marketplace_received', $data_insert)){
			redirect('values-marketplaces');
		}
	}


	public function cancel_receipt($id)
	{
		$this->db->where('id', $id);
		if($this->db->delete('tbl_marketplace_received')){
			redirect('values-marketplaces');
		}
	}


	
}
