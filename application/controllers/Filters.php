<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Filters extends MY_Controller
{

	public function __construct()
	{
		parent::__construct();
	}

	public function index()
	{
		$data = $this->_getDataDashboard();

		$this->db->select('*');
		$this->db->from('tbl_orders');
		$this->db->order_by('id', 'desc');
		$this->db->limit(5);
		$data['orders'] = $this->db->get()->result();

		// $data += $this->_getRespAction();

		// $data['session'] = 'Orders';
		$data['view'] = 'filter/filters';
		$this->load->view('includes/template', $data);
	}

	public function filter()
	{
		// var_dump('chegou');
		// die();
		$data = $this->_getDataDashboard();

		$ini_date = date_human_to_mysql($this->input->get('ini_date'));
		$end_date = date_human_to_mysql($this->input->get('end_date'));
		$state = $this->input->get('state');

		$filter = $this->input->get('filter');

		if ($filter == 'on') {
			$details_orders =  $this->all_sellers_filter($ini_date, $end_date);
			$data['details_orders'] = $details_orders;

			// echo json_encode($data);
			// die();
			$data['view'] = 'filter/total_sellers';
		} else {
			$seller_id = $this->input->get('seller');

			$data['ini_date'] = date_mysql_to_human($ini_date);
			$data['end_date'] = date_mysql_to_human($end_date);

			if (!isset($state)) {
				$state = ['RECEIVED', 'SHIPPING', 'SHIPPED'];
			}
			$state_canceled = ['CANCELED', 'CLOSED'];


			$total_orders = $this->get_orders_by_seller($seller_id, null, null, $state, null, $ini_date, $end_date);
			$total_orders_canceled = $this->get_orders_by_seller($seller_id, null, null, $state_canceled, null, $ini_date, $end_date);
			$total_orders_all = $this->get_orders_by_seller($seller_id, null, null, null, null, $ini_date, $end_date);

			$worten_pt_filter = $this->get_orders_by_seller($seller_id, 'worten', 'PT', $state, null,  $ini_date, $end_date);
			$worten_es_filter = $this->get_orders_by_seller($seller_id, 'worten', 'ES', $state, null,  $ini_date, $end_date);
			$carrefour_es_filter = $this->get_orders_by_seller($seller_id, 'carrefour', 'ES', $state, null, $ini_date, $end_date);
			$fnac_pt_filter = $this->get_orders_by_seller($seller_id, 'fnac', 'PT', $state, null,  $ini_date, $end_date);
			$fnac_es_filter = $this->get_orders_by_seller($seller_id, 'fnac', 'PT', $state, null,  $ini_date, $end_date);
			$mediamarket_filter = $this->get_orders_by_seller($seller_id, 'mediamarkt', 'ES', $state, null,  $ini_date, $end_date);

			$data['total_orders'] = $total_orders;
			$data['total_orders_canceled'] = $total_orders_canceled;
			$data['total_orders_all'] = $total_orders_all;

			$data['worten_pt_filter'] = $worten_pt_filter;
			$data['worten_es_filter'] = $worten_es_filter;
			$data['carrefour_es_filter'] = $carrefour_es_filter;
			$data['fnac_pt_filter'] = $fnac_pt_filter;
			$data['fnac_es_filter'] = $fnac_es_filter;
			$data['mediamarket_filter'] = $mediamarket_filter;


			# Vai buscar os dados do vendedor
			$seller = $this->_getSeller($seller_id);
			$data['seller'] = $seller;

			############# Tratamento valores BIGHUB - SELLER - CUSTOMER #############

			$orders = $total_orders['result'];

			# Atraves do IBAN identifico a origem do vendedor
			$seller_country = $seller->bank_number ? substr($seller->bank_number, 0, 2) : 'Error';

			# Se não existir um IBAN ele vai tentar buscar através do NIF
			if ($seller_country == 'Error' || $seller_country != 'PT' || $seller_country != 'ES') {
				if ($this->_verificarLetra($seller->nif)) {
					$seller_country = 'ES';
				} else {
					$seller_country = 'PT';
				}
			}
			$data['pais_vendedor'] = $seller_country;


			$total_valor_de_repasse_ao_vendedor = 0;
			$total_valor_de_repasse_ao_vendedor_15p = 0;
			$soma_dos_ivas_vies = 0;
			$soma_dos_ivas = 0;

			// echo json_encode($orders);
			// die();

			if (isset($orders) && !empty($orders)) {
				foreach ($orders as $key => $order) {
			
					# Verifica se $order é um objeto ou array
					$user_id = is_object($order) ? $order->user_id : $order['user_id'];

					# Get seller
					$seller_data = [
						'data_order' => $order,
						'data_seller' => $this->get_seller($user_id),
						'company_seller' => $this->get_company_seller($user_id)
					];
					
	
					# Imposto PT e ES
					$percentual_pt = 23;
					$percentual_es = 21;

					$data_customer = json_decode($order->customer);

					$costumer_country = $data_customer->shipping_address->country;
					
					$bighub_country = 'ES';
					$orders[$key]->origens = [
						'localidade_bighub' => $bighub_country,
						'localidade_vendedor' => $seller_country,
						'localidade_comprador' => $costumer_country,
					];

					#var_dump($order->user_id, $seller_country.'-'.$costumer_country);

					$valor_venda_retirando_a_comissao = $order->total_price - $order->total_commission;
					$orders[$key]->financeiro = [
						'valor_venda' => $order->total_price,
						'comissao_marketplace' => $order->total_commission,
						'valor_venda_retirando_a_comissao' => round($valor_venda_retirando_a_comissao, 2),
					];

					# Aplicando valores no caso da comissao ser 15%
					# Não vamos assumir o valor que vem do marketplace e sim os 15%
					$order_menos_15_por_cento = ($order->total_price * 15) / 100;
					$valor_venda_retirando_a_comissao_bighub_15 = $order->total_price - $order_menos_15_por_cento;
					$orders[$key]->financeiro_com_cobranca_de_15_porcento = [
						'valor_venda' => $order->total_price,
						'comissao_marketplace' => $order->total_commission,
						'comissao_15_por_cento_bighub' => round(($order->total_price * 15) / 100, 2),
						'lucro_bighub_sobre_comissao_15_por_cento' => round(($order->total_price * 15) / 100 - $order->total_commission, 2),
						'valor_venda_retirando_a_comissao_bighub_15' => round($valor_venda_retirando_a_comissao_bighub_15, 2),
					];


					// echo json_encode($order);
					// die();
					// $orders[$key]->financeiro = [
					// 	'remove_5_euros' => false
					// ];

					// echo json_encode($seller_country);
					// die();

					if ($seller_country == 'PT' && $costumer_country == 'ES' ||
						$seller_country == 'PT' && $costumer_country == 'SPAIN' ||
						$seller_country == 'PT' && $costumer_country == 'España' ||
						$seller_country == 'PT' && $costumer_country == 'FR' ||
						$seller_country == 'PT' && $costumer_country == 'France' ||
						$seller_country == 'PT' && $costumer_country == 'IT' ||
						$seller_country == 'PT' && $costumer_country == 'BE' ||
						$costumer_country == 'IT') {

							
						# Se o vendedor PT enviar para PT será cobrado 23%
						$total_do_iva = ($valor_venda_retirando_a_comissao * $percentual_pt) / 100;
						$total_menos_iva_23 = $valor_venda_retirando_a_comissao - $total_do_iva;

						if ($order->state != 'CLOSED') {
							$soma_dos_ivas_vies += $total_do_iva;
							$soma_dos_ivas += $total_do_iva;
						}

						# if cliente marcado aplicar os 5
						$orders[$key]->financeiro += [
							'remove_5_euros' => true #true
						];

						$orders[$key]->financeiro += [
							'valor_cobrado_de_iva' => round($total_do_iva, 2),
							'valor_retirando_o_iva_23' => round($total_menos_iva_23, 2),
							'valor_de_repasse_ao_vendedor' => round($total_menos_iva_23, 2),
							'tipo_iva' => 'Vies'
						];

						# if cliente marcado aplicar os 5
						$total_valor_de_repasse_ao_vendedor += $total_menos_iva_23 - 5;
						#$total_valor_de_repasse_ao_vendedor += $total_menos_iva_23;

						$total_do_iva_15 = round(($valor_venda_retirando_a_comissao_bighub_15 * $percentual_pt) / 100, 2);
						$total_menos_iva_23_15 = $valor_venda_retirando_a_comissao_bighub_15 - $total_do_iva_15;
						$orders[$key]->financeiro_com_cobranca_de_15_porcento += [
							'valor_cobrado_de_iva_15p' => $total_do_iva_15,
							'valor_retirando_o_iva_23_15p' => round($total_menos_iva_23_15, 2),
							'valor_de_repasse_ao_vendedor_15p' => round($total_menos_iva_23_15, 2)
						];

						$total_valor_de_repasse_ao_vendedor_15p += $total_menos_iva_23_15;
						$lucro_no_repasse_bighub = $total_valor_de_repasse_ao_vendedor - $total_valor_de_repasse_ao_vendedor_15p;
					}

					// var_dump($seller_country, $costumer_country);

					if (
						$seller_country == 'ES' && $costumer_country == 'SPAIN' ||
						$seller_country == 'ES' && $costumer_country == 'España' ||
						$seller_country == 'ES' && $costumer_country == 'ES' ||
						$seller_country == 'ES' && $costumer_country == 'PT' ||
						$seller_country == 'ES' && $costumer_country == 'FR' ||
						$seller_country == 'PT' && $costumer_country == 'PT'
					) {

						// var_dump('entra');
						// die();

						# Se o vendedor PT enviar para PT será cobrado 23%
						$total_do_iva = ($valor_venda_retirando_a_comissao * $percentual_pt) / 100;
						$total_menos_iva_21 = $valor_venda_retirando_a_comissao - $total_do_iva;

						$soma_dos_ivas_vies += 0;
						$soma_dos_ivas += $total_do_iva;

						$orders[$key]->financeiro += [
							'remove_5_euros' => false #true
						];

						$orders[$key]->financeiro += [
							'apresenta_valor_venda_sem_iva_21' => round($total_menos_iva_21, 2),
							'valor_cobrado_de_iva' => round($total_do_iva, 2),
							'valor_de_repasse_ao_vendedor' => round($valor_venda_retirando_a_comissao, 2),
							'tipo_iva' => 'Total'
						];

						$total_valor_de_repasse_ao_vendedor += $valor_venda_retirando_a_comissao;

						$total_do_iva_15 = round(($valor_venda_retirando_a_comissao_bighub_15 * $percentual_es) / 100, 2);
						$total_menos_iva_21_15 = $valor_venda_retirando_a_comissao_bighub_15 - $total_do_iva_15;
						$orders[$key]->financeiro_com_cobranca_de_15_porcento += [
							'apresenta_valor_venda_sem_iva_15p' => round($total_menos_iva_21_15, 2),
							'apresenta_valor_cobrado_de_iva_21_15p' => $total_do_iva_15,
							'apresenta_valor_de_repasse_ao_vendedor' => round($valor_venda_retirando_a_comissao_bighub_15, 2)
						];

						$total_valor_de_repasse_ao_vendedor_15p += $total_menos_iva_21_15;
						$lucro_no_repasse_bighub = $total_valor_de_repasse_ao_vendedor - $total_valor_de_repasse_ao_vendedor_15p;
					}

					
					$data['calculos_de_valores_geral'] = [
						'comissao_15_por_cento' => ($total_orders['total_price'] * 15) / 100,
						'soma_dos_ivas' => $soma_dos_ivas,
						'soma_dos_ivas_vies' => $soma_dos_ivas_vies,
						'repasse_ao_vendedor' => round($total_valor_de_repasse_ao_vendedor, 2),
						'repasse_ao_vendedor_15p' => round($total_valor_de_repasse_ao_vendedor_15p, 2),
						'lucro_bighub' => round($lucro_no_repasse_bighub, 2)
					];

					// if($order->guid === '98a8cfac-da30-11ee-9406-56000431c7b4'){
					// 	echo json_encode($orders[$key]);
					// 	die();
					// }

				}
				echo json_encode($orders);
				die();
			} else {
				$data['total_orders'] = [
					'result' => [],
					'total_price' => 0.00,
					'total_commission' => 0.00,
					'count'  => 0
				];

				$data['calculos_de_valores_geral'] = [
					'comissao_15_por_cento' => 0.00,
					'soma_dos_ivas' => 0.00,
					'soma_dos_ivas_vies' => 0.00,
					'repasse_ao_vendedor' => 0.00,
					'repasse_ao_vendedor_15p' => 0.00,
					'lucro_bighub' => 0.00
				];
			}
			
			$data['view'] = 'filter/filters';
		}



		$this->load->view('includes/template', $data);
		// echo json_encode($data);
		// die();
	}


	public function get_seller($user_id)
	{
		$this->db->where('id', $user_id);
		return $this->db->get('tbl_user_clients')->row(); // Retorna um objeto PHP
	}
	
	public function get_company_seller($user_id)
	{
		$this->db->where('client_id', $user_id);
		return $this->db->get('tbl_company')->row(); // Retorna um objeto PHP
	}
	

	public function all_sellers_filter($ini_date, $end_date)
	{
		$resp = array();

		$resp['datas'] = [
			'data_ini' => date_mysql_to_human($ini_date),
			'data_fim' => date_mysql_to_human($end_date),
			'texto_data' => date_mysql_to_human($ini_date).' a '.date_mysql_to_human($end_date)
		];

		$this->db->distinct();
		$this->db->select('user_id');
		$this->db->from('tbl_orders'); // Substitua pelo nome da sua tabela de ordens
		$this->db->where('created_date >=', $ini_date . ' 00:00:00');
		$this->db->where('created_date <=', $end_date . ' 23:59:59');
		$query = $this->db->get();
		$result = $query->result();

		foreach ($result as $row) {
			$seller_id = $row->user_id;
			$values = $this->motor_calculo_e_busca($seller_id, $ini_date, $end_date);
			$resp['orders'][] = $values;
		}

		$total_geral_vendas = 0;
		$total_geral_comissoes = 0;
		$total_recebimento_mkt = 0;
		$total_iva_normal = 0;
		$total_iva_vies = 0;
		$total_ja_pago_aos_vendedores = 0;
		$total_repasse_ao_seller = 0;
		$total_pendente_de_pagamento = 0;
		$total_5_euros_descontados = 0;

		foreach ($resp['orders'] as $seller){
			$total_geral_vendas += $seller['total_valor_venda'];
			$total_geral_comissoes += $seller['total_das_comissoes'];
			$total_recebimento_mkt += $seller['valor_venda_retirando_a_comissao'];
			$total_iva_normal += $seller['iva_normal'];
			$total_iva_vies += $seller['iva_vies'];
			$total_ja_pago_aos_vendedores += $seller['total_ja_pago'];
			$total_repasse_ao_seller += $seller['repasse_ao_seller'];
			$total_pendente_de_pagamento += $seller['falta_pagar'];
			$total_5_euros_descontados += $seller['somas_5_euros'];
		}

		$resp['total_geral'] = [
			'total_geral_vendas' => round($total_geral_vendas,2),
			'total_geral_comissoes' => round($total_geral_comissoes,2),
			'total_recebimento_mkt' => round($total_recebimento_mkt,2),
			'total_iva_normal' => round($total_iva_normal,2),
			'total_iva_vies' => round($total_iva_vies,2),
			'total_ja_pago_aos_vendedores' => round($total_ja_pago_aos_vendedores,2),
			'total_repasse_ao_seller' => round($total_repasse_ao_seller,2),
			'total_pendente_de_pagamento' => round($total_pendente_de_pagamento,2),
			'total_5_euros_descontados' => round($total_5_euros_descontados,2)
		];

		// echo json_encode($resp);
		// die();
		return $resp;

	}

	private function motor_calculo_e_busca($seller_id, $ini_date, $end_date)
	{
			if (!isset($state)) {
				$state = ['RECEIVED', 'SHIPPING', 'SHIPPED'];
			}
			$state_canceled = ['CANCELED', 'CLOSED'];

			$total_orders = $this->get_orders_by_seller($seller_id, null, null, $state, null, $ini_date, $end_date);
			$total_orders_canceled = $this->get_orders_by_seller($seller_id, null, null, $state_canceled, null, $ini_date, $end_date);
			#$total_orders_all = $this->get_orders_by_seller($seller_id, null, null, null, null, $ini_date, $end_date);

			$data['total_orders'] = $total_orders;
			$data['total_orders_canceled'] = $total_orders_canceled;
			#$data['total_orders_all'] = $total_orders_all;

			# Vai buscar os dados do vendedor
			$seller = $this->_getSeller($seller_id);
			$data['seller'] = $seller;

			############# Tratamento valores BIGHUB - SELLER - CUSTOMER #############

			$orders = $total_orders['result'];

			# Atraves do IBAN identifico a origem do vendedor
			$seller_country = $seller->bank_number ? substr($seller->bank_number, 0, 2) : 'Error';

			# Se não existir um IBAN ele vai tentar buscar através do NIF
			if ($seller_country == 'Error' || $seller_country != 'PT' || $seller_country != 'ES') {
				if ($this->_verificarLetra($seller->nif)) {
					$seller_country = 'ES';
				} else {
					$seller_country = 'PT';
				}
			}
			$data['pais_vendedor'] = $seller_country;

			$total_valor_de_repasse_ao_vendedor = 0;
			$soma_dos_ivas_vies = 0;
			$soma_dos_ivas = 0;

			if (isset($orders) && !empty($orders)) {
				foreach ($orders as $key => $order) {

					$validate1 = false;
					$validate2 = false;
				
					#echo json_encode($order);

					# Imposto PT e ES
					$percentual_pt = 23;
					$percentual_es = 21;

					$data_customer = json_decode($order->customer);

					$costumer_country = $data_customer->shipping_address->country;

					#var_dump($costumer_country);

					$bighub_country = 'ES';
					$orders[$key]->origens = [
						'localidade_bighub' => $bighub_country,
						'localidade_vendedor' => $seller_country,
						'localidade_comprador' => $costumer_country,
					];

					$valor_venda_retirando_a_comissao = $order->total_price - $order->total_commission;
					$orders[$key]->financeiro = [
						'valor_venda' => $order->total_price,
						'comissao_marketplace' => $order->total_commission,
						'valor_venda_retirando_a_comissao' => round($valor_venda_retirando_a_comissao, 2),
					];

					#var_dump($order->id, $order->user_id, $seller_country.'-'.$costumer_country);
					#continue;

					// if($order->id == 19717354){
					// 	echo json_encode($orders[$key]);
					// 	die();
					// }

					#var_dump('Entrar no 1');

					if ($seller_country == 'PT' && $costumer_country == 'ES' ||
						$seller_country == 'PT' && $costumer_country == 'SPAIN' ||
						$seller_country == 'PT' && $costumer_country == 'España' ||
						$seller_country == 'PT' && $costumer_country == 'FR' ||
						$seller_country == 'PT' && $costumer_country == 'France' ||
						$seller_country == 'PT' && $costumer_country == 'IT' ||
						$seller_country == 'PT' && $costumer_country == 'BE' ||
						$costumer_country == 'IT') {

						# Se o vendedor PT enviar para PT será cobrado 23%
						$total_do_iva = ($valor_venda_retirando_a_comissao * $percentual_pt) / 100;
						$total_menos_iva_23 = $valor_venda_retirando_a_comissao - $total_do_iva;

						if ($order->state != 'CLOSED') {
							$soma_dos_ivas_vies += $total_do_iva;
							$soma_dos_ivas += $total_do_iva;
						}

						# if cliente marcado aplicar os 5
						$orders[$key]->financeiro += [
							'remove_5_euros' => true, #true
							'iva_vies' => true
						];

						$orders[$key]->financeiro += [
							'valor_cobrado_de_iva' => round($total_do_iva, 2),
							'valor_retirando_o_iva' => round($total_menos_iva_23, 2),
							'texto_detalhe_repasse' => round($total_menos_iva_23, 2).' - 5 = '.round($total_menos_iva_23 - 5, 2),
							'valor_de_repasse_ao_vendedor' => round($total_menos_iva_23 - 5, 2),
							'tipo_iva' => 'Vies'
						];

						$total_valor_de_repasse_ao_vendedor += $total_menos_iva_23 - 5;
						#var_dump('1 if ok');
						$validate1 = true;
					}

					#var_dump('Entrar no 2');

					if (
						$seller_country == 'ES' && $costumer_country == 'España' ||
						$seller_country == 'ES' && $costumer_country == 'SPAIN' ||
						$seller_country == 'ES' && $costumer_country == 'ES' ||
						$seller_country == 'ES' && $costumer_country == 'PT' ||
						$seller_country == 'ES' && $costumer_country == 'FR' ||
						$seller_country == 'PT' && $costumer_country == 'PT'
					) {

						# Se o vendedor PT enviar para PT será cobrado 23%
						$total_do_iva = ($valor_venda_retirando_a_comissao * $percentual_pt) / 100;
						$total_menos_iva_21 = $valor_venda_retirando_a_comissao - $total_do_iva;

						$soma_dos_ivas_vies += 0;
						$soma_dos_ivas += $total_do_iva;

						$orders[$key]->financeiro += [
							'remove_5_euros' => false, #true
							'iva_vies' => false
						];

						$orders[$key]->financeiro += [
							'apresenta_valor_venda_sem_iva_21' => round($total_menos_iva_21, 2),
							'valor_cobrado_de_iva' => round($total_do_iva, 2),
							'valor_venda_retirando_a_comissao' => round($valor_venda_retirando_a_comissao, 2),
							'texto_detalhe_repasse' => round($total_menos_iva_21, 2).' + '.round($total_do_iva, 2).' = '.round($valor_venda_retirando_a_comissao, 2),
							'valor_de_repasse_ao_vendedor' => round($valor_venda_retirando_a_comissao, 2),
							'tipo_iva' => 'Total'
						];

						$total_valor_de_repasse_ao_vendedor += $valor_venda_retirando_a_comissao;
						#var_dump('2 if ok, foi');
						
						$validate2 = true;
					}

					if(!$validate1 && !$validate2){
						echo 'Error na ordem de número: '.$order->id;
					}
				
					// if($order->id == 19672660){
					// 	echo json_encode($orders[$key]);
					// 	die();
					// }

					// if(!isset($orders[$key]->financeiro->valor_cobrado_de_iva)){
					// 	echo json_encode($order->id);
					// 	die();
					// }
					
					
				}



				$total_valor_venda = 0;
				$total_das_comissoes = 0;
				$valor_venda_retirando_a_comissao = 0;
				$iva_normal = 0;
				$iva_vies = 0;
				$repasse_ao_seller = 0;
				$total_ja_pago = 0;
				$cinco_euros = 0;

				# Preparando a resposta com todos os dados neceessários para cada vendedor.
				foreach ($orders as $key => $order) {

					// if($order->id == 19666121){
					// 	echo json_encode($order->financeiro['valor_cobrado_de_iva']);
					// 	//die();
					// }

					// if(!isset($order->financeiro['valor_cobrado_de_iva'])){
					// 	echo json_encode($order->id);
					// 	die();
					// }
					$total_valor_venda += $order->financeiro['valor_venda'];
					$total_das_comissoes += $order->financeiro['comissao_marketplace'];
					$valor_venda_retirando_a_comissao += $order->financeiro['valor_venda_retirando_a_comissao'];
					$iva_normal += $order->financeiro['valor_cobrado_de_iva'];
					$repasse_ao_seller += $order->financeiro['valor_de_repasse_ao_vendedor'];

					if($order->paid == 1){
						$total_ja_pago += $order->financeiro['valor_de_repasse_ao_vendedor'];
					}

					if($order->financeiro['iva_vies'] == true){
						$iva_vies += $order->financeiro['valor_cobrado_de_iva'];
						$cinco_euros += 5;
					}
				}

				$resp = [
					'id_vendedor' => $seller->id_seller,
					'nome_vendedor' => $seller->name,
					'quantidade_ordens_canceladas' => $total_orders_canceled['count'],
					'total_em_ordens_canceladas' => round($total_orders_canceled['total_price'], 2),
					'quantidade_ordens_sem_canceladas' => count($orders),
					'total_valor_venda' => round($total_valor_venda, 2),
					'total_das_comissoes' => round($total_das_comissoes, 2),
					'valor_venda_retirando_a_comissao' =>  round($valor_venda_retirando_a_comissao, 2),
					'iva_normal' =>  round($iva_normal, 2),
					'iva_vies' =>  round($iva_vies, 2),
					'repasse_ao_seller' =>  round($repasse_ao_seller, 2),
					'total_ja_pago' =>  round($total_ja_pago, 2),
					'falta_pagar' =>  round($repasse_ao_seller - $total_ja_pago, 2),
					'somas_5_euros' => $cinco_euros
				];

			} else {
				$resp = [
					'id_vendedor' => $seller->id_seller,
					'nome_vendedor' => $seller->name,
					'quantidade_ordens_canceladas' => 0,
					'total_em_ordens_canceladas' => 0,
					'quantidade_ordens_sem_canceladas' => 0,
					'total_valor_venda' => 0,
					'total_das_comissoes' => 0,
					'valor_venda_retirando_a_comissao' =>  0,
					'iva_normal' => 0,
					'iva_vies' =>  0,
					'repasse_ao_seller' =>  0,
					'total_ja_pago' =>  0,
					'falta_pagar' =>  0,
					'somas_5_euros' => 0
				];
			}
			// echo json_encode($data);
			// die();

			return $resp;


	}

	public function filter_month()
	{
		$data = $this->_getDataDashboard();

		$ini_date = $this->input->get('ini_date');
		$end_date = $this->input->get('end_date');
		$channel = $this->input->get('channel');

		$data['ini_date'] = date_mysql_to_human($ini_date);
		$data['end_date'] = date_mysql_to_human($end_date);


		if(isset($channel) && !empty($channel)){
			$data['channel'] = strtoupper($channel);
		}

		$states = ['RECEIVED', 'SHIPPING', 'SHIPPED', 'CLOSED'];
		$vendas_geral = $this->get_orders_by_channel($channel, $ini_date, $end_date, $states);
		$groupedData = $this->groupAndSummarizeData($vendas_geral['result']);
		$data['vendas_geral'] = $groupedData;

		$states = ['CANCELED'];
		$canceladas = $this->get_orders_by_channel($channel, $ini_date, $end_date, 'CANCELED');
		$groupedDataCanceled = $this->groupAndSummarizeData($canceladas['result']);
		$data['canceladas'] = $groupedDataCanceled;


		// echo json_encode($data);
		// die();

		$data['view'] = 'filter/filters_channel';
		$this->load->view('includes/template', $data);
		// echo json_encode($data);
		// die();

	}

	public function groupAndSummarizeData($vendas_geral) {
		$groupedData = [];

		foreach ($vendas_geral as $entry) {
			if (isset($entry->channel) && isset($entry->country) && isset($entry->total_price) && isset($entry->total_commission)) {
				$channel = $entry->channel;
				$country = $entry->country;
				$totalPrice = floatval($entry->total_price);
				$totalCommission = floatval($entry->total_commission);

				if (!isset($groupedData[$channel][$country])) {
					$groupedData[$channel][$country] = [
						"total_price" => 0,
						"total_commission" => 0,
						"count" => 0,
					];
				}

				$groupedData[$channel][$country]["total_price"] += $totalPrice;
				$groupedData[$channel][$country]["total_commission"] += $totalCommission;
				$groupedData[$channel][$country]["count"]++;
			}
		}

		return $groupedData;
	}

	private function get_orders_by_seller($seller_id, $channel = null, $country = null, $state = null, $limit = null, $ini_date, $end_date)
	{

		$this->db->select('to.id, to.guid, to.user_id, to.reference, to.state, to.channel, to.country, to.total_price, to.total_commission, to.shipping_time, to.created_date, to.purchased, to.customer, op.paid, op.payment_date, op.payment_obs as obs');

		$this->db->where('to.created_date >=', $ini_date . ' 00:00:00');
		$this->db->where('to.created_date <=', $end_date . ' 23:59:59');

		if ($seller_id != null) {
			$this->db->where('to.user_id', $seller_id);
		}

		if ($channel != null) {
			$this->db->where('to.channel', $channel);
		}

		if ($country != null) {
			$this->db->where('to.country', $country);
		}

		if ($state != null) {
			$this->db->where_in('to.state', $state);
		}

		if ($limit != null) {
			$this->db->limit($limit);
		}

		$this->db->join('tbl_orders_payment op', 'op.order_guid = to.guid', 'left');
		$this->db->order_by('to.id', 'desc');
		$result = $this->db->get('tbl_orders to')->result();

		# Somando todos os valores
		$totalPrice = 0;
		$totalCommission = 0;
		foreach ($result as $item) {
			$totalPrice += floatval($item->total_price);
			$totalCommission += floatval($item->total_commission);
		}

		$data = [
			'result' => $result,
			'total_price' => round($totalPrice, 2),
			'total_commission' => round($totalCommission, 2),
			'count'  => count($result)
		];

		return $data;
	}

	// private function get_orders_all_seller($seller_id, $channel = null, $country = null, $state = null, $limit = null, $ini_date, $end_date)
	// {

	// 	$this->db->select('to.id, to.guid, to.user_id, to.reference, to.state, to.channel, to.country, to.total_price, to.total_commission, to.shipping_time, to.created_date, to.purchased, to.customer, op.paid, op.payment_date, op.payment_obs as obs');

	// 	$this->db->where('to.created_date >=', $ini_date . ' 00:00:00');
	// 	$this->db->where('to.created_date <=', $end_date . ' 23:59:59');

	// 	if ($seller_id != null) {
	// 		$this->db->where('to.user_id', $seller_id);
	// 	}

	// 	if ($channel != null) {
	// 		$this->db->where('to.channel', $channel);
	// 	}

	// 	if ($country != null) {
	// 		$this->db->where('to.country', $country);
	// 	}

	// 	if ($state != null) {
	// 		$this->db->where_in('to.state', $state);
	// 	}

	// 	if ($limit != null) {
	// 		$this->db->limit($limit);
	// 	}

	// 	$this->db->join('tbl_orders_payment op', 'op.order_guid = to.guid', 'left');
	// 	$this->db->order_by('to.id', 'desc');
	// 	$result = $this->db->get('tbl_orders to')->result();

	// 	# Somando todos os valores
	// 	$totalPrice = 0;
	// 	$totalCommission = 0;
	// 	foreach ($result as $item) {
	// 		$totalPrice += floatval($item->total_price);
	// 		$totalCommission += floatval($item->total_commission);
	// 	}

	// 	$data = [
	// 		'result' => $result,
	// 		'total_price' => round($totalPrice, 2),
	// 		'total_commission' => round($totalCommission, 2),
	// 		'total_menos_comission' => round($totalPrice - $totalCommission, 2),
	// 		'quantity_orders'  => count($result)
	// 	];

	// 	return $data;
	// }

	private function get_orders_by_channel($channel = null, $ini_date, $end_date, $states, $country = null, $limit = null)
	{
		$this->db->select('id, user_id, reference, state, channel, country, total_price, total_commission, shipping_time, created_date, purchased');
		$this->db->where('created_date >=', $ini_date . ' 00:00:00');
		$this->db->where('created_date <=', $end_date . ' 23:59:59');

		if ($channel != null) {
			$this->db->where('channel', $channel);
		}

		if ($country != null) {
			$this->db->where('country', $country);
		}

		if ($states != null) {
			$this->db->where_in('state', $states);
		}

		if ($limit != null) {
			$this->db->limit($limit);
		}

		$this->db->order_by('id', 'desc');
		$result = $this->db->get('tbl_orders')->result();

		# Somando todos os valores
		$totalPrice = 0;
		$totalCommission = 0;

		foreach ($result as $item) {
			$totalPrice += floatval($item->total_price);
			$totalCommission += floatval($item->total_commission);
		}

		$data = [
			'result' => $result,
			'total' => round($totalPrice, 2),
			'comission_mkt' => round($totalCommission, 2),
			'count'  => count($result)
		];

		return $data;
	}

	public function update_order()
	{
		$order_guid = $this->input->get('guidId');
		$obs = $this->input->get('obs');

		$this->db->where('order_guid', $order_guid);
		$query = $this->db->get('tbl_orders_payment');

		if ($query->num_rows() > 0) {
			$data_info = [
				'payment_date' => date('Y-m-d'),
				'payment_obs' => $obs
			];
			$this->db->where('order_guid', $order_guid);
			if ($this->db->update('tbl_orders_payment', $data_info)) {
				$data = [
					'order_guid' => $order_guid,
					'action' => 'update',
					'msg' => 'success'
				];
				echo json_encode($data);
				return;
			}
		} else {
			$data_info = [
				'order_guid' => $order_guid,
				'paid' => 1,
				'payment_date' => date('Y-m-d'),
				'payment_obs' => $obs
			];

			if ($this->db->insert('tbl_orders_payment', $data_info)) {
				$data = [
					'order_guid' => $order_guid,
					'action' => 'insert',
					'msg' => 'success'
				];
				echo json_encode($data);
				return;
			}
		}
	}

	public function remove_payment_order()
	{
		$order_guid = $this->input->get('guidId');

		$this->db->where('order_guid', $order_guid);
		$this->db->delete('tbl_orders_payment');

		$data = [
			'order_guid' => $order_guid,
			'action' => 'remove',
			'msg' => 'success'
		];
		echo json_encode($data);
		return;
	}


	public function seller_filter()
	{

		$data = $this->_getDataDashboard();

		$this->db->select('id, name');
		$all_clients = $this->db->get('tbl_user_clients')->result();
		$data['clients'] = $all_clients;

		// echo json_encode($all_clients);
		// die();

		$data['view'] = 'filter/filter_data';
		$this->load->view('includes/template', $data);
	}



}
