<?php

ini_set('memory_limit', '5120M'); // Defina o valor de memória desejado, como 1024M para 1GB

defined('BASEPATH') or exit('No direct script access allowed');

class Invoices extends 	CI_Controller
{

	const URL_API_MARKTPLACES = 'https://prd-mkp.bighub.store';

	public function __construct()
	{
		parent::__construct();
		$this->load->model('FlowInvoices_model');
	}


	public function list_invoices()
	{
		$data = $this->_getDataDashboard();

		$this->db->select('tc.name as name_seller, tsp.*');
		$this->db->from('tbl_seller_payment_summary tsp');
		$this->db->join('tbl_user_clients tc', 'tc.id = tsp.id_seller', 'left');

		$data['invoices'] = $this->db->get()->result();

		$data += $this->_getRespAction();

		$data['session'] = 'Invoices';
		$data['view'] = 'invoices/list_invoices';
		$this->load->view('includes/template', $data);
	}



	public function gerar_faturas()
	{

		echo ' Iniciou';
		$idOrders = array(
			'4984119-A',
			'4984674-A',
			'4984697-A',
			'4985360-A',
			'4986936-A',
			'4987315-A',
			'4987425-A',
			'4988142-A',
			'4988431-A',
			'4988522-A',
			'4988697-A',
			'4989439-A',
			'4989581-A',
			'4989657-A',
			'4990207-A',
			'4990634-A',
			'4990647-A',
			'4990893-A',
			'4991278-A',
			'4991341-A',
			'4992254-A',
			'4998032-A',
			'4998098-A',
			'4999045-A',
			'4999352-A',
			'4999867-A',
			'4999929-A',
			'5000206-A',
			'5000337-A',
			'5000346-A',
			'5000705-A',
			'5001551-A',
			'5002217-A',
			'5009929-A',
			'5013629-A',
			'4925028-A',
			'4925066-A',
			'4934890-A',
			'4938627-A',
			'4943500-A',
			'4967640-A',
			'4970251-A',
			'4975320-A',
			'4976106-A',
			'4983159-A',
			'4987314-A',
			'4999491-A',
			'5002097-A',
			'5007760-A',
			'5007769-A',
			'5008928-A',
			'5008932-A',
			'5013104-A',
			'5014292-A',
			'5019675-A',
			'5020732-A',
		);

		foreach ($idOrders as $order) {
			$this->generate_invoice($order);
		}
	}


	public function generate_invoice()
	{
		#$data = $this->_getDataDashboard();

		$order_id = $this->input->post('order_id');

		// var_dump($order_id);
		// die();

		// if ($order_id == null) {
		// 	$order_id = $this->input->post('order_id');
		// }

		$this->db->select('*');
		$this->db->where('id', $order_id);
		// $this->db->where('reference', $order_id);
		$order = $this->db->get('tbl_orders')->row();

		// echo json_encode($order);
		// die();

		if (isset($order) && isset($order->reference)) {
			$dataOrder = ["id" => $order->reference];
			//return;
		} else {
			echo "Não encontrada '$order_id' | ";
			return;
		}

		// echo 'FIMMM';
		// die();

		# Get channel da order
		$channel = $order->channel;

		#NIF deafults
		$numeroNIF = '999999999';

		// # PCCOMP & PCOMPES ----------------------------------------------------------
		if ($channel == 'pccomp' || $channel == 'pccompes' || $channel == 'carrefour' || $channel == 'pixmania') {

			if ($channel == 'pccomp' && $order->country == 'ES') {
				$channel = 'pccompes';
			}

			# Vai buscar os dados da order
			$getResponseFirst = $this->curl_get_data_order($dataOrder, $channel);
			if(isset($getResponseFirst) && is_object($getResponseFirst) && $getResponseFirst->resp != 'order_id_not_found'){
				try {
					// Tenta acessar os dados normalmente
					if (isset($getResponseFirst[0]->responseFirst->customer->shipping_address->additional_info)) {
						$additionalInfo = $getResponseFirst[0]->responseFirst->customer->shipping_address->additional_info;
						// Expressão regular melhorada para capturar o NIF após "Nif:"
						if (preg_match('/Nif:\s*([A-Z0-9]+)/i', $additionalInfo, $matches)) {
							$numeroNIF = $matches[1]; // Usar o grupo capturado
						}
					} 
				} catch (Error $e) {
					// Verifica se o erro é do tipo "Cannot use object of type stdClass as array"
					if (strpos($e->getMessage(), 'stdClass as array') !== false) {
						// Converte a resposta para array e tenta novamente
						$getResponseFirst = $this->convertObjectToArray($getResponseFirst);
			
						if (isset($getResponseFirst[0]['responseFirst']['customer']['shipping_address']['additional_info'])) {
							$additionalInfo = $getResponseFirst[0]['responseFirst']['customer']['shipping_address']['additional_info'];
							// Expressão regular melhorada para capturar o NIF após "Nif:"
							if (preg_match('/Nif:\s*([A-Z0-9]+)/i', $additionalInfo, $matches)) {
								$numeroNIF = $matches[1]; // Usar o grupo capturado
							}
						}
					} else {
						// Re-lança a exceção se for um erro diferente
						throw $e;
					}
				}
			}
		}

		# CARREFOUR -  PIXMANIA
		if ($channel == 'carrefour_old' || $channel == 'pixmania_old') {

			# Vai buscar os dados da order
			$getResponseFirst = $this->curl_get_data_order($dataOrder, $channel);
	
			if(isset($getResponseFirst) && is_object($getResponseFirst) && $getResponseFirst->resp != 'order_id_not_found'){
				try {
					// Tenta acessar os dados normalmente
					if (isset($getResponseFirst[0]->responseFirst->customer->shipping_address->additional_info)) {
						$additionalInfo = $getResponseFirst[0]->responseFirst->customer->shipping_address->additional_info;
						if (preg_match('/\d+[A-Za-z]*/', $additionalInfo, $matches)) {
							$numeroNIF = $matches[0];
						}
					}
				} catch (Error $e) {
					// Verifica se o erro é do tipo "Cannot use object of type stdClass as array"
					if (strpos($e->getMessage(), 'stdClass as array') !== false) {
						// Converte a resposta para array e tenta novamente
						$getResponseFirst = $this->convertObjectToArray($getResponseFirst);
	
						if (isset($getResponseFirst[0]['responseFirst']['customer']['shipping_address']['additional_info'])) {
							$additionalInfo = $getResponseFirst[0]['responseFirst']['customer']['shipping_address']['additional_info'];
							if (preg_match('/\d+[A-Za-z]*/', $additionalInfo, $matches)) {
								$numeroNIF = $matches[0];
							}
						}
					} else {
						// Re-lança a exceção se for um erro diferente
						throw $e;
					}
				}
			}
			
		}

		# FNAC ----------------------------------------------------------
		if ($channel == 'fnac') {
			$channel = 'fnac';
			if ($order->country == 'ES') {
				$channel = 'fnaces';
			}

			# Vai buscar os dados da order
			$getResponseFirst = $this->curl_get_data_order($dataOrder, $channel);
			if (isset($getResponseFirst[0]->responseFirst->billing_address->nif)) {
				if (preg_match('/\d+[A-Za-z]*/', $getResponseFirst[0]->responseFirst->billing_address->nif, $matches)) {
					$numeroNIF = $matches[0];
				}
			}
		}


		# CARREFOUR ----------------------------------------------------------
		if ($channel == 'teste') {
			# Vai buscar os dados da order
			$getResponseFirst = $this->curl_get_data_order($dataOrder, $channel);

			echo json_encode($getResponseFirst);
			die();
			if (isset($getResponseFirst[0]->responseFirst->billing_address->nif)) {
				if (preg_match('/\d+[A-Za-z]*/', $getResponseFirst[0]->responseFirst->billing_address->nif, $matches)) {
					$numeroNIF = $matches[0];
				}
			}
		}

		# LEROYMERLIN ----------------------------------------------------------
		if ($channel == 'leroymerlin') {
			# Vai buscar os dados da order
			$getResponseFirst = $this->curl_get_data_order($dataOrder, $channel);

			echo json_encode($getResponseFirst);
			die();
			if (isset($getResponseFirst[0]->responseFirst->billing_address->nif)) {
				if (preg_match('/\d+[A-Za-z]*/', $getResponseFirst[0]->responseFirst->billing_address->nif, $matches)) {
					$numeroNIF = $matches[0];
				}
			}
		}

		# MEDIAMARKT ----------------------------------------------------------
		if ($channel == 'mediamarkt') {
			# Vai buscar os dados da order
			$getResponseFirst = $this->curl_get_data_order($dataOrder, $channel);
			if (isset($getResponseFirst[0]->responseFirst->order_additional_fields[0]->value)) {
				if (preg_match('/\d+[A-Za-z]*/', $getResponseFirst[0]->responseFirst->order_additional_fields[0]->value, $matches)) {
					$numeroNIF = $getResponseFirst[0]->responseFirst->order_additional_fields[0]->value; # $matches[0];
				}
			}
		}

		# WORTEN ----------------------------------------------------------
		if ($channel == 'worten') {
			# Vai buscar os dados da order
			$getResponseFirst = $this->curl_get_data_order($dataOrder, $channel);

			echo json_encode($getResponseFirst);
			die();
			if (isset($getResponseFirst[0]->responseFirst->billing_address->nif)) {
				if (preg_match('/\d+[A-Za-z]*/', $getResponseFirst[0]->responseFirst->billing_address->nif, $matches)) {
					$numeroNIF = $matches[0];
				}
			}
		}

		$customer = json_decode($order->customer);
		$products_order = json_decode($order->items);

		$itens = count($products_order);
		if ($itens > 1) {
			var_dump('Order de numero -' . $order_id . ' tem mais de um item');
			return;
		}

		$products = [];
		foreach ($products_order as $prod) {
			$products = [
				'reference' => $prod->ean,
				'title' => $prod->title,
				'price' => $this->remove21porcento($order->total_price)
			];
		}

		if (isset($order) && $order != null) {
			$payload = [
				"user" => $order->user_id,
				"vat" => $numeroNIF,
				"number" => 'BIGHUB-' . $order->id,
				"name" => $customer->first_name . ' ' . $customer->last_name,
				"address" => isset($customer->billing_address->street_1) ? 
					($customer->billing_address->street_1 . ' ' . ($customer->billing_address->street_2 ?? '')) : 
					($customer->shipping_address->street_1 . ' ' . ($customer->shipping_address->street_2 ?? '')),
				"city" => isset($customer->billing_address->city) ? 
					$customer->billing_address->city : 
					$customer->shipping_address->city,
				"zipCode" => isset($customer->billing_address->zip_code) ? 
					$customer->billing_address->zip_code : 
					$customer->shipping_address->zip_code,
				"country" => isset($customer->billing_address->country) ? 
					$customer->billing_address->country : 
					$customer->shipping_address->country,
				"products" => [
					$products
				]
			];

			// echo json_encode($payload);
			// die();

			$resp = $this->curl_send_data_moloni($payload);

			$author = [
				'user_name' => $this->session->userdata('nome_usuario'),
				'user_code' => $this->session->userdata('token_usuario'),
				'date_hour' => date('Y-m-d H:m:i'),
				'origin' => 'Thor'
			];

			$tbl_data = [
				'order_id' => $order->id,
				'payload_create_invoice' => json_encode($payload),
				'response_create_invoice' => json_encode($resp),
				'id_invoice' => $resp->id,
				'author' => json_encode($author)
			];

			$this->db->insert('tbl_invoices', $tbl_data);

			$resposta = [
				'status' => 'success',
				'id' => $resp->id,
				'order' => $order_id
			];
			echo json_encode($resposta);
		} else {
			$resposta = [
				'status' => 'error'
			];
			echo json_encode($resposta);
		}
	}

	private function convertObjectToArray($object)
	{
		if (is_object($object)) {
			$object = get_object_vars($object);
		}
		if (is_array($object)) {
			$object = array_map('convertObjectToArray', $object);
		}
		return $object;
	}


	private function remove21porcento($valor)
	{
		// Dividir o valor por 1.21
		$resultado_divisao = $valor / 1.21;

		// // Multiplicar o resultado por 1.21
		// $resultado_final = $resultado_divisao * 1.21;

		// // Arredondar o resultado para 2 casas decimais
		// $resultado_final_arredondado = round($resultado_final, 2);

		return $resultado_divisao;
	}

	# Chama Curl responsável pelo envio para o marktplace
	private function curl_send_data_moloni($order)
	{
		$url_api = self::URL_API_MARKTPLACES . "/moloni/create";

		$handle   = curl_init($url_api);

		$request_headers     = [];
		$request_headers[] = 'Content-Type: application/json';

		curl_setopt($handle, CURLOPT_POST, true);
		curl_setopt($handle, CURLOPT_POSTFIELDS, json_encode($order));
		curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);

		$response = curl_exec($handle);
		return json_decode($response);
	}

	private function curl_get_data_order($dataOrder, $channel)
	{
		$url_api = self::URL_API_MARKTPLACES . "/" . $channel . "/orderId";

		$handle   = curl_init($url_api);

		$request_headers     = [];
		$request_headers[] = 'Content-Type: application/json';

		curl_setopt($handle, CURLOPT_POST, true);
		curl_setopt($handle, CURLOPT_POSTFIELDS, json_encode($dataOrder));
		curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);

		$response = curl_exec($handle);
		return json_decode($response);
	}

	public function get_invoices_bighub()
	{

		$data = $this->_getDataDashboard();

		// $this->db->select('id, name');
		// $all_clients = $this->db->get('tbl_user_clients')->result();
		// $data['clients'] = $all_clients;

		// echo json_encode($all_clients);
		// die();

		$data['view'] = 'invoices/get_invoices_bighub';
		$this->load->view('includes/template', $data);
	}

	# COmentada pois so foi usado para colocar o has_invoice nas ordens
	private function check_invoices_in_orders()
	{
		// $invoices = $this->db->select('order_id')->get('tbl_invoices')->result();

		// foreach ($invoices as $invoice) {
		// 	if (isset($invoice->order_id)) {
		// 		$this->db->where('id', $invoice->order_id);
		// 		$data = ['has_invoice' => 1];
		// 		if($this->db->update('tbl_orders', $data)){
		// 			echo 'id:'.$invoice->order_id.' -success';
		// 		}
		// 	}
		// }

		// echo 'fim';
		// die();
	}


	public function filter_invoices_bighub()
	{
		#$data = $this->_getDataDashboard();
		$ini_date = date_human_to_mysql($this->input->get('ini_date'));
		$end_date = date_human_to_mysql($this->input->get('end_date'));

		$this->db->select('ord.id, ord.user_id, ord.reference, ord.country, ord.total_price, ord.total_commission, ord.customer, ord.has_invoice, ord.channel, ord.created_date, ord.attachment, ord.has_invoice');
		$this->db->from('tbl_orders ord');
		$this->db->where('ord.created_date >=', $ini_date . ' 00:00:00');
		$this->db->where('ord.created_date <=', $end_date . ' 23:59:59');

		$this->db->where('ord.has_invoice', 0);

		#$this->db->join('tbl_invoices inv', 'ord.id = inv.order_id', 'left');

		$this->db->order_by('ord.id', 'desc');
		$all_orders = $this->db->get()->result();

		$marketplaces = array("phonehouse");

		$orders = [];
		foreach ($all_orders as $key => $order) {
			if (in_array($order->channel, $marketplaces)) {
				$customer =  json_decode($order->customer);
				$client_name = $this->_formatName($customer->first_name . ' ' . $customer->last_name);
				$orders[$key] = $order;
				$orders[$key]->client_name = $client_name;

				$orders_id[] = $order->id;
			}
		}

		$data['data_ini'] = date_mysql_to_human($ini_date);
		$data['data_fim'] = date_mysql_to_human($end_date);
		$data['quantity_orders'] = count($orders);

		$data['orders_id'] = $orders_id;

		$this->send_invoices_to_the_queue($orders_id);

		#$data['orders'] = $orders;
		#$data += $this->_getRespAction();

		// $data['session'] = 'Orders';
		// $data['view'] = 'invoices/list_orders';

		echo json_encode(count($orders_id));
		echo json_encode($data);
		die();
		#$this->load->view('includes/template', $data);
	}


	# Enviado todos os orders id para a fila
	private function send_invoices_to_the_queue($orders_id)
	{
		$delay = 0;
		foreach ($orders_id as $order_id) {
			# Montar o payload e enviar para fila
			$delay = $delay + 20000;
			$this->bulk_send_generate_invoices($order_id, $delay);
			// die();
		}

		return true;
	}

	# Vai gerar uma fila para cada order_id
	private function bulk_send_generate_invoices($order_id, $delay = 20000)
	{

		$URL_PUSHTOQUEUE = 'https://app.bighub.store/queue/';
		$url_thor_generate_invoices = 'https://apolo.bighub.store/generate-queue-invoice/' . $order_id;

		$job = [
			'name' => 'Order ID (' . $order_id . ')',
			'url' => $url_thor_generate_invoices,
			'method' => 'POST',
			'headers' => [
				'Accept' => 'application/json',
				'Content-Type' => 'application/json'
			],
			'options' => [
				'removeOnComplete' => 100000,
				'delay' => $delay,
				'concurrency' => 1,
				'limiter' => [
					'max' => 1,
					'duration' => 1000
				]
			]
		];

		$url_api = $URL_PUSHTOQUEUE . "generate-invoice/job";
		$handle  = curl_init($url_api);
		$request_headers = [];
		$request_headers[] = 'Accept: application/json';
		$request_headers[] = 'Content-Type: application/json';
		$request_headers[] = 'x-apikey: a57ed2dc-f0e3-4883-a3e5-959cc8af4c54';

		curl_setopt($handle, CURLOPT_POST, true);
		curl_setopt($handle, CURLOPT_POSTFIELDS, json_encode($job));
		curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);

		curl_exec($handle);
	}


	public function generate_queue_invoice($data)
	{
		$order_id = $this->input->post('order_id');

		$this->db->select('*');
		$this->db->where('id', $order_id);
		$order = $this->db->get('tbl_orders')->row();

		$channel = 'pccomp';
		if ($order->country == 'ES') {
			$channel = 'pccompes';
		}

		$dataOrder = ["id" => $order->reference];
		$getResponseFirst = $this->curl_get_data_order($dataOrder, $channel);

		$numeroNIF = '999999999';
		if (isset($getResponseFirst[0]->responseFirst->customer->shipping_address->additional_info)) {
			if (preg_match('/\d+[A-Za-z]*/', $getResponseFirst[0]->responseFirst->customer->shipping_address->additional_info, $matches)) {
				$numeroNIF = $matches[0];
			}
		}

		$customer = json_decode($order->customer);
		$products_order = json_decode($order->items);

		$products = [];
		foreach ($products_order as $prod) {
			$products = [
				'reference' => $prod->ean,
				'title' => $prod->title,
				'price' => $this->remove21porcento($prod->price)
			];
		}

		if (isset($order) && $order != null) {

			$payload = [
				"user" => $order->user_id,
				"vat" => $numeroNIF,
				"number" => 'BIGHUB-' . $order->id,
				"name" => $customer->first_name . ' ' . $customer->last_name,
				"address" => $customer->billing_address->street_1 . ' ' . $customer->billing_address->street_2,
				"city" => $customer->billing_address->city,
				"zipCode" => $customer->billing_address->zip_code,
				"country" => $order->country,
				"products" => [
					$products
				]
			];

			$resp = $this->curl_send_data_moloni($payload);

			$author = [
				'user_name' => $this->session->userdata('nome_usuario'),
				'user_code' => $this->session->userdata('token_usuario'),
				'date_hour' => date('Y-m-d H:m:i'),
				'origin' => 'Thor'
			];

			$tbl_data = [
				'order_id' => $order->id,
				'payload_create_invoice' => json_encode($payload),
				'response_create_invoice' => json_encode($resp),
				'id_invoice' => $resp->id,
				'author' => json_encode($author)
			];

			$this->db->insert('tbl_invoices', $tbl_data);

			$resposta = [
				'status' => 'success',
				'id' => $resp->id
			];
			echo json_encode($resposta);
		} else {
			$resposta = [
				'status' => 'error'
			];
			echo json_encode($resposta);
		}
	}

	public function download_invoices()
	{
		$data = $this->_getDataDashboard();

		$this->db->select('id, name');
		$all_clients = $this->db->get('tbl_user_clients')->result();
		$data['clients'] = $all_clients;

		// echo json_encode($all_clients);
		// die();

		$data['view'] = 'invoices/download_invoices';
		$this->load->view('includes/template', $data);
	}

	public function init_download_invoices()
	{
		$data = $this->_getDataDashboard();

		$ini_date = date_human_to_mysql($this->input->get('ini_date'));
		$end_date = date_human_to_mysql($this->input->get('end_date'));
		$filter = $this->input->get('filter');

		if ($filter == 'on') {
			$this->download_bulk_invoices(null, $ini_date, $end_date);
			echo 'Download processado das faturas de todos os sellers.';
			die();
			// $data['view'] = 'filter/total_sellers';
		} else {
			$seller_id = $this->input->get('seller');
			$this->download_bulk_invoices($seller_id, $ini_date, $end_date);
			echo 'Download processado das faturas do seller ' . $seller_id;
			die();
		}
	}

	private function generateUUIDv4()
	{
		$data = random_bytes(16);
		$data[6] = chr(ord($data[6]) & 0x0f | 0x40); // Versão 4
		$data[8] = chr(ord($data[8]) & 0x3f | 0x80); // Variante 10xx

		return vsprintf('%s%s-%s-%s-%s-%s%s%s', str_split(bin2hex($data), 4));
	}

	public function download_bulk_invoices($user_id = null, $ini_date = null, $end_date = null)
	{
		$this->db->select('id, attachment');
		$this->db->from('tbl_orders');
		$this->db->where('attachment IS NOT NULL');

		if ($user_id != null) {
			$this->db->where('user_id', $user_id);
		}

		if ($ini_date != null) {
			$this->db->where('created_date >=', $ini_date . ' 00:00:00');
		}

		if ($end_date != null) {
			$this->db->where('created_date <=', $end_date . ' 23:59:59');
		}

		$data_atual = date('Y-m-d');
		if ($ini_date == null || $end_date == null) {
			$this->db->where('created_date >=', $data_atual . ' 00:00:00');
			$this->db->where('created_date <=', $data_atual . ' 23:59:59');
		}

		$query = $this->db->get();

		var_dump('Quantidade de faturas ' . $query->num_rows());

		if ($query->num_rows() > 0) {
			$all_orders = $query->result();

			$lastObject = end($all_orders);

			$token = $this->generateUUIDv4();

			foreach ($all_orders as $key => $order) {

				$colun_attachement = $order->attachment;
				$attachement = json_decode($colun_attachement, true);

				$data['title'] = 'Faturas - Data ' . date_mysql_to_human($ini_date) . ' a ' . date_mysql_to_human($end_date);
				$data['token'] = $token;
				$data['invoice'] = $order->id;
				$data['lastInvoice'] = 0;

				if ($order->id === $lastObject->id) {
					$data['lastInvoice'] = 1;
				}


				if (!empty($colun_attachement)) {
					if (!empty($attachement[0]['file_name'])) {
						//var_dump('fatura ok '.$order->id);

						# Validamos tudo, agora vamos gerar a fila.
						$this->FlowInvoices_model->bulk_get_invoice($data, $user_id);

						// if ($this->get_invoice_order($order->id)) {
						// 	// echo 'ID:' . $order->id . ' gerado -';
						// 	// sleep(2);
						// } else {
						// 	echo ' Error pdf';
						// 	//die();
						// }
					} else {
						//var_dump('error fatura '.$order->id);
						if ($order->id === $lastObject->id) {
							$data['lastInvoice'] = 1;

							$penultimoObjeto = $all_orders[count($all_orders) - 2];
							$data['invoice'] = $penultimoObjeto->id;

							$this->FlowInvoices_model->bulk_get_invoice($data, $user_id);
						}
					}
				}
			}

			echo ' A solicitação foi feita com sucesso. Assim que processarmos todas as faturas enviaremos um eail informando.';
		} else {
			echo 'No periodo entre as datas escolhidas não existem faturas.';
			// Não há registros correspondentes
		}
	}

	public function get_invoice_bulk()
	{
		$query = $this->db->select('id, attachment, user_id, reference')->where('id', $data->id_tbl_order)->get('tbl_orders');
		$resp = $query->row();

		if ($resp->attachment != null && !empty($resp->attachment)) {

			$raw = $resp->attachment;
			$array = json_decode($raw, true);

			$file_name = $array[0]['file_name'];
			$user = $this->db->select('user_token')->where('id', $resp->user_id)->get('tbl_user_clients')->row();

			$token = $this->curl_get_jwttoken_user($user->user_token);

			$pdflink = 'https://app.bighub.store/api/v2/orders/' . $resp->id . '/attachments?file_name=' . $file_name . '&token=' . $token->data->api_token;
		}

		$url = str_replace(' ', '%20', $pdflink);
		if ($this->downloadPDF($url, $resp->reference)) {

			if ($data->lastInvoice === 1) {
				$downloadFolder = 'documents/' . date('Y-m-d');

				# Compactar tudo para um arquivo .ZIP

				# Pegar o nome do arquivo .zip criado
				$data_email = [
					'link_download' => 'https://thor.bighub.store/documents/783_17.05.2024.zip'
				];

				# Disparar email com o link para download das faturas.
			}
			return true;
		} else {
			return false;
		}
	}

	private function get_invoice_order($id_tbl_order)
	{
		$query = $this->db->select('id, attachment, user_id, reference')->where('id', $id_tbl_order)->get('tbl_orders');
		$resp = $query->row();

		if ($resp->attachment != null && !empty($resp->attachment)) {

			$raw = $resp->attachment;
			$array = json_decode($raw, true);

			$file_name = $array[0]['file_name'];
			$user = $this->db->select('user_token')->where('id', $resp->user_id)->get('tbl_user_clients')->row();

			$token = $this->curl_get_jwttoken_user($user->user_token);

			$pdflink = 'https://app.bighub.store/api/v2/orders/' . $resp->id . '/attachments?file_name=' . $file_name . '&token=' . $token->data->api_token;
		}

		$url = str_replace(' ', '%20', $pdflink);
		if ($this->downloadPDF($url, $resp->reference)) {
			return true;
		} else {
			return false;
		}
	}

	private function downloadPDF($pdfLink, $order_id)
	{
		set_time_limit(60);

		$downloadFolder = 'documents/' . date('Y-m-d');

		if (!file_exists($downloadFolder)) {
			mkdir($downloadFolder, 0777, true);
		}

		$fileName = $order_id . '.pdf';
		$filePath = $downloadFolder . '/' . $fileName;

		$ch = curl_init($pdfLink);

		$options = array(
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_USERAGENT      => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
			CURLOPT_TIMEOUT        => 30,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_VERBOSE        => true,
		);

		curl_setopt_array($ch, $options);


		$pdfContent = curl_exec($ch);


		if (curl_errno($ch)) {
			echo 'Erro cURL: ' . curl_error($ch);
		}

		curl_close($ch);

		set_time_limit(30);

		if ($pdfContent !== false) {
			$bytesWritten = file_put_contents($filePath, $pdfContent);

			if ($bytesWritten !== false) {
				#echo "Download concluído! $bytesWritten bytes gravados no arquivo.";
				return true;
			} else {
				echo "Erro ao gravar o conteúdo no arquivo.";
				return false;
			}
		} else {
			echo "Erro ao obter o conteúdo do PDF";
			return false;
		}
	}

	private function curl_get_jwttoken_user($user_token)
	{
		$url  = "https://app.bighub.store/api/v1/authorize?app_key=$user_token";

		$handle   = curl_init($url);

		$request_headers     = [];
		$request_headers[] = 'Accept: application/json';
		$request_headers[] = 'Content-Type: application/json';

		curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);
		$response = curl_exec($handle);

		return json_decode($response);
	}














	// private function verificarLetra($valor) {
	// 	if (preg_match('/[a-zA-Z]/', $valor)) {
	// 		return true; // Contém uma letra
	// 	} else {
	// 		return false; // Não contém letras
	// 	}
	// }


	public function impersonate()
	{
		$token = $this->input->post('user');
		$resp = $this->curl_impersonate($token);
		echo json_encode($resp->data->api_token);
	}

	private function curl_impersonate($user_token)
	{
		$url  = "https://app.bighub.store/api/v1/authorize?app_key=$user_token";

		$handle   = curl_init($url);

		$request_headers     = [];
		$request_headers[] = 'Accept: application/json';
		$request_headers[] = 'Content-Type: application/json';

		curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);
		$response = curl_exec($handle);

		return json_decode($response);
	}

	public function update_purchased_order()
	{
		$order_id = $this->input->post('order');
		$action = $this->input->post('action');

		$this->db->where('id', $order_id);
		$data = [
			'purchased' => $action
		];
		if ($this->db->update('tbl_orders', $data)) {
			$resp = [
				'update' => 'success',
				'action' => $action
			];
			echo json_encode($resp);
		}
	}
}
