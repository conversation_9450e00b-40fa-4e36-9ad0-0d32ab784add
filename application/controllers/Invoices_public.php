<?php

ini_set('memory_limit', '5120M'); // Defina o valor de memória desejado, como 1024M para 1GB

defined('BASEPATH') or exit('No direct script access allowed');

class Invoices_public extends 	CI_Controller
{

	public function __construct()
	{
		parent::__construct();
		$this->load->model('Emails_model');
	}


	public function get_invoice_bulk()
	{
		$http_post = file_get_contents('php://input');

		if (isset($http_post) && !empty($http_post)) {
			$info_order = json_decode($http_post,  true);

			$id_tbl_order = $info_order['invoice'];

			$query = $this->db->select('id, attachment, user_id, reference')->where('id', $id_tbl_order)->get('tbl_orders');
			$resp = $query->row();

			if ($resp->attachment != null && !empty($resp->attachment)) {

				$raw = $resp->attachment;
				$array = json_decode($raw, true);

				$file_name = $array[0]['file_name'];
				$user = $this->db->select('user_token')->where('id', $resp->user_id)->get('tbl_user_clients')->row();

				$token = $this->curl_get_jwttoken_user($user->user_token);

				$pdflink = 'https://app.bighub.store/api/v2/orders/' . $resp->id . '/attachments?file_name=' . $file_name . '&token=' . $token->data->api_token;


				$token_invoice = $info_order['token'];

				$url = str_replace(' ', '%20', $pdflink);
				if ($this->downloadPDF($url, $resp->reference, $token_invoice)) {
					$last_object = $info_order['lastInvoice'];

					if ($last_object != 0) {
						# Preparando dados para compactar as faturas
						$date = date('Y-m-d');
						$folderPath = 'documents/' .$token_invoice.'-'.date('Y-m-d');
						$zipFilePath = 'documents/' .$token_invoice.'-'.date('Y-m-d').'/faturas-'.$token_invoice.'-'.$date.'.zip';

						if ($this->zipFolder($folderPath, $zipFilePath)) {
							# Pegar o nome do arquivo .zip criado
							
								$title_email = $info_order['title'] ?? 'Faturas processadas e compactadas para download';
								$link_download = 'https://thor.bighub.store/'.$zipFilePath;
							
							# Envia o email informando que finalizou as faturas.
							$this->Emails_model->send_alert_download_invoices_ok($title_email, $link_download);
							die();
						} else {
							echo 'Falha ao criar o arquivo .zip.';
						}

					}else{
						echo ' Não dispara email';
					}
					return true;
				} else {
					return false;
				}
			}
		}

		echo 'error post';
	}

	private function downloadPDF($pdfLink, $order_id, $token)
	{
		//set_time_limit(60);

		$downloadFolder = 'documents/'.$token.'-'.date('Y-m-d');

		if (!file_exists($downloadFolder)) {
			mkdir($downloadFolder, 0777, true);
		}

		$fileName = $order_id . '.pdf';
		$filePath = $downloadFolder . '/' . $fileName;

		$ch = curl_init($pdfLink);

		$options = array(
			CURLOPT_RETURNTRANSFER => true,
			CURLOPT_USERAGENT      => 'Mozilla/5.0 (X11; Linux x86_64) AppleWebKit/537.36 (KHTML, like Gecko) Chrome/120.0.0.0 Safari/537.36',
			CURLOPT_TIMEOUT        => 30,
			CURLOPT_FOLLOWLOCATION => true,
			CURLOPT_VERBOSE        => true,
		);

		curl_setopt_array($ch, $options);


		$pdfContent = curl_exec($ch);


		if (curl_errno($ch)) {
			echo 'Erro cURL: ' . curl_error($ch);
		}

		curl_close($ch);

		//set_time_limit(30);

		if ($pdfContent !== false) {
			$bytesWritten = file_put_contents($filePath, $pdfContent);

			if ($bytesWritten !== false) {
				#echo "Download concluído! $bytesWritten bytes gravados no arquivo.";
				return true;
			} else {
				echo "Erro ao gravar o conteúdo no arquivo.";
				return false;
			}
		} else {
			echo "Erro ao obter o conteúdo do PDF";
			return false;
		}
	}

	private function curl_get_jwttoken_user($user_token)
	{
		$url  = "https://app.bighub.store/api/v1/authorize?app_key=$user_token";

		$handle   = curl_init($url);

		$request_headers     = [];
		$request_headers[] = 'Accept: application/json';
		$request_headers[] = 'Content-Type: application/json';

		curl_setopt($handle, CURLOPT_RETURNTRANSFER, TRUE);
		curl_setopt($handle, CURLOPT_HTTPHEADER, $request_headers);
		$response = curl_exec($handle);

		return json_decode($response);
	}

	private function zipFolder($source, $destination) {
		if (!extension_loaded('zip') || !file_exists($source)) {
			return false;
		}
	
		$zip = new ZipArchive();
		if (!$zip->open($destination, ZipArchive::CREATE | ZipArchive::OVERWRITE)) {
			return false;
		}
	
		$source = realpath($source);
		if (is_dir($source)) {
			$files = new RecursiveIteratorIterator(
				new RecursiveDirectoryIterator($source),
				RecursiveIteratorIterator::LEAVES_ONLY
			);
	
			foreach ($files as $file) {
				if (!$file->isDir()) {
					$filePath = $file->getRealPath();
					$relativePath = substr($filePath, strlen($source) + 1);
					$zip->addFile($filePath, $relativePath);
				}
			}
		} else if (is_file($source)) {
			$zip->addFile($source, basename($source));
		}
	
		return $zip->close();
	}
}
