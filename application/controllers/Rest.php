<?php

defined('BASEPATH') or exit('No direct script access allowed');

class Rest extends CI_Controller
{

	public function __construct()
	{
		parent::__construct();
	}


	public function getorders_data_client()
	{
		 // Parâmetros que você deseja passar via GET
		 $param1 = '02/10/2023';
		 $param2 = '10/10/2023';
		 $param3 = 190;

		 $retorno = $this->filter($param1, $param2, $param3);

		 echo json_encode($retorno);
		 die();
	}


	private function filter($dataini, $datafim, $seller)
	{
		// $data = $this->_getDataDashboard();

		$ini_date = date_human_to_mysql($dataini);
		$end_date = date_human_to_mysql($datafim);
		$state = $this->input->get('state');

		$filter = 'off';

		if ($filter == 'on') {
			// $details_orders =  $this->all_sellers_filter($ini_date, $end_date);
			// $data['details_orders'] = $details_orders;

			// // echo json_encode($data);
			// // die();
			// $data['view'] = 'filter/total_sellers';
		} else {

			$seller_id = $seller;

			$data['ini_date'] = date_mysql_to_human($ini_date);
			$data['end_date'] = date_mysql_to_human($end_date);

			if (!isset($state)) {
				$state = ['RECEIVED', 'SHIPPING', 'SHIPPED'];
			}
			$state_canceled = ['CANCELED', 'CLOSED'];

			$total_orders = $this->get_orders_by_seller($seller_id, null, null, $state, null, $ini_date, $end_date);
			$total_orders_canceled = $this->get_orders_by_seller($seller_id, null, null, $state_canceled, null, $ini_date, $end_date);
			$total_orders_all = $this->get_orders_by_seller($seller_id, null, null, null, null, $ini_date, $end_date);

			$worten_pt_filter = $this->get_orders_by_seller($seller_id, 'worten', 'PT', $state, null,  $ini_date, $end_date);
			$worten_es_filter = $this->get_orders_by_seller($seller_id, 'worten', 'ES', $state, null,  $ini_date, $end_date);
			$carrefour_es_filter = $this->get_orders_by_seller($seller_id, 'carrefour', 'ES', $state, null, $ini_date, $end_date);
			$fnac_pt_filter = $this->get_orders_by_seller($seller_id, 'fnac', 'PT', $state, null,  $ini_date, $end_date);
			$fnac_es_filter = $this->get_orders_by_seller($seller_id, 'fnac', 'PT', $state, null,  $ini_date, $end_date);
			$mediamarket_filter = $this->get_orders_by_seller($seller_id, 'mediamarkt', 'ES', $state, null,  $ini_date, $end_date);

			$data['total_orders'] = $total_orders;
			$data['total_orders_canceled'] = $total_orders_canceled;
			$data['total_orders_all'] = $total_orders_all;

			$data['worten_pt_filter'] = $worten_pt_filter;
			$data['worten_es_filter'] = $worten_es_filter;
			$data['carrefour_es_filter'] = $carrefour_es_filter;
			$data['fnac_pt_filter'] = $fnac_pt_filter;
			$data['fnac_es_filter'] = $fnac_es_filter;
			$data['mediamarket_filter'] = $mediamarket_filter;


			# Vai buscar os dados do vendedor
			$seller = $this->getSeller($seller_id);
			$data['seller'] = $seller;

			############# Tratamento valores BIGHUB - SELLER - CUSTOMER #############

			$orders = $total_orders['result'];

			# Atraves do IBAN identifico a origem do vendedor
			$seller_country = $seller->bank_number ? substr($seller->bank_number, 0, 2) : 'Error';

			# Se não existir um IBAN ele vai tentar buscar através do NIF
			if ($seller_country == 'Error' || $seller_country != 'PT' || $seller_country != 'ES') {
				if ($this->verificarLetra($seller->nif)) {
					$seller_country = 'ES';
				} else {
					$seller_country = 'PT';
				}
			}
			$data['pais_vendedor'] = $seller_country;


			$total_valor_de_repasse_ao_vendedor = 0;
			$total_valor_de_repasse_ao_vendedor_15p = 0;
			$soma_dos_ivas_vies = 0;
			$soma_dos_ivas = 0;


			if (isset($orders) && !empty($orders)) {

				
				foreach ($orders as $key => $order) {

					# Imposto PT e ES
					$percentual_pt = 23;
					$percentual_es = 21;

					$data_customer = json_decode($order->customer);

					$costumer_country = $data_customer->shipping_address->country;

					$bighub_country = 'ES';
					$orders[$key]->origens = [
						'localidade_bighub' => $bighub_country,
						'localidade_vendedor' => $seller_country,
						'localidade_comprador' => $costumer_country,
					];

					$valor_venda_retirando_a_comissao = $order->total_price - $order->total_commission;
					$orders[$key]->financeiro = [
						'valor_venda' => $order->total_price,
						'comissao_marketplace' => $order->total_commission,
						'valor_venda_retirando_a_comissao' => round($valor_venda_retirando_a_comissao, 2),
					];

					# Aplicando valores no caso da comissao ser 15%
					# Não vamos assumir o valor que vem do marketplace e sim os 15%
					$order_menos_15_por_cento = ($order->total_price * 15) / 100;
					$valor_venda_retirando_a_comissao_bighub_15 = $order->total_price - $order_menos_15_por_cento;
					$orders[$key]->financeiro_com_cobranca_de_15_porcento = [
						'valor_venda' => $order->total_price,
						'comissao_marketplace' => $order->total_commission,
						'comissao_15_por_cento_bighub' => round(($order->total_price * 15) / 100, 2),
						'lucro_bighub_sobre_comissao_15_por_cento' => round(($order->total_price * 15) / 100 - $order->total_commission, 2),
						'valor_venda_retirando_a_comissao_bighub_15' => round($valor_venda_retirando_a_comissao_bighub_15, 2),
					];


					// echo json_encode($order);
					// die();
					// $orders[$key]->financeiro = [
					// 	'remove_5_euros' => false
					// ];


					if ($seller_country == 'PT' && $costumer_country == 'ES' || $costumer_country == 'IT') {

						# Se o vendedor PT enviar para PT será cobrado 23%
						$total_do_iva = ($valor_venda_retirando_a_comissao * $percentual_pt) / 100;
						$total_menos_iva_23 = $valor_venda_retirando_a_comissao - $total_do_iva;

						if ($order->state != 'CLOSED') {
							$soma_dos_ivas_vies += $total_do_iva;
							$soma_dos_ivas += $total_do_iva;
						}

						# if cliente marcado aplicar os 5 
						$orders[$key]->financeiro += [
							'remove_5_euros' => true #true
						];

						$orders[$key]->financeiro += [
							'valor_cobrado_de_iva' => round($total_do_iva, 2),
							'valor_retirando_o_iva_23' => round($total_menos_iva_23, 2),
							'valor_de_repasse_ao_vendedor' => round($total_menos_iva_23, 2),
							'tipo_iva' => 'Vies'
						];

						# if cliente marcado aplicar os 5 
						$total_valor_de_repasse_ao_vendedor += $total_menos_iva_23 - 5;
						#$total_valor_de_repasse_ao_vendedor += $total_menos_iva_23;

						$total_do_iva_15 = round(($valor_venda_retirando_a_comissao_bighub_15 * $percentual_pt) / 100, 2);
						$total_menos_iva_23_15 = $valor_venda_retirando_a_comissao_bighub_15 - $total_do_iva_15;
						$orders[$key]->financeiro_com_cobranca_de_15_porcento += [
							'valor_cobrado_de_iva_15p' => $total_do_iva_15,
							'valor_retirando_o_iva_23_15p' => round($total_menos_iva_23_15, 2),
							'valor_de_repasse_ao_vendedor_15p' => round($total_menos_iva_23_15, 2)
						];

						$total_valor_de_repasse_ao_vendedor_15p += $total_menos_iva_23_15;
						$lucro_no_repasse_bighub = $total_valor_de_repasse_ao_vendedor - $total_valor_de_repasse_ao_vendedor_15p;
					}

					// var_dump($seller_country, $costumer_country);

					if (
						$seller_country == 'ES' && $costumer_country == 'ES' ||
						$seller_country == 'ES' && $costumer_country == 'PT' ||
						$seller_country == 'PT' && $costumer_country == 'PT'
					) {

						// var_dump('entra');
						// die();

						# Se o vendedor PT enviar para PT será cobrado 23%
						$total_do_iva = ($valor_venda_retirando_a_comissao * $percentual_pt) / 100;
						$total_menos_iva_21 = $valor_venda_retirando_a_comissao - $total_do_iva;

						$soma_dos_ivas_vies += 0;
						$soma_dos_ivas += $total_do_iva;

						$orders[$key]->financeiro += [
							'remove_5_euros' => false #true
						];

						$orders[$key]->financeiro += [
							'apresenta_valor_venda_sem_iva_21' => round($total_menos_iva_21, 2),
							'valor_cobrado_de_iva' => round($total_do_iva, 2),
							'valor_de_repasse_ao_vendedor' => round($valor_venda_retirando_a_comissao, 2),
							'tipo_iva' => 'Total'
						];

						$total_valor_de_repasse_ao_vendedor += $valor_venda_retirando_a_comissao;

						$total_do_iva_15 = round(($valor_venda_retirando_a_comissao_bighub_15 * $percentual_es) / 100, 2);
						$total_menos_iva_21_15 = $valor_venda_retirando_a_comissao_bighub_15 - $total_do_iva_15;
						$orders[$key]->financeiro_com_cobranca_de_15_porcento += [
							'apresenta_valor_venda_sem_iva_15p' => round($total_menos_iva_21_15, 2),
							'apresenta_valor_cobrado_de_iva_21_15p' => $total_do_iva_15,
							'apresenta_valor_de_repasse_ao_vendedor' => round($valor_venda_retirando_a_comissao_bighub_15, 2)
						];

						$total_valor_de_repasse_ao_vendedor_15p += $total_menos_iva_21_15;
						$lucro_no_repasse_bighub = $total_valor_de_repasse_ao_vendedor - $total_valor_de_repasse_ao_vendedor_15p;
					}


					$data['calculos_de_valores_geral'] = [
						'comissao_15_por_cento' => ($total_orders['total_price'] * 15) / 100,
						'soma_dos_ivas' => $soma_dos_ivas,
						'soma_dos_ivas_vies' => $soma_dos_ivas_vies,
						'repasse_ao_vendedor' => round($total_valor_de_repasse_ao_vendedor, 2),
						'repasse_ao_vendedor_15p' => round($total_valor_de_repasse_ao_vendedor_15p, 2),
						'lucro_bighub' => round($lucro_no_repasse_bighub, 2)
					];

					// if($order->guid === 'c06db6ca-378d-11ee-aadf-56000431c7b4'){
					// 	echo json_encode($orders[$key]);
					//	die();
					// }

				}
			} else {
				$data['total_orders'] = [
					'result' => [],
					'total_price' => 0.00,
					'total_commission' => 0.00,
					'count'  => 0
				];

				$data['calculos_de_valores_geral'] = [
					'comissao_15_por_cento' => 0.00,
					'soma_dos_ivas' => 0.00,
					'soma_dos_ivas_vies' => 0.00,
					'repasse_ao_vendedor' => 0.00,
					'repasse_ao_vendedor_15p' => 0.00,
					'lucro_bighub' => 0.00
				];
			}
			$data['view'] = 'filter/filters';
		}

	 
		return $data;
		
		#$this->load->view('includes/template', $data);
		// echo json_encode($data);
		// die();
	}


	private function get_orders_by_seller($seller_id, $channel = null, $country = null, $state = null, $limit = null, $ini_date, $end_date)
	{

		$this->db->select('to.id, to.guid, to.user_id, to.reference, to.state, to.channel, to.country, to.total_price, to.total_commission, to.shipping_time, to.created_date, to.purchased, to.customer, op.paid, op.payment_date, op.payment_obs as obs');
		
		$this->db->where('to.created_date >=', $ini_date . ' 00:00:00');
		$this->db->where('to.created_date <=', $end_date . ' 23:59:59');

		if ($seller_id != null) {
			$this->db->where('to.user_id', $seller_id);
		}

		if ($channel != null) {
			$this->db->where('to.channel', $channel);
		}

		if ($country != null) {
			$this->db->where('to.country', $country);
		}

		if ($state != null) {
			$this->db->where_in('to.state', $state);
		}

		if ($limit != null) {
			$this->db->limit($limit);
		}

		$this->db->join('tbl_orders_payment op', 'op.order_guid = to.guid', 'left');
		$this->db->order_by('to.id', 'desc');
		$result = $this->db->get('tbl_orders to')->result();

		# Somando todos os valores 
		$totalPrice = 0;
		$totalCommission = 0;
		foreach ($result as $item) {
			$totalPrice += floatval($item->total_price);
			$totalCommission += floatval($item->total_commission);
		}

		$data = [
			'result' => $result,
			'total_price' => round($totalPrice, 2),
			'total_commission' => round($totalCommission, 2),
			'count'  => count($result)
		];

		return $data;
	}

	private function verificarLetra($valor) {
		if (preg_match('/[a-zA-Z]/', $valor)) {
			return true; // Contém uma letra
		} else {
			return false; // Não contém letras
		}
	}

	private function getSeller($user_id){
		$this->db->select('uc.*, uc.id as id_seller, c.*');
		$this->db->where('uc.id', $user_id);
		$this->db->join("tbl_company c", "c.client_id = uc.id",'left');	
		$query = $this->db->get('tbl_user_clients uc');
		return $query->row();
	}
}
