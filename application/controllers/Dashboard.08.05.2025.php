<?php

use PHPUnit\TextUI\XmlConfiguration\Php;

defined('BASEPATH') or exit('No direct script access allowed');

class Dashboard extends MY_Controller
{

	public function index()
	{
		$data = $this->_getDataDashboard();

		$primeiroDia = date('Y-m-01'); 
		$ultimoDia = date('Y-m-t');      
		$data_dia = date('Y-m-d');       
		$data['data_dia'] = $data_dia;  

		# Buscar todas as vendas realizada dentro do mês atual
		$this->db->select('id, reference, channel, country, state, total_price, total_commission, shipping_time, additional_info');
		$this->db->where('DATE(created_date) >=', $primeiroDia);
		$this->db->where('DATE(created_date) <=', $ultimoDia);
		// $this->db->limit(5);
		// $this->db->where('country','PT');
		// $this->db->where('channel','worten');
		$this->db->order_by('id', 'desc');

		$saleMonth = $this->db->get('tbl_orders')->result();
		$data['quantity_sales_month'] = count($saleMonth);
		// echo json_encode($data);
		// die();


		$totalSalesMonth = 0;
		$totalCommissionMonth = 0;
		foreach ($saleMonth as $item) {
			$totalSalesMonth += floatval($item->total_price);
			$totalCommissionMonth += floatval($item->total_commission);
		}

		# Buscar todas as vendas realizadas dentro do dia de hoje
		$this->db->select('id, reference, channel, country, state, total_price, total_commission, shipping_time, additional_info');
		$this->db->where('DATE(created_date) >=', $data_dia);
		$this->db->where('DATE(created_date) <=', $data_dia);
		#$this->db->where('channel','worten');
		$this->db->order_by('id', 'desc');
		$daily_sales = $this->db->get('tbl_orders')->result();
		$data['quantity_sales_day'] = count($daily_sales);

		

		$totalSalesDay = 0;
		$totalCommissionDay = 0;
		foreach ($daily_sales as $item) {
			$totalSalesDay += floatval($item->total_price);
			$totalCommissionDay += floatval($item->total_commission);
		}

		# Get para dados de cada marketplace
		$worten_pt_month = $this->get_orders_by($saleMonth, 'worten', 'PT', 'channel-1');
		#die();
		$worten_es_month = $this->get_orders_by($saleMonth, 'worten', 'ES', 'channel-1');
		$worten_pt_month_2 = $this->get_orders_by($saleMonth, 'worten', 'PT', 'channel-2');
		$worten_es_month_2 = $this->get_orders_by($saleMonth, 'worten', 'ES', 'channel-2');

		$carrefour_es_month = $this->get_orders_by($saleMonth, 'carrefour', 'ES', 'channel-1');
		
		$mediamarkt_es_month = $this->get_orders_by($saleMonth, 'mediamarkt', 'ES', 'channel-1');
		$fnac_pt_month = $this->get_orders_by($saleMonth, 'fnac', 'PT', 'channel-1');
		$fnac_es_month = $this->get_orders_by($saleMonth, 'fnac', 'ES', 'channel-1');
		$fnac_fr_month = $this->get_orders_by($saleMonth, 'fnac', 'FR', 'channel-1');

		$eprice_it_month = $this->get_orders_by($saleMonth, 'eprice', 'IT', 'channel-1');
		$pccomp_es_month = $this->get_orders_by($saleMonth, 'pccomp', 'ES', 'channel-1');
		$pccomp_pt_month = $this->get_orders_by($saleMonth, 'pccomp', 'PT', 'channel-1');
		$makro_es_month = $this->get_orders_by($saleMonth, 'makro', 'ES', 'channel-1');
		$makro_pt_month = $this->get_orders_by($saleMonth, 'makro', 'PT', 'channel-1');
		$leroy_es_month = $this->get_orders_by($saleMonth, 'leroymerlin', 'ES', 'channel-1');
		$phonehouse_es_month = $this->get_orders_by($saleMonth, 'phonehouse', 'ES', 'channel-1');
		$cdiscount_fr_month = $this->get_orders_by($saleMonth, 'cdiscount', 'FR', 'channel-1');
		$cdiscount_fr_month_2 = $this->get_orders_by($saleMonth, 'cdiscount', 'FR', 'channel-3');
		$miravia_es_month = $this->get_orders_by($saleMonth, 'miravia', 'ES', 'channel-1');
		$manomano_es_month = $this->get_orders_by($saleMonth, 'manomano', 'ES', 'channel-1');
		$bulevip_es_month = $this->get_orders_by($saleMonth, 'bulevip', 'ES', 'channel-1');
		$pixmania_es_month = $this->get_orders_by($saleMonth, 'pixmania', 'ES', 'channel-1');
		$pixmania_fr_month = $this->get_orders_by($saleMonth, 'pixmania', 'FR', 'channel-1');
		$elcorteingles_es_month = $this->get_orders_by($saleMonth, 'elcorteingles', 'ES', 'channel-1');
		$rakuten_fr_month = $this->get_orders_by($saleMonth, 'rakuten', 'FR', 'channel-1');
		$pccomp_it_month = $this->get_orders_by($saleMonth, 'pccomp', 'IT', 'channel-1');
		$pccomp_fr_month = $this->get_orders_by($saleMonth, 'pccomp', 'FR', 'channel-1');
		$mediamarkt_de_month = $this->get_orders_by($saleMonth, 'mediamarkt', 'DE', 'channel-1');
		$empik_pl_month  = $this->get_orders_by($saleMonth, 'empik', 'PL', 'channel-1');
		$rueducommerce_fr_month = $this->get_orders_by($saleMonth, 'rueducommerce', 'FR', 'channel-1');
		$perfumesclub_pt_month = $this->get_orders_by($saleMonth, 'perfumesclub', 'PT', 'channel-1');
		$bigbang_si_month = $this->get_orders_by($saleMonth, 'bigbang', 'SI', 'channel-1');
		$eleclerc_fr_month = $this->get_orders_by($saleMonth, 'eleclerc', 'FR', 'channel-1');
		$clubefashion_month = $this->get_orders_by($saleMonth, 'clubefashion', 'PT', 'channel-1');
		$backmarket_month = $this->get_orders_by($saleMonth, 'backmarket', null, 'channel-1');
		$aliexpress_month = $this->get_orders_by($saleMonth, 'aliexpress', null, 'channel-1');
		$kaufland_month = $this->get_orders_by($saleMonth, 'kaufland', 'DE', 'channel-1');
		$planetahuerto_month = $this->get_orders_by($saleMonth, 'planetahuerto', null);
		$conrad_month = $this->get_orders_by($saleMonth, 'conrad', null, 'channel-1');
		$quirumedes_month = $this->get_orders_by($saleMonth, 'quirumedes', null, 'channel-1');
		$carrefour_fr_month = $this->get_orders_by($saleMonth, 'carrefour', 'FR', 'channel-1');
		$mediamarkt_es_month_2 = $this->get_orders_by($saleMonth, 'mediamarkt', 'ES', 'channel-2');
		$carrefour_es_month_2 = $this->get_orders_by($saleMonth, 'carrefour', 'ES', 'channel-2');

		$venteunique_pt_month = $this->get_orders_by($saleMonth, 'venteunique', 'PT', 'channel-1');
		$venteunique_es_month = $this->get_orders_by($saleMonth, 'venteunique', 'ES', 'channel-1');
		$venteunique_be_month = $this->get_orders_by($saleMonth, 'venteunique', 'BE', 'channel-1');
		$venteunique_de_month = $this->get_orders_by($saleMonth, 'venteunique', 'DE', 'channel-1');
		$venteunique_fr_month = $this->get_orders_by($saleMonth, 'venteunique', 'FR', 'channel-1');
		$venteunique_it_month = $this->get_orders_by($saleMonth, 'venteunique', 'IT', 'channel-1');
		$venteunique_nl_month = $this->get_orders_by($saleMonth, 'venteunique', 'NL', 'channel-1');

		$shopapotheke_de_month = $this->get_orders_by($saleMonth, 'shopapotheke', 'DE', 'channel-1');
		$shopapotheke_at_month = $this->get_orders_by($saleMonth, 'shopapotheke', 'AT', 'channel-1');

		$conforama_month = $this->get_orders_by($saleMonth, 'conforama', 'FR', 'channel-1');
		
		$macway_fr_month = $this->get_orders_by($saleMonth, 'macway', 'FR', 'channel-1');

		$tiendanimal_es_month = $this->get_orders_by($saleMonth, 'tiendanimal', 'ES', 'channel-1');
		$stockly_month = $this->get_orders_by($saleMonth, 'mktbighub', null);

		$ubaldi_fr_month_c2 = $this->get_orders_by($saleMonth, 'ubaldi', 'FR', 'channel-2');

		$data['ubaldi_fr_month_c2'] = $ubaldi_fr_month_c2;
		$data['worten_pt_month'] = $worten_pt_month;
		$data['worten_es_month'] = $worten_es_month;
		$data['worten_pt_month_2'] = $worten_pt_month_2;
		$data['worten_es_month_2'] = $worten_es_month_2;
		$data['carrefour_es_month'] = $carrefour_es_month;
		$data['mediamarkt_es_month'] = $mediamarkt_es_month;
		$data['fnac_pt_month'] = $fnac_pt_month;
		$data['fnac_es_month'] = $fnac_es_month;
		$data['fnac_fr_month'] = $fnac_fr_month;

		$data['eprice_it_month'] = $eprice_it_month;
		$data['pccomp_es_month'] = $pccomp_es_month;
		$data['pccomp_pt_month'] = $pccomp_pt_month;
		$data['makro_es_month'] = $makro_es_month;
		$data['makro_pt_month'] = $makro_pt_month;
		$data['leroy_es_month'] = $leroy_es_month;
		$data['phonehouse_es_month'] = $phonehouse_es_month;
		$data['cdiscount_fr_month'] = $cdiscount_fr_month;
		$data['cdiscount_fr_month_2'] = $cdiscount_fr_month_2;
		$data['miravia_es_month'] = $miravia_es_month;
		$data['manomano_es_month'] = $manomano_es_month;
		$data['bulevip_es_month'] = $bulevip_es_month;
		$data['pixmania_es_month'] = $pixmania_es_month;
		$data['pixmania_fr_month'] = $pixmania_fr_month;
		$data['elcorteingles_es_month'] = $elcorteingles_es_month;
		$data['rakuten_fr_month'] = $rakuten_fr_month;
		$data['pccomp_it_month'] = $pccomp_it_month;
		$data['pccomp_fr_month'] = $pccomp_fr_month;
		$data['mediamarkt_de_month'] = $mediamarkt_de_month;
		$data['empik_pl_month'] = $empik_pl_month;
		$data['rueducommerce_fr_month'] = $rueducommerce_fr_month;
		$data['perfumesclub_pt_month'] = $perfumesclub_pt_month;
		$data['bigbang_si_month'] = $bigbang_si_month;
		$data['eleclerc_fr_month'] = $eleclerc_fr_month;
		$data['clubefashion_month'] = $clubefashion_month;
		$data['backmarket_month'] = $backmarket_month;
		$data['aliexpress_month'] = $aliexpress_month;
		$data['kaufland_month'] = $kaufland_month;
		$data['planetahuerto_month'] = $planetahuerto_month;
		$data['conrad_month'] = $conrad_month;
		$data['quirumedes_month'] = $quirumedes_month;
		$data['mediamarkt_es_month_2'] = $mediamarkt_es_month_2;
		$data['carrefour_fr_month'] = $carrefour_fr_month;
		$data['carrefour_es_month_2'] = $carrefour_es_month_2;

		$data['venteunique_pt_month'] = $venteunique_pt_month;
		$data['venteunique_es_month'] = $venteunique_es_month;
		$data['venteunique_be_month'] = $venteunique_be_month;
		$data['venteunique_de_month'] = $venteunique_de_month;
		$data['venteunique_fr_month'] = $venteunique_fr_month;
		$data['venteunique_it_month'] = $venteunique_it_month;
		$data['venteunique_nl_month'] = $venteunique_nl_month;
		
		$data['shopapotheke_de_month'] = $shopapotheke_de_month;
		$data['shopapotheke_at_month'] = $shopapotheke_at_month;

		$data['conforama_month'] = $conforama_month;

		$data['macway_fr_month'] = $macway_fr_month;

		$data['tiendanimal_es_month'] = $tiendanimal_es_month;
		$data['stockly_month'] = $stockly_month;
		



		$worten_pt_day = $this->get_orders_by($daily_sales, 'worten', 'PT', 'channel-1');
		$worten_es_day = $this->get_orders_by($daily_sales, 'worten', 'ES', 'channel-1');
		$worten_pt_day_2 = $this->get_orders_by($daily_sales, 'worten', 'PT', 'channel-2');
		$worten_es_day_2 = $this->get_orders_by($daily_sales, 'worten', 'ES', 'channel-2');

		$carrefour_es_day = $this->get_orders_by($daily_sales, 'carrefour', 'ES', 'channel-1');

		$mediamarkt_es_day = $this->get_orders_by($daily_sales, 'mediamarkt', 'ES', 'channel-1');
		$fnac_pt_day = $this->get_orders_by($daily_sales, 'fnac', 'PT', 'channel-1');
		$fnac_es_day = $this->get_orders_by($daily_sales, 'fnac', 'ES', 'channel-1');
		$fnac_fr_day = $this->get_orders_by($daily_sales, 'fnac', 'FR', 'channel-1');

		$eprice_it_day = $this->get_orders_by($daily_sales, 'eprice', 'IT', 'channel-1');
		$pccomp_es_day = $this->get_orders_by($daily_sales, 'pccomp', 'ES', 'channel-1');
		$pccomp_pt_day = $this->get_orders_by($daily_sales, 'pccomp', 'PT', 'channel-1');
		$makro_es_day = $this->get_orders_by($daily_sales, 'makro', 'ES', 'channel-1');
		$makro_pt_day = $this->get_orders_by($daily_sales, 'makro', 'PT', 'channel-1');
		$leroy_es_day = $this->get_orders_by($daily_sales, 'leroymerlin', 'ES', 'channel-1');
		$phonehouse_es_day = $this->get_orders_by($daily_sales, 'phonehouse', 'ES', 'channel-1');
		$cdiscount_fr_day = $this->get_orders_by($daily_sales, 'cdiscount', 'FR', 'channel-1');
		$cdiscount_fr_day_2 = $this->get_orders_by($daily_sales, 'cdiscount', 'FR', 'channel-3');
		$miravia_es_day = $this->get_orders_by($daily_sales, 'miravia', 'ES', 'channel-1');
		$manomano_es_day = $this->get_orders_by($daily_sales, 'manomano', 'ES', 'channel-1');
		$bulevip_es_day = $this->get_orders_by($daily_sales, 'bulevip', 'ES', 'channel-1');
		$pixmania_es_day = $this->get_orders_by($daily_sales, 'pixmania', 'ES', 'channel-1');
		$pixmania_fr_day = $this->get_orders_by($daily_sales, 'pixmania', 'FR', 'channel-1');
		$elcorteingles_es_day = $this->get_orders_by($daily_sales, 'elcorteingles', 'ES', 'channel-1');
		$rakuten_fr_day = $this->get_orders_by($daily_sales, 'rakuten', 'FR', 'channel-1');
		$pccomp_it_day = $this->get_orders_by($daily_sales, 'pccomp', 'IT', 'channel-1');
		$pccomp_fr_day = $this->get_orders_by($daily_sales, 'pccomp', 'FR', 'channel-1');
		$mediamarkt_de_day = $this->get_orders_by($daily_sales, 'mediamarkt', 'DE', 'channel-1');
		$empik_pl_day = $this->get_orders_by($daily_sales, 'empik', 'PL', 'channel-1');
		$rueducommerce_fr_day = $this->get_orders_by($daily_sales, 'rueducommerce', 'FR', 'channel-1');
		$perfumesclub_pt_day = $this->get_orders_by($daily_sales, 'perfumesclub', 'PT', 'channel-1');
		$bigbang_si_day = $this->get_orders_by($daily_sales, 'bigbang', 'SI', 'channel-1');
		$eleclerc_fr_day = $this->get_orders_by($daily_sales, 'eleclerc', 'FR', 'channel-1');
		$clubefashion_day = $this->get_orders_by($daily_sales, 'clubefashion', 'PT', 'channel-1');
		$backmarket_day = $this->get_orders_by($daily_sales, 'backmarket', null, 'channel-1');		
		$aliexpress_day = $this->get_orders_by($daily_sales, 'aliexpress', null, 'channel-1');
		$kaufland_day = $this->get_orders_by($daily_sales, 'kaufland', 'DE', 'channel-1');
		$planetahuerto_day = $this->get_orders_by($daily_sales, 'planetahuerto', null, 'channel-1');
		$conrad_day = $this->get_orders_by($daily_sales, 'conrad', null, 'channel-1');
		$quirumedes_day = $this->get_orders_by($daily_sales, 'quirumedes', null, 'channel-1');
		$carrefour_fr_day = $this->get_orders_by($daily_sales, 'carrefour', 'FR', 'channel-1');
		$mediamarkt_es_day_2 = $this->get_orders_by($daily_sales,'mediamarkt', 'ES', 'channel-2');
		$carrefour_es_day_2 = $this->get_orders_by($daily_sales,'carrefour', 'ES', 'channel-2');

		$venteunique_pt_day = $this->get_orders_by($daily_sales, 'venteunique', 'PT', 'channel-1');
		$venteunique_es_day = $this->get_orders_by($daily_sales, 'venteunique', 'ES', 'channel-1');
		$venteunique_be_day = $this->get_orders_by($daily_sales, 'venteunique', 'BE', 'channel-1');
		$venteunique_de_day = $this->get_orders_by($daily_sales, 'venteunique', 'DE', 'channel-1');
		$venteunique_fr_day = $this->get_orders_by($daily_sales, 'venteunique', 'FR', 'channel-1');
		$venteunique_it_day = $this->get_orders_by($daily_sales, 'venteunique', 'IT', 'channel-1');
		$venteunique_nl_day = $this->get_orders_by($daily_sales, 'venteunique', 'NL', 'channel-1');

		$shopapotheke_de_day = $this->get_orders_by($daily_sales, 'shopapotheke', 'DE', 'channel-1');
		$shopapotheke_at_day = $this->get_orders_by($daily_sales, 'shopapotheke', 'AT', 'channel-1');
		$conforama_day = $this->get_orders_by($daily_sales, 'conforama', 'FR', 'channel-1');

		$macway_fr_day = $this->get_orders_by($daily_sales, 'macway', 'FR', 'channel-1');
		$tiendanimal_es_day = $this->get_orders_by($daily_sales, 'tiendanimal', 'ES', 'channel-1');
		$stockly_day = $this->get_orders_by($daily_sales, 'mktbighub', null);

		$ubaldi_fr_day_c2 = $this->get_orders_by($saleMonth, 'ubaldi', 'FR', 'channel-2');

		$data['ubaldi_fr_day_c2'] = $ubaldi_fr_day_c2;

		$data['worten_pt_day'] = $worten_pt_day;
		$data['worten_es_day'] = $worten_es_day;
		$data['worten_pt_day_2'] = $worten_pt_day_2;
		$data['worten_es_day_2'] = $worten_es_day_2;
		$data['carrefour_es_day'] = $carrefour_es_day;
		$data['mediamarkt_es_day'] = $mediamarkt_es_day;
		$data['fnac_pt_day'] = $fnac_pt_day;
		$data['fnac_es_day'] = $fnac_es_day;
		$data['fnac_fr_day'] = $fnac_fr_day;
		$data['eprice_it_day'] = $eprice_it_day;
		$data['pccomp_es_day'] = $pccomp_es_day;
		$data['pccomp_pt_day'] = $pccomp_pt_day;
		$data['makro_es_day'] = $makro_es_day;
		$data['makro_pt_day'] = $makro_pt_day;
		$data['leroy_es_day'] = $leroy_es_day;
		$data['phonehouse_es_day'] = $phonehouse_es_day;
		$data['cdiscount_fr_day'] = $cdiscount_fr_day;
		$data['cdiscount_fr_day_2'] = $cdiscount_fr_day_2;
		$data['miravia_es_day'] = $miravia_es_day;
		$data['manomano_es_day'] = $manomano_es_day;
		$data['bulevip_es_day'] = $bulevip_es_day;
		$data['pixmania_es_day'] = $pixmania_es_day;
		$data['pixmania_fr_day'] = $pixmania_fr_day;
		$data['elcorteingles_es_day'] = $elcorteingles_es_day;
		$data['rakuten_fr_day'] = $rakuten_fr_day;
		$data['pccomp_it_day'] = $pccomp_it_day;
		$data['pccomp_fr_day'] = $pccomp_fr_day;
		$data['mediamarkt_de_day'] = $mediamarkt_de_day;
		$data['empik_pl_day'] = $empik_pl_day;
		$data['rueducommerce_fr_day'] = $rueducommerce_fr_day;
		$data['perfumesclub_pt_day'] = $perfumesclub_pt_day;
		$data['bigbang_si_day'] = $bigbang_si_day;
		$data['eleclerc_fr_day'] = $eleclerc_fr_day;
		$data['clubefashion_day'] = $clubefashion_day;
		$data['backmarket_day'] = $backmarket_day;
		$data['aliexpress_day'] = $aliexpress_day;
		$data['kaufland_day'] = $kaufland_day;
		$data['planetahuerto_day'] = $planetahuerto_day;
		$data['conrad_day'] = $conrad_day;
		$data['quirumedes_day'] = $quirumedes_day;
		$data['carrefour_fr_day'] = $carrefour_fr_day;
		$data['mediamarkt_es_day_2'] = $mediamarkt_es_day_2;
		$data['carrefour_es_day_2'] = $carrefour_es_day_2;

		$data['venteunique_pt_day'] = $venteunique_pt_day;
		$data['venteunique_es_day'] = $venteunique_es_day;
		$data['venteunique_be_day'] = $venteunique_be_day;
		$data['venteunique_de_day'] = $venteunique_de_day;
		$data['venteunique_fr_day'] = $venteunique_fr_day;
		$data['venteunique_it_day'] = $venteunique_it_day;
		$data['venteunique_nl_day'] = $venteunique_nl_day;

		$data['shopapotheke_de_day'] = $shopapotheke_de_day;
		$data['shopapotheke_at_day'] = $shopapotheke_at_day;

		$data['conforama_day'] = $conforama_day;

		$data['macway_fr_day'] = $macway_fr_day;

		$data['tiendanimal_es_day'] = $tiendanimal_es_day;
		$data['stockly_day'] = $stockly_day;
		

		// Array de combinações site-pais
		// $combinations = [
		// 	['worten', 'PT'],
		// 	['worten', 'ES'],
		// 	['carrefour', 'ES'],
		// 	['mediamarkt', 'ES'],
		// 	['fnac', 'PT'],
		// 	['fnac', 'ES'],
		// 	['eprice', 'IT'],
		// 	['pccomp', 'ES'],
		// 	['pccomp', 'PT'],
		// 	['makro', 'ES'],
		// 	['makro', 'PT'],
		// 	['leroymerlin', 'ES'],
		// 	['phonehouse', 'ES'],
		// 	['cdiscount', 'FR'],
		// 	['miravia', 'ES'],
		// 	['manomano', 'ES'],
		// 	['bulevip', 'ES'],
		// 	['pixmania', 'ES'],
		// 	['pixmania', 'FR'],
		// 	['elcorteingles', 'ES'],
		// 	['rakuten', 'FR'],
		// 	['pccomp', 'IT'],
		// 	['pccomp', 'FR'],
		// 	['mediamarkt', 'DE'],
		// 	['empik', 'PL'],
		// 	['rueducommerce', 'FR'],
		// 	['perfumesclub', 'PT'],
		// 	['bigbang', 'SI'],
		// 	['eleclerc', 'FR'],
		// 	['clubefashion', 'PT'],
		// 	['backmarket', null],
		// 	['aliexpress', null],
		// 	['kaufland', null],
		// 	['planetahuerto', null],
		// 	['venteunique', null],
		// 	['conrad', null],
		// 	['quirumedes', null],
		// 	['carrefour', 'FR'],
		// 	['mediamarkt', 'ES', 'channel2']  // Exemplo com canal adicional
		// ];

		// foreach ($combinations as $combination) {
		// 	// Extrai o site, país e opcionalmente o canal (se existir)
		// 	$site = $combination[0];
		// 	$country = $combination[1] ?? null; // Caso não tenha país, será null
		// 	$channel_suffix = $combination[2] ?? ''; // Canal adicional, se houver
			
		// 	// Se o país for null, usa apenas o site na chave
		// 	if ($country === null) {
		// 		$key = "{$site}_day"; // Chave sem o país
		// 	} else {
		// 		// Converte o país para minúsculo
		// 		$country = strtolower($country);
				
		// 		// Se houver um canal, adiciona ao final
		// 		$key = "{$site}_{$country}_day" . ($channel_suffix ? "_{$channel_suffix}" : '');
		// 	}
			
		// 	// Verifica a chave gerada
		// 	echo "Gerando chave: {$key}\n";
			
		// 	// Chama a função get_orders_by e verifica se ela retorna valores
		// 	$result = $this->get_orders_by($daily_sales, $site, $country, $channel_suffix);
			
		// 	// Depura o resultado
		// 	var_dump($result);
		// 	die();
			
		// 	// Verifica se o resultado não é vazio antes de adicionar ao array $data
		// 	if ($result) {
		// 		$data[$key] = $result;
		// 	} else {
		// 		echo "Nenhum resultado para {$key}\n";
		// 	}
		// }
		

		$data['totalSalesDay'] = FormatarValor($totalSalesDay);
		$data['totalCommissionDay'] = FormatarValor($totalCommissionDay);
		$data['totalSalesMonth'] = FormatarValor($totalSalesMonth);
		$data['totalCommissionMonth'] = FormatarValor($totalCommissionMonth);

		# Ultimas 5 vendas
		$this->db->select('*');
		$this->db->from('tbl_orders');
		$this->db->limit(5);
		$this->db->order_by('id', 'desc');
		$data['orders'] = $this->db->get()->result();


		# Buscar os vendedores do dia de hoje
		$this->db->select('user_id, COUNT(id) as total_orders, SUM(total_price) as total_sales');
		$this->db->select("GROUP_CONCAT(DISTINCT CONCAT(channel, '-', country) SEPARATOR ', ') as channels", false);
		$this->db->from('tbl_orders');
		$this->db->where('DATE(created_date) >=', $data_dia);
		$this->db->where('DATE(created_date) <=', $data_dia);
		$this->db->group_by('user_id');
		$this->db->order_by('total_sales', 'desc');
		$sales_by_user = $this->db->get()->result();

		// Encontre o user_id que mais vendeu
		$top_selling_user = null;
		$max_sales = 0;

		foreach ($sales_by_user as $sale) {
			if ($sale->total_sales > $max_sales) {
				$max_sales = $sale->total_sales;
				$top_selling_user = $sale;
			}
		}

		// Mova o usuário que mais vendeu para o início do array
		if ($top_selling_user !== null) {
			$index = array_search($top_selling_user, $sales_by_user);
			if ($index !== false) {
				unset($sales_by_user[$index]);
				array_unshift($sales_by_user, $top_selling_user);
			}
		}

		$data['bandages'] = $sales_by_user;

		$data['view'] = 'dashboard/dashboard';
		$this->load->view('includes/template', $data);
	}

	private function get_orders_by_old($channel = null, $country = null, $data_ini, $data_fim, $account = null)
	{

		$this->db->select('id, reference, channel, country, state, total_price, total_commission, shipping_time, additional_info');

		$this->db->where('DATE(created_date) >=', $data_ini);
		$this->db->where('DATE(created_date) <=', $data_fim);

		if ($channel != null) {
			$this->db->where('channel', $channel);
		}

		if ($country != null) {
			$this->db->where('country', $country);
		}

		$this->db->order_by('id', 'desc');
		$result = $this->db->get('tbl_orders')->result();

		# Somando todos os valores 
		$totalPrice = 0;
		$totalCommission = 0;
		foreach ($result as $item) {

			if ($account != null) {
				$resp = json_decode($item->additional_info);
				if ($resp[0]->value == $account) {
					$totalPrice += floatval($item->total_price);
					$totalCommission += floatval($item->total_commission);
				}
			} else {
				// Verifica se é uma string JSON
				if (is_string($item->additional_info) && $item->additional_info === "[]") {
					#var_dump('entrou como string JSON com array vazio');
					$totalPrice += floatval($item->total_price);
					$totalCommission += floatval($item->total_commission);
				} elseif (
					(is_array($item->additional_info) && empty($item->additional_info)) ||
					(is_object($item->additional_info) && empty(get_object_vars($item->additional_info)))
				) {
					#var_dump('entrou como array ou objeto vazio');
					$totalPrice += floatval($item->total_price);
					$totalCommission += floatval($item->total_commission);
				} else {
					#var_dump('Não entrou no if do else');
					#var_dump($item->additional_info);
				}
			}
		}

		$data = [
			'result' => $result,
			'total_price' => round($totalPrice, 2),
			'total_commission' => round($totalCommission, 2),
			'count'  => count($result)
		];

		// if($channel == 'clubefashion'){
		// 	var_dump($channel, $country, $data_ini, $data_fim);
		// 	echo json_encode($data);
		// 	die();
		// }


		return $data;
	}


	private function get_orders_by($daily_sales, $channel, $country, $account = null)
	{

		$count_sales = 0;  // Inicializa o contador de vendas
		$totalPrice = 0;   // Inicializa o totalPrice
		$totalCommission = 0; // Inicializa o totalCommission

		
		foreach ($daily_sales as $sale) {

			if ($sale->channel == $channel && $sale->country == $country) {

				// Verifica se a variável $account não é nula
				if ($account != 'channel-1') {
					$resp = json_decode($sale->additional_info);
				
					// Verifique se $resp é um array ou objeto válido
					if (is_array($resp) && isset($resp[0]) && isset($resp[0]->value)) {
						$count_sales++;
						if ($resp[0]->value == $account) {
							$totalPrice += (float) $sale->total_price;
							$totalCommission += (float) $sale->total_commission;
						}
					} else {
						// Log ou mensagem de erro para ajudar no diagnóstico
						error_log("JSON inválido ou estrutura inesperada: " . $sale->additional_info);
					}
				}elseif($account == 'channel-1') {

					// Se additional_info for uma string JSON vazia, soma o totalPrice e totalCommission
					if (is_string($sale->additional_info) && $sale->additional_info === "[]" || $sale->additional_info === null) {
					
						$count_sales++;
						$totalPrice += (float) $sale->total_price;
						$totalCommission += (float) $sale->total_commission;

					} elseif (
						(is_array($sale->additional_info) && empty($sale->additional_info)) || 
						(is_object($sale->additional_info) && empty(get_object_vars($sale->additional_info)))
					) {
						$count_sales++;
						// Se additional_info for um array ou objeto vazio, soma o totalPrice e totalCommission
						$totalPrice += (float) $sale->total_price;
						$totalCommission += (float) $sale->total_commission;
					}
				} else {
					$count_sales++;
					// Garante que o total_price e total_commission sejam somados como floats
					$totalPrice += (float) $sale->total_price;
					$totalCommission += (float) $sale->total_commission;
				}
			} elseif (is_null($country)) {
				if ($sale->channel == $channel) { 
					#echo $sale->channel;
					$count_sales++;
					$totalPrice += (float) $sale->total_price;
					$totalCommission += (float) $sale->total_commission;
				}
			}
		}

		
		$data = [
			#'result' => $result,
			'total_price' => round($totalPrice, 2),
			'total_commission' => round($totalCommission, 2),
			'count'  => $count_sales,
			// 'channel' => $channel,
			// 'country' => $country,
			// 'account' => $account
		];

		// if($channel = 'worten'){
		// 	echo json_encode($data);
		// 	die();
		// }

		
		// if($channel == 'pixmania'){
		// 	echo json_encode($data);
		// 	die();
		// }
	
		return $data;
	}

}
