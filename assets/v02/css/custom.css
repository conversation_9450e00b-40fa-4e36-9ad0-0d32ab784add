.new-button{
    padding: 3px 9px 3px 9px;
    margin-bottom: 0px;
    font-size: 13px;
}

.new-button-2{
    padding: 3px 9px 3px 9px;
    margin-bottom: 0px;
    font-size: 11px;
}

.font-16{
    font-size: 16px!important;
}

.font-10{
    font-size: 10px!important;
}


.state-positive{
    background: #aee3b1!important;
    font-size: 13px!important;
    color: #025d05 ;
}

.state-negative{
    background: #e93e3d!important;
    font-size: 13px!important;
    color: #d7d9e1;
}

.state-neuter{
    background: #cccaca!important;
    font-size: 13px!important;
    color: #5c4f4f;
}

.badge-neuter{
    color: #5c4f4f;
    background-color: #cccaca;
}

.badge-negative{
    color: #d7d9e1;
    background-color: #e93e3d;
}



.margin-negativa-15{
    margin-top: -15px;
}

.color-hr{
    background: #3d3d3d;
}


.font-info-dashboard{
    font-size: 14px!important;
}
.font-info-dashboard-date{
    font-size: 17px!important;
}
.font-value-dashboard{
    font-size: 21px!important;
}

.font-euro-symbol{
    font-size: 15px;
    margin-right: 10px;
}

.rigth-important{
    text-align: right!important;
}

.left-important{
    text-align: left!important;
}

.margin-top-3{
    margin-top: 3px;
}

.background-order-summary{
    background: #f1f1f1;
    border-radius: 20px;
    padding: 1px 13px 11px 13px;
}

.margin-line{
    margin: 3px 0px 12px 0px;
}

.color-red{
    color: red;
}

.color-blue{
    color: blue;
}

.color-received-mkt{
    color:#5757ad;
}

.margin-filter{
    margin-bottom: 12px;
    margin-left: 12px;
}

.z-index-1{
    z-index: 1;
}

.hr-normal{
    background-color: #cccaca;
}

.order-paid{
    background-color: #6788b1;
    color: #ffffff;
}

.state-new{
    padding: 3px 7px!important;
    font-size: 9px!important;
    border-radius: 4px!important;
    font-weight: bolder!important;
}

.esconder{
    display: none;
}

.font-11-important{
    font-size: 12px!important;
}

.font25{
    font-size: 25px;
}

.background-box{
    background: #f9f1e7;
}

.t-c{
    text-align: center;
}

.f12-db{
    font-size: 13px;
    display: block;
}

.button-filter{
    text-align: right;
    margin-top: 20px;
}
.display-ib{
    display: inline-block;
}

.color-shipping-bighub{
    background: #ffe4c49e!important;
}

.border-0{
    border-radius: 0px !important;
}

/* Loading */
.c-loader {
    animation: is-rotating 1s infinite;
    border: 6px solid #e5e5e5;
    border-radius: 50%;
    border-top-color: #6788b1;
    height: 40px;
    width: 40px;
  }

  .elcorteringles-color{
    background: #05b105!important;
  }

  @keyframes is-rotating {
    to {
      transform: rotate(1turn);
    }
  }


  .timeline-day{
    color: #ffffff;
  }

  .color-icon-impersonate{
    color: #ffffff !important;
  }