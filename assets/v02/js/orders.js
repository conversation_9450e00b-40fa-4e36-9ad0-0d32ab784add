/** Modal para os order details */

let id = 0;
let fsetId = true
let fsetIdClient = true

function setId(_id) {
	$(".modal-body").html('Calma, carregando os dados da Order..');
	id = _id
	fsetId = true
}

function setIdClient(_id) {
	$(".modal-body").html('Calma, carregando os dados do Seller..');
	id = _id
	fsetIdClient = true
}

$(document).ready(function () {
	if(fsetId){
		$('#modal-dados-order').on('shown.bs.modal', function () {
			$(this).find(".modal-body").load(base_url + "/order-details/" + id);
		});
	}
	if(fsetIdClient){
		$('#modal-dados-cliente').on('shown.bs.modal', function () {
			$(this).find(".modal-body").load(base_url + "/client-details/" + id);
		});
	}
	
})





function checkOrder(order_id, purchase) {
	action = 0
	msg = 'Compra desmarcada com sucesso.'
	if (purchase == 1) {
		action = 1
		msg = 'Compra marcada com sucesso.'
	}
	$.ajax({
		url: base_url + '/update-purchased-order',
		type: 'POST',
		dataType: 'json',
		data: {
			order: order_id,
			action: action
		},
		success: function (response) {
			alert(msg);
		},
		error: function (xhr, status, error) {
			// Lógica de tratamento de erros
			console.error(xhr.responseText);
		}
	});
}


function generateInvoice(orderId) {
	$("#invoice-loading-"+orderId).removeClass("esconder");
	$("#generate-invoice-"+orderId).prop("disabled", true);
	console.log('foi');
	
	$.ajax({
		url: base_url + '/generate-invoice',
		type: 'POST',
		dataType: 'json',
		data: {
			order_id: orderId
		},
		success: function (response) {
			if(response.status === 'success' ){
				$("#invoice-loading-"+orderId).addClass("esconder");
				$("#generate-invoice-"+orderId).addClass("esconder");
				$("#see-invoice-"+orderId).removeClass("esconder");
				$("#see-invoice-"+orderId).attr("href", "https://prd-mkp.bighub.store/invoices/"+response.id+".pdf");
			}
		},
		error: function (xhr, status, error) {
			// Lógica de tratamento de erros
			console.error(xhr.responseText);
		}
	});
}