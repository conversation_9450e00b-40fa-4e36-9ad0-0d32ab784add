function chamarCurlImpersonate(user_token) {
  $.ajax({
    url: base_url + "/impersonate",
    type: "POST",
    dataType: "json",
    data: {
      user: user_token
    },
    success: function (response) {
      var token = response;
      var redirectUrl = "https://app.bighub.store?token=" + token;
      window.open(redirectUrl, "_blank");
    },
    error: function (xhr, status, error) {
      // Lógica de tratamento de erros
      console.error(xhr.responseText);
    },
  });
}

function getDataIban(user_token, id_tbl_company) {
  console.log(user_token, id_tbl_company);

  $.ajax({
    url: base_url + "/impersonate",
    type: "POST",
    dataType: "json",
    data: {
      user: user_token,
    },
    success: function (response) {
      var token = response;
      var redirectUrl =
        "https://app.bighub.store/api/v2/store/companies/" +
        id_tbl_company +
        "/attachments?token=" +
        token;
      window.open(redirectUrl, "_blank");
    },
    error: function (xhr, status, error) {
      // Lógica de tratamento de erros
      console.error(xhr.responseText);
    },
  });
}

function updateStatusUser(user_token, status) {
  $.ajax({
    url: base_url + "/update-status-user",
    type: "POST",
    dataType: "json",
    data: {
      user: user_token,
      status: status,
    },
    success: function (response) {
      if (status == "active") {
        show_button_inactive(user_token);
      } else {
        show_button_active(user_token);
      }
    },
    error: function (xhr, status, error) {
      // Lógica de tratamento de erros
      console.error(xhr.responseText);
    },
  });
}

function checkPortes(id, action) {
	$.ajax({
		url: base_url + "check-shipping",
		type: "POST",
		dataType: "json",
		data: {
			user_id: id,
			action_id: action
		},
		success: function(response) {
			if (response.validate === true) {
				if(action === 0){
					document.querySelector(".portes-checked-" + id).checked = true;
          var div = document.getElementById('text-value-shipping-' + id);
          div.textContent = 'bighub';

          var inputElement = document.querySelector(".portes-checked-" + id);
          inputElement.setAttribute('onclick', 'checkPortes('+id+', 1)');
				}
				if(action === 1){
					document.querySelector(".portes-checked-" + id).checked = false;
          var div = document.getElementById('text-value-shipping-' + id);
          div.textContent = 'seller';

          var inputElement = document.querySelector(".portes-checked-" + id);
          inputElement.setAttribute('onclick', 'checkPortes('+id+', 0)');
				}
			} else {
				document.querySelector(".portes-checked-" + id).checked = false;
				alert("Deve existir portes configurados para ativação dos portes BIGhub");
				
			}
		},
		error: function(xhr, status, error) {
			// Lógica de tratamento de erros
			console.error(xhr.responseText);
			document.querySelector(".portes-checked-" + id).checked = false;
			alert("Erro de conexão com o servidor. Por favor, tente novamente mais tarde.");
			return false; // Isso para o processo
		},
	});	
}


function show_button_active(user_token) {
  var elementoOriginal = document.querySelector(".userativo_" + user_token);

  // Criar um novo elemento span
  var novoElemento = document.createElement("span");

  // Definir o valor do atributo id
  novoElemento.setAttribute("id", "user_" + user_token);

  // Adicionar as classes 'badge-sm' e 'badge-secondary'
  novoElemento.classList.add(
    "badge-sm",
    "badge-secondary",
    "userinativo_" + user_token
  );

  // Definir o conteúdo do novo elemento
  novoElemento.textContent = "Por ativar";

  // Substituir o elemento original pelo novo elemento
  elementoOriginal.parentNode.replaceChild(novoElemento, elementoOriginal);

  // Selecionar o botão com a classe 'new-button'
  var botaoOriginal = document.getElementById("button_user_" + user_token);

  // Atualizar as classes e os valores do botão
  botaoOriginal.classList.remove("btn-primary");
  botaoOriginal.classList.add("btn-warning");
  botaoOriginal.setAttribute(
    "onclick",
    "updateStatusUser('" + user_token + "', 'active')"
  );
  botaoOriginal.textContent = "Ativar";
}

function show_button_inactive(user_token) {
  var elementoOriginal = document.querySelector(".userinativo_" + user_token);

  // Criar um novo elemento span
  var novoElemento = document.createElement("span");

  // Definir o valor do atributo id
  novoElemento.setAttribute("id", "user_" + user_token);

  // Adicionar as classes 'badge-sm' e 'badge-secondary'
  novoElemento.classList.add(
    "badge-sm",
    "badge-success",
    "userativo_" + user_token
  );

  // Definir o conteúdo do novo elemento
  novoElemento.textContent = "Ativo";

  // Substituir o elemento original pelo novo elemento
  elementoOriginal.parentNode.replaceChild(novoElemento, elementoOriginal);

  // Selecionar o botão com a classe 'new-button'
  var botaoOriginal = document.getElementById("button_user_" + user_token);

  // Atualizar as classes e os valores do botão
  botaoOriginal.classList.remove("btn-warning");
  botaoOriginal.classList.add("btn-primary");
  botaoOriginal.setAttribute(
    "onclick",
    "updateStatusUser('" + user_token + "', 'inactive')"
  );
  botaoOriginal.textContent = "Inativar";
}
