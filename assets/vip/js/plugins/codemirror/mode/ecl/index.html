<!doctype html>

<title>CodeMirror: ECL mode</title>
<meta charset="utf-8"/>
<link rel=stylesheet href="../../doc/docs.css">

<link rel="stylesheet" href="../../lib/codemirror.css">
<script src="../../lib/codemirror.js"></script>
<script src="ecl.js"></script>
<style>.CodeMirror {border: 1px solid black;}</style>
<div id=nav>
  <a href="http://codemirror.net"><h1>CodeMirror</h1><img id=logo src="../../doc/logo.png"></a>

  <ul>
    <li><a href="../../index.html">Home</a>
    <li><a href="../../doc/manual.html">Manual</a>
    <li><a href="https://github.com/codemirror/codemirror">Code</a>
  </ul>
  <ul>
    <li><a href="../index.html">Language modes</a>
    <li><a class=active href="#">ECL</a>
  </ul>
</div>

<article>
<h2>ECL mode</h2>
<form><textarea id="code" name="code">
/*
sample useless code to demonstrate ecl syntax highlighting
this is a multiline comment!
*/

//  this is a singleline comment!

import ut;
r := 
  record
   string22 s1 := '123';
   integer4 i1 := 123;
  end;
#option('tmp', true);
d := dataset('tmp::qb', r, thor);
output(d);
</textarea></form>
    <script>
      var editor = CodeMirror.fromTextArea(document.getElementById("code"), {});
    </script>

    <p>Based on CodeMirror's clike mode.  For more information see <a href="http://hpccsystems.com">HPCC Systems</a> web site.</p>
    <p><strong>MIME types defined:</strong> <code>text/x-ecl</code>.</p>

  </article>
