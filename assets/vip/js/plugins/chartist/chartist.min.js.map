{"version": 3, "file": "chartist.min.js", "sources": ["chartist.js"], "names": ["root", "factory", "define", "amd", "exports", "module", "this", "Chartist", "version", "window", "document", "noop", "n", "alphaNumerate", "String", "fromCharCode", "extend", "target", "sources", "Array", "prototype", "slice", "call", "arguments", "for<PERSON>ach", "source", "prop", "replaceAll", "str", "subStr", "newSubStr", "replace", "RegExp", "stripUnit", "value", "ensureUnit", "unit", "querySelector", "query", "Node", "times", "length", "apply", "sum", "previous", "current", "mapMultiply", "factor", "num", "mapAdd", "addend", "serialMap", "arr", "cb", "result", "Math", "max", "map", "e", "index", "args", "roundWithPrecision", "digits", "precision", "pow", "round", "escapingMap", "&", "<", ">", "\"", "'", "serialize", "data", "undefined", "JSON", "stringify", "Object", "keys", "reduce", "key", "deserialize", "parse", "createSvg", "container", "width", "height", "className", "svg", "querySelectorAll", "filter", "getAttribute", "xmlNs", "qualifiedName", "<PERSON><PERSON><PERSON><PERSON>", "Svg", "attr", "addClass", "style", "append<PERSON><PERSON><PERSON>", "_node", "reverseData", "labels", "reverse", "series", "i", "getDataArray", "recursiveConvert", "isNaN", "hasOwnProperty", "reversed", "normalizePadding", "padding", "fallback", "top", "right", "bottom", "left", "normalizeDataArray", "dataArray", "j", "getMetaData", "meta", "orderOfMagnitude", "floor", "log", "abs", "LN10", "projectLength", "axisLength", "bounds", "range", "getAvailableHeight", "options", "chartPadding", "axisX", "offset", "getHighLow", "recursiveHighLow", "findHigh", "highLow", "high", "findLow", "low", "Number", "MAX_VALUE", "rho", "gcd", "p", "q", "f", "x", "divisor", "x1", "x2", "getBounds", "scaleMinSpace", "referenceValue", "only<PERSON><PERSON><PERSON>", "newMin", "newMax", "min", "valueRange", "oom", "step", "ceil", "numberOfSteps", "scaleUp", "smallestFactor", "values", "push", "polarToCartesian", "centerX", "centerY", "radius", "angleInDegrees", "angleInRadians", "PI", "cos", "y", "sin", "createChartRect", "fallbackPadding", "has<PERSON><PERSON><PERSON>", "axisY", "yAxisOffset", "xAxisOffset", "normalizedPadding", "chartRect", "y1", "y2", "position", "createGrid", "projectedValue", "axis", "group", "classes", "eventEmitter", "positionalData", "units", "pos", "counterUnits", "gridElement", "elem", "join", "emit", "type", "element", "createLabel", "axisOffset", "labelOffset", "useForeignObject", "labelElement", "len", "content", "foreignObject", "text", "createAxis", "gridGroup", "labelGroup", "axisOptions", "toUpperCase", "projectedV<PERSON>ues", "projectValue", "bind", "labelValues", "labelInterpolationFnc", "showGrid", "gridOffset", "classNames", "grid", "dir", "showLabel", "label", "getSeriesOption", "name", "seriesOptions", "optionsProvider", "responsiveOptions", "updateCurrentOptions", "preventChangedEvent", "previousOptions", "currentOptions", "baseOptions", "mql", "matchMedia", "matches", "removeMediaQueryListeners", "mediaQueryListeners", "removeListener", "addListener", "getCurrentOptions", "Interpolation", "none", "pathCoordinates", "valueData", "path", "Path", "hole", "move", "line", "simple", "defaultOptions", "d", "prevX", "prevY", "currX", "currY", "prevData", "currData", "curve", "cardinal", "splitIntoSegments", "segments", "tension", "t", "c", "paths", "segment", "z", "iLen", "postpone", "EventEmitter", "addEventHandler", "event", "handler", "handlers", "removeEventHandler", "splice", "indexOf", "<PERSON><PERSON><PERSON><PERSON>", "listToArray", "list", "properties", "superProtoOverride", "superProto", "Class", "proto", "create", "cloneDefinitions", "constr", "instance", "fn", "constructor", "getOwnPropertyNames", "propName", "defineProperty", "getOwnPropertyDescriptor", "update", "override", "initializeTimeoutId", "createChart", "detach", "clearTimeout", "removeEventListener", "resizeListener", "on", "off", "initialize", "addEventListener", "plugins", "plugin", "Base", "supportsForeignObject", "isSupported", "supportsAnimations", "__chartist__", "setTimeout", "Error", "attributes", "parent", "insertFirst", "Element", "createElementNS", "svgNs", "setAttributeNS", "uri", "<PERSON><PERSON><PERSON><PERSON>", "insertBefore", "ns", "getAttributeNS", "prefix", "setAttribute", "parentNode", "SVGElement", "node", "nodeName", "selector", "foundNode", "foundNodes", "List", "createElement", "innerHTML", "xhtmlNs", "fnObj", "createTextNode", "empty", "remove", "newElement", "<PERSON><PERSON><PERSON><PERSON>", "append", "trim", "split", "names", "concat", "self", "removeClass", "removedClasses", "removeAllClasses", "getBBoxProperty", "getBBox", "clientHeight", "clientWidth", "animate", "animations", "guided", "attribute", "createAnimate", "animationDefinition", "timeout", "easing", "attributeProperties", "Easing", "begin", "dur", "calcMode", "keySplines", "keyTimes", "fill", "from", "attributeName", "beginElement", "err", "to", "params", "SvgList", "nodeList", "svgElements", "prototypeProperty", "feature", "implementation", "hasFeature", "easingCubicBeziers", "easeInSine", "easeOutSine", "easeInOutSine", "easeInQuad", "easeOutQuad", "easeInOutQuad", "easeInCubic", "easeOutCubic", "easeInOutCubic", "easeInQuart", "easeOutQuart", "easeInOutQuart", "easeInQuint", "easeOutQuint", "easeInOutQuint", "easeInExpo", "easeOutExpo", "easeInOutExpo", "easeInCirc", "easeOutCirc", "easeInOutCirc", "easeInBack", "easeOutBack", "easeInOutBack", "command", "pathElements", "relative", "pathElement", "toLowerCase", "forEachParam", "pathElementIndex", "elementDescriptions", "paramName", "paramIndex", "SvgPath", "close", "count", "arc", "rx", "ry", "xAr", "lAf", "sf", "chunks", "match", "pop", "elements", "chunk", "shift", "description", "spliceArgs", "accuracyMultiplier", "accuracy", "scale", "translate", "transform", "transformFnc", "transformed", "clone", "splitByCommand", "joinedPath", "m", "l", "a", "Axis", "axisUnits", "rectEnd", "rectStart", "rectOffset", "LinearScaleAxis", "axisUnit", "StepAxis", "<PERSON><PERSON><PERSON><PERSON>", "stepCount", "stretch", "seriesGroups", "normalizedData", "chart", "fullWidth", "seriesIndex", "series-name", "pathData", "valueIndex", "lineSmooth", "showPoint", "showLine", "showArea", "smoothing", "point", "areaBase", "areaBaseProjected", "pathSegment", "solidPathSegments", "firstElement", "lastElement", "areaPath", "area", "Line", "vertical", "horizontal", "start", "end", "distributeSeries", "horizontalBars", "stackBars", "serialSums", "valueAxis", "labelAxisStepCount", "labelAxis", "zeroPoint", "stackedBarV<PERSON>ues", "periodHalf<PERSON>ength", "biPol", "projected", "bar", "previousStack", "labelAxisValueIndex", "seriesBarDistance", "positions", "Bar", "determineAnchorPosition", "center", "direction", "toTheRight", "labelRadius", "totalDataSum", "startAngle", "donut", "chartDonut", "chartPie", "total", "previousValue", "currentValue", "donut<PERSON>idth", "labelPosition", "hasSingleValInSeries", "val", "endAngle", "sliceDonut", "slice<PERSON>ie", "interpolatedV<PERSON>ue", "dx", "dy", "text-anchor", "labelDirection", "Pie"], "mappings": ";;;;;;CAAC,SAAUA,EAAMC,GACO,kBAAXC,SAAyBA,OAAOC,IAEzCD,UAAW,WACT,MAAQF,GAAe,SAAIC,MAED,gBAAZG,SAIhBC,OAAOD,QAAUH,IAEjBD,EAAe,SAAIC,KAErBK,KAAM,WAYR,GAAIC,IACFC,QAAS,QA2xHX,OAxxHC,UAAUC,EAAQC,EAAUH,GAC3B,YASAA,GAASI,KAAO,SAAUC,GACxB,MAAOA,IAUTL,EAASM,cAAgB,SAAUD,GAEjC,MAAOE,QAAOC,aAAa,GAAKH,EAAI,KAWtCL,EAASS,OAAS,SAAUC,GAC1BA,EAASA,KAET,IAAIC,GAAUC,MAAMC,UAAUC,MAAMC,KAAKC,UAAW,EAWpD,OAVAL,GAAQM,QAAQ,SAASC,GACvB,IAAK,GAAIC,KAAQD,GACa,gBAAjBA,GAAOC,IAAuC,OAAjBD,EAAOC,IAAoBD,EAAOC,YAAiBP,OAGzFF,EAAOS,GAAQD,EAAOC,GAFtBT,EAAOS,GAAQnB,EAASS,UAAWC,EAAOS,GAAOD,EAAOC,MAOvDT,GAYTV,EAASoB,WAAa,SAASC,EAAKC,EAAQC,GAC1C,MAAOF,GAAIG,QAAQ,GAAIC,QAAOH,EAAQ,KAAMC,IAU9CvB,EAAS0B,UAAY,SAASC,GAK5B,MAJoB,gBAAVA,KACRA,EAAQA,EAAMH,QAAQ,eAAgB,MAGhCG,GAWV3B,EAAS4B,WAAa,SAASD,EAAOE,GAKpC,MAJoB,gBAAVF,KACRA,GAAgBE,GAGXF,GAUT3B,EAAS8B,cAAgB,SAASC,GAChC,MAAOA,aAAiBC,MAAOD,EAAQ5B,EAAS2B,cAAcC,IAUhE/B,EAASiC,MAAQ,SAASC,GACxB,MAAOtB,OAAMuB,MAAM,KAAM,GAAIvB,OAAMsB,KAWrClC,EAASoC,IAAM,SAASC,EAAUC,GAChC,MAAOD,IAAYC,EAAUA,EAAU,IAUzCtC,EAASuC,YAAc,SAASC,GAC9B,MAAO,UAASC,GACd,MAAOA,GAAMD,IAWjBxC,EAAS0C,OAAS,SAASC,GACzB,MAAO,UAASF,GACd,MAAOA,GAAME,IAYjB3C,EAAS4C,UAAY,SAASC,EAAKC,GACjC,GAAIC,MACAb,EAASc,KAAKC,IAAId,MAAM,KAAMU,EAAIK,IAAI,SAASC,GAC7C,MAAOA,GAAEjB,SAWf,OARAlC,GAASiC,MAAMC,GAAQjB,QAAQ,SAASkC,EAAGC,GACzC,GAAIC,GAAOR,EAAIK,IAAI,SAASC,GAC1B,MAAOA,GAAEC,IAGXL,GAAOK,GAASN,EAAGX,MAAM,KAAMkB,KAG1BN,GAWT/C,EAASsD,mBAAqB,SAAS3B,EAAO4B,GAC5C,GAAIC,GAAYR,KAAKS,IAAI,GAAIF,GAAUvD,EAASwD,UAChD,OAAOR,MAAKU,MAAM/B,EAAQ6B,GAAaA,GASzCxD,EAASwD,UAAY,EAQrBxD,EAAS2D,aACPC,IAAK,QACLC,IAAK,OACLC,IAAK,OACLC,IAAK,SACLC,IAAM,UAWRhE,EAASiE,UAAY,SAASC,GAC5B,MAAY,QAATA,GAA0BC,SAATD,EACXA,GACiB,gBAATA,GACfA,EAAO,GAAGA,EACc,gBAATA,KACfA,EAAOE,KAAKC,WAAWH,KAAMA,KAGxBI,OAAOC,KAAKvE,EAAS2D,aAAaa,OAAO,SAASzB,EAAQ0B,GAC/D,MAAOzE,GAASoB,WAAW2B,EAAQ0B,EAAKzE,EAAS2D,YAAYc,KAC5DP,KAULlE,EAAS0E,YAAc,SAASR,GAC9B,GAAmB,gBAATA,GACR,MAAOA,EAGTA,GAAOI,OAAOC,KAAKvE,EAAS2D,aAAaa,OAAO,SAASzB,EAAQ0B,GAC/D,MAAOzE,GAASoB,WAAW2B,EAAQ/C,EAAS2D,YAAYc,GAAMA,IAC7DP,EAEH,KACEA,EAAOE,KAAKO,MAAMT,GAClBA,EAAqBC,SAAdD,EAAKA,KAAqBA,EAAKA,KAAOA,EAC7C,MAAMf,IAER,MAAOe,IAaTlE,EAAS4E,UAAY,SAAUC,EAAWC,EAAOC,EAAQC,GACvD,GAAIC,EAwBJ,OAtBAH,GAAQA,GAAS,OACjBC,EAASA,GAAU,OAInBnE,MAAMC,UAAUC,MAAMC,KAAK8D,EAAUK,iBAAiB,QAAQC,OAAO,SAAkCF,GACrG,MAAOA,GAAIG,aAAapF,EAASqF,MAAMC,iBACtCrE,QAAQ,SAA+BgE,GACxCJ,EAAUU,YAAYN,KAIxBA,EAAM,GAAIjF,GAASwF,IAAI,OAAOC,MAC5BX,MAAOA,EACPC,OAAQA,IACPW,SAASV,GAAWS,MACrBE,MAAO,UAAYb,EAAQ,aAAeC,EAAS,MAIrDF,EAAUe,YAAYX,EAAIY,OAEnBZ,GAUTjF,EAAS8F,YAAc,SAAS5B,GAC9BA,EAAK6B,OAAOC,UACZ9B,EAAK+B,OAAOD,SACZ,KAAK,GAAIE,GAAI,EAAGA,EAAIhC,EAAK+B,OAAO/D,OAAQgE,IACR,gBAApBhC,GAAK+B,OAAOC,IAA4C/B,SAAxBD,EAAK+B,OAAOC,GAAGhC,KACvDA,EAAK+B,OAAOC,GAAGhC,KAAK8B,UACZ9B,EAAK+B,OAAOC,YAActF,QAClCsD,EAAK+B,OAAOC,GAAGF,WAarBhG,EAASmG,aAAe,SAAUjC,EAAM8B,GAWtC,QAASI,GAAiBzE,GACxB,MAAawC,UAAVxC,GAAiC,OAAVA,GAAoC,gBAAVA,IAAsB0E,MAAM1E,GACvEwC,QACExC,EAAMuC,MAAQvC,YAAkBf,QACjCe,EAAMuC,MAAQvC,GAAOuB,IAAIkD,GACzBzE,EAAM2E,eAAe,SACtBF,EAAiBzE,EAAMA,QAEtBA,EAIZ,OAnBGqE,IAAY9B,EAAKqC,WAAaP,GAAW9B,EAAKqC,YAC/CvG,EAAS8F,YAAY5B,GACrBA,EAAKqC,UAAYrC,EAAKqC,UAiBjBrC,EAAK+B,OAAO/C,IAAIkD,IAWzBpG,EAASwG,iBAAmB,SAASC,EAASC,GAG5C,MAFAA,GAAWA,GAAY,EAEG,gBAAZD,IACZE,IAAKF,EACLG,MAAOH,EACPI,OAAQJ,EACRK,KAAML,IAENE,IAA4B,gBAAhBF,GAAQE,IAAmBF,EAAQE,IAAMD,EACrDE,MAAgC,gBAAlBH,GAAQG,MAAqBH,EAAQG,MAAQF,EAC3DG,OAAkC,gBAAnBJ,GAAQI,OAAsBJ,EAAQI,OAASH,EAC9DI,KAA8B,gBAAjBL,GAAQK,KAAoBL,EAAQK,KAAOJ,IAY5D1G,EAAS+G,mBAAqB,SAAUC,EAAW9E,GACjD,IAAK,GAAIgE,GAAI,EAAGA,EAAIc,EAAU9E,OAAQgE,IACpC,GAAIc,EAAUd,GAAGhE,SAAWA,EAI5B,IAAK,GAAI+E,GAAID,EAAUd,GAAGhE,OAAYA,EAAJ+E,EAAYA,IAC5CD,EAAUd,GAAGe,GAAK9C,MAItB,OAAO6C,IAGThH,EAASkH,YAAc,SAASjB,EAAQ7C,GACtC,GAAIzB,GAAQsE,EAAO/B,KAAO+B,EAAO/B,KAAKd,GAAS6C,EAAO7C,EACtD,OAAOzB,GAAQ3B,EAASiE,UAAUtC,EAAMwF,MAAQhD,QAUlDnE,EAASoH,iBAAmB,SAAUzF,GACpC,MAAOqB,MAAKqE,MAAMrE,KAAKsE,IAAItE,KAAKuE,IAAI5F,IAAUqB,KAAKwE,OAYrDxH,EAASyH,cAAgB,SAAUC,EAAYxF,EAAQyF,GACrD,MAAOzF,GAASyF,EAAOC,MAAQF,GAWjC1H,EAAS6H,mBAAqB,SAAU5C,EAAK6C,GAC3C,MAAO9E,MAAKC,KAAKjD,EAAS0B,UAAUoG,EAAQ/C,SAAWE,EAAIF,WAAa+C,EAAQC,aAAapB,IAAOmB,EAAQC,aAAalB,QAAUiB,EAAQE,MAAMC,OAAQ,IAW3JjI,EAASkI,WAAa,SAAUlB,EAAWc,GASzC,QAASK,GAAiBjE,GACxB,GAAGA,YAAgBtD,OACjB,IAAK,GAAIsF,GAAI,EAAGA,EAAIhC,EAAKhC,OAAQgE,IAC/BiC,EAAiBjE,EAAKgC,QAGpBkC,IAAYlE,EAAOmE,EAAQC,OAC7BD,EAAQC,KAAOpE,GAGbqE,GAAWrE,EAAOmE,EAAQG,MAC5BH,EAAQG,IAAMtE,GAnBpB,GAAImE,IACAC,KAAuBnE,SAAjB2D,EAAQQ,MAAsBG,OAAOC,WAAaZ,EAAQQ,KAChEE,IAAqBrE,SAAhB2D,EAAQU,IAAoBC,OAAOC,WAAaZ,EAAQU,KAE/DJ,EAA4BjE,SAAjB2D,EAAQQ,KACnBC,EAA0BpE,SAAhB2D,EAAQU,GAqCpB,OAjBAL,GAAiBnB,GAIbqB,EAAQC,MAAQD,EAAQG,MAEN,IAAhBH,EAAQG,IACVH,EAAQC,KAAO,EACND,EAAQG,IAAM,EAEvBH,EAAQC,KAAO,EAGfD,EAAQG,IAAM,GAIXH,GAUTrI,EAAS2I,IAAM,SAASlG,GAKtB,QAASmG,GAAIC,EAAGC,GACd,MAAID,GAAIC,IAAM,EACLA,EAEAF,EAAIE,EAAGD,EAAIC,GAItB,QAASC,GAAEC,GACT,MAAOA,GAAIA,EAAI,EAbjB,GAAW,IAARvG,EACD,MAAOA,EAeT,IAAoBwG,GAAhBC,EAAK,EAAGC,EAAK,CACjB,IAAI1G,EAAM,IAAM,EACd,MAAO,EAGT,GACEyG,GAAKH,EAAEG,GAAMzG,EACb0G,EAAKJ,EAAEA,EAAEI,IAAO1G,EAChBwG,EAAUL,EAAI5F,KAAKuE,IAAI2B,EAAKC,GAAK1G,SACd,IAAZwG,EAET,OAAOA,IAcTjJ,EAASoJ,UAAY,SAAU1B,EAAYW,EAASgB,EAAeC,EAAgBC,GACjF,GAAIrD,GACFsD,EACAC,EACA9B,GACEW,KAAMD,EAAQC,KACdE,IAAKH,EAAQG,MAMbc,GAAqC,IAAnBA,KACpB3B,EAAOW,KAAOtF,KAAKC,IAAIqG,EAAgB3B,EAAOW,MAC9CX,EAAOa,IAAMxF,KAAK0G,IAAIJ,EAAgB3B,EAAOa,MAG/Cb,EAAOgC,WAAahC,EAAOW,KAAOX,EAAOa,IACzCb,EAAOiC,IAAM5J,EAASoH,iBAAiBO,EAAOgC,YAC9ChC,EAAOkC,KAAO7G,KAAKS,IAAI,GAAIkE,EAAOiC,KAClCjC,EAAO+B,IAAM1G,KAAKqE,MAAMM,EAAOa,IAAMb,EAAOkC,MAAQlC,EAAOkC,KAC3DlC,EAAO1E,IAAMD,KAAK8G,KAAKnC,EAAOW,KAAOX,EAAOkC,MAAQlC,EAAOkC,KAC3DlC,EAAOC,MAAQD,EAAO1E,IAAM0E,EAAO+B,IACnC/B,EAAOoC,cAAgB/G,KAAKU,MAAMiE,EAAOC,MAAQD,EAAOkC,KAIxD,IAAI3H,GAASlC,EAASyH,cAAcC,EAAYC,EAAOkC,KAAMlC,GACzDqC,EAAmBX,EAATnH,EACV+H,EAAiBV,EAAcvJ,EAAS2I,IAAIhB,EAAOC,OAAS,CAGhE,IAAG2B,GAAevJ,EAASyH,cAAcC,EAAY,EAAGC,IAAW0B,EACjE1B,EAAOkC,KAAO,MACT,IAAGN,GAAeU,EAAiBtC,EAAOkC,MAAQ7J,EAASyH,cAAcC,EAAYuC,EAAgBtC,IAAW0B,EAIrH1B,EAAOkC,KAAOI,MAGd,QACE,GAAID,GAAWhK,EAASyH,cAAcC,EAAYC,EAAOkC,KAAMlC,IAAW0B,EACxE1B,EAAOkC,MAAQ,MACV,CAAA,GAAKG,KAAWhK,EAASyH,cAAcC,EAAYC,EAAOkC,KAAO,EAAGlC,IAAW0B,GAOpF,KALA,IADA1B,EAAOkC,MAAQ,EACZN,GAAe5B,EAAOkC,KAAO,IAAM,EAAG,CACvClC,EAAOkC,MAAQ,CACf,QAWR,IAFAL,EAAS7B,EAAO+B,IAChBD,EAAS9B,EAAO1E,IACVuG,EAAS7B,EAAOkC,MAAQlC,EAAOa,KACnCgB,GAAU7B,EAAOkC,IAEnB,MAAMJ,EAAS9B,EAAOkC,MAAQlC,EAAOW,MACnCmB,GAAU9B,EAAOkC,IAOnB,KALAlC,EAAO+B,IAAMF,EACb7B,EAAO1E,IAAMwG,EACb9B,EAAOC,MAAQD,EAAO1E,IAAM0E,EAAO+B,IAEnC/B,EAAOuC,UACFhE,EAAIyB,EAAO+B,IAAKxD,GAAKyB,EAAO1E,IAAKiD,GAAKyB,EAAOkC,KAChDlC,EAAOuC,OAAOC,KAAKnK,EAASsD,mBAAmB4C,GAGjD,OAAOyB,IAaT3H,EAASoK,iBAAmB,SAAUC,EAASC,EAASC,EAAQC,GAC9D,GAAIC,IAAkBD,EAAiB,IAAMxH,KAAK0H,GAAK,GAEvD,QACE1B,EAAGqB,EAAWE,EAASvH,KAAK2H,IAAIF,GAChCG,EAAGN,EAAWC,EAASvH,KAAK6H,IAAIJ,KAapCzK,EAAS8K,gBAAkB,SAAU7F,EAAK6C,EAASiD,GACjD,GAAIC,MAAalD,EAAQE,QAASF,EAAQmD,OACtCC,EAAcF,EAAUlD,EAAQmD,MAAMhD,OAAS,EAC/CkD,EAAcH,EAAUlD,EAAQE,MAAMC,OAAS,EAE/CnD,EAAQG,EAAIH,SAAW9E,EAAS0B,UAAUoG,EAAQhD,QAAU,EAC5DC,EAASE,EAAIF,UAAY/E,EAAS0B,UAAUoG,EAAQ/C,SAAW,EAC/DqG,EAAoBpL,EAASwG,iBAAiBsB,EAAQC,aAAcgD,EAGxEjG,GAAQ9B,KAAKC,IAAI6B,EAAOqG,EAAcC,EAAkBtE,KAAOsE,EAAkBxE,OACjF7B,EAAS/B,KAAKC,IAAI8B,EAAQmG,EAAcE,EAAkBzE,IAAMyE,EAAkBvE,OAElF,IAAIwE,IACF5E,QAAS2E,EACTtG,MAAO,WACL,MAAO/E,MAAKoJ,GAAKpJ,KAAKmJ,IAExBnE,OAAQ,WACN,MAAOhF,MAAKuL,GAAKvL,KAAKwL,IA2B1B,OAvBGP,IAC8B,UAA3BlD,EAAQE,MAAMwD,UAChBH,EAAUE,GAAKH,EAAkBzE,IAAMwE,EACvCE,EAAUC,GAAKtI,KAAKC,IAAI8B,EAASqG,EAAkBvE,OAAQwE,EAAUE,GAAK,KAE1EF,EAAUE,GAAKH,EAAkBzE,IACjC0E,EAAUC,GAAKtI,KAAKC,IAAI8B,EAASqG,EAAkBvE,OAASsE,EAAaE,EAAUE,GAAK,IAG3D,UAA3BzD,EAAQmD,MAAMO,UAChBH,EAAUnC,GAAKkC,EAAkBtE,KAAOoE,EACxCG,EAAUlC,GAAKnG,KAAKC,IAAI6B,EAAQsG,EAAkBxE,MAAOyE,EAAUnC,GAAK,KAExEmC,EAAUnC,GAAKkC,EAAkBtE,KACjCuE,EAAUlC,GAAKnG,KAAKC,IAAI6B,EAAQsG,EAAkBxE,MAAQsE,EAAaG,EAAUnC,GAAK,MAGxFmC,EAAUnC,GAAKkC,EAAkBtE,KACjCuE,EAAUlC,GAAKnG,KAAKC,IAAI6B,EAAQsG,EAAkBxE,MAAOyE,EAAUnC,GAAK,GACxEmC,EAAUE,GAAKH,EAAkBzE,IACjC0E,EAAUC,GAAKtI,KAAKC,IAAI8B,EAASqG,EAAkBvE,OAAQwE,EAAUE,GAAK,IAGrEF,GAgBTrL,EAASyL,WAAa,SAASC,EAAgBtI,EAAOuI,EAAM1D,EAAQ/F,EAAQ0J,EAAOC,EAASC,GAC1F,GAAIC,KACJA,GAAeJ,EAAKK,MAAMC,IAAM,KAAOP,EAAeO,IACtDF,EAAeJ,EAAKK,MAAMC,IAAM,KAAOP,EAAeO,IACtDF,EAAeJ,EAAKO,aAAaD,IAAM,KAAOhE,EAC9C8D,EAAeJ,EAAKO,aAAaD,IAAM,KAAOhE,EAAS/F,CAEvD,IAAIiK,GAAcP,EAAMQ,KAAK,OAAQL,EAAgBF,EAAQQ,KAAK,KAGlEP,GAAaQ,KAAK,OAChBtM,EAASS,QACP8L,KAAM,OACNZ,KAAMA,EACNvI,MAAOA,EACPwI,MAAOA,EACPY,QAASL,GACRJ,KAmBP/L,EAASyM,YAAc,SAASf,EAAgBtI,EAAO2C,EAAQ4F,EAAMe,EAAYC,EAAaf,EAAOC,EAASe,EAAkBd,GAC9H,GAAIe,GACAd,IAOJ,IALAA,EAAeJ,EAAKK,MAAMC,KAAOP,EAAeO,IAAMU,EAAYhB,EAAKK,MAAMC,KAC7EF,EAAeJ,EAAKO,aAAaD,KAAOU,EAAYhB,EAAKO,aAAaD,KACtEF,EAAeJ,EAAKK,MAAMc,KAAOpB,EAAeoB,IAChDf,EAAeJ,EAAKO,aAAaY,KAAOJ,EAAa,GAElDE,EAAkB,CAGnB,GAAIG,GAAU,gBAAkBlB,EAAQQ,KAAK,KAAO,YAClDV,EAAKK,MAAMc,IAAM,KAAO9J,KAAKU,MAAMqI,EAAeJ,EAAKK,MAAMc,MAAQ,OACrEnB,EAAKO,aAAaY,IAAM,KAAO9J,KAAKU,MAAMqI,EAAeJ,EAAKO,aAAaY,MAAQ,OACnF/G,EAAO3C,GAAS,SAElByJ,GAAejB,EAAMoB,cAAcD,EAAS/M,EAASS,QACnDkF,MAAO,sBACNoG,QAEHc,GAAejB,EAAMQ,KAAK,OAAQL,EAAgBF,EAAQQ,KAAK,MAAMY,KAAKlH,EAAO3C,GAGnF0I,GAAaQ,KAAK,OAAQtM,EAASS,QACjC8L,KAAM,QACNZ,KAAMA,EACNvI,MAAOA,EACPwI,MAAOA,EACPY,QAASK,EACTI,KAAMlH,EAAO3C,IACZ2I,KAgBL/L,EAASkN,WAAa,SAASvB,EAAMzH,EAAMmH,EAAW8B,EAAWC,EAAYR,EAAkB9E,EAASgE,GACtG,GAAIuB,GAAcvF,EAAQ,OAAS6D,EAAKK,MAAMC,IAAIqB,eAC9CC,EAAkBrJ,EAAKhB,IAAIyI,EAAK6B,aAAaC,KAAK9B,IAClD+B,EAAcxJ,EAAKhB,IAAImK,EAAYM,sBAEvCJ,GAAgBtM,QAAQ,SAASyK,EAAgBtI,GAC/C,GAAIuJ,IACF3D,EAAG,EACH4B,EAAG,IAID8C,EAAYtK,IAAiC,IAAvBsK,EAAYtK,MAMhB,MAAnBuI,EAAKK,MAAMC,KACZP,EAAeO,IAAMZ,EAAUnC,GAAKwC,EAAeO,IACnDU,EAAY3D,EAAIlB,EAAQE,MAAM2E,YAAY3D,EAIZ,UAA3BlB,EAAQE,MAAMwD,SACfmB,EAAY/B,EAAIS,EAAU5E,QAAQE,IAAMmB,EAAQE,MAAM2E,YAAY/B,GAAKgC,EAAmB,EAAI,IAE9FD,EAAY/B,EAAIS,EAAUC,GAAKxD,EAAQE,MAAM2E,YAAY/B,GAAKgC,EAAmB,EAAI,MAGvFlB,EAAeO,IAAMZ,EAAUC,GAAKI,EAAeO,IACnDU,EAAY/B,EAAI9C,EAAQmD,MAAM0B,YAAY/B,GAAKgC,EAAmBlB,EAAeoB,IAAM,GAIzD,UAA3BhF,EAAQmD,MAAMO,SACfmB,EAAY3D,EAAI4D,EAAmBvB,EAAU5E,QAAQK,KAAOgB,EAAQmD,MAAM0B,YAAY3D,EAAIqC,EAAUnC,GAAK,GAEzGyD,EAAY3D,EAAIqC,EAAUlC,GAAKrB,EAAQmD,MAAM0B,YAAY3D,EAAI,IAI9DqE,EAAYO,UACb5N,EAASyL,WAAWC,EAAgBtI,EAAOuI,EAAMA,EAAKkC,WAAYxC,EAAUM,EAAKO,aAAaY,OAAQK,GACpGrF,EAAQgG,WAAWC,KACnBjG,EAAQgG,WAAWnC,EAAKK,MAAMgC,MAC7BlC,GAGFuB,EAAYY,WACbjO,EAASyM,YAAYf,EAAgBtI,EAAOsK,EAAa/B,EAAM0B,EAAYpF,OAAQ0E,EAAaS,GAC9FtF,EAAQgG,WAAWI,MACnBpG,EAAQgG,WAAWnC,EAAKK,MAAMgC,KAC9BlG,EAAQgG,WAAWT,EAAY7B,WAC9BoB,EAAkBd,OAc3B9L,EAASmO,gBAAkB,SAASlI,EAAQ6B,EAASrD,GACnD,GAAGwB,EAAOmI,MAAQtG,EAAQ7B,QAAU6B,EAAQ7B,OAAOA,EAAOmI,MAAO,CAC/D,GAAIC,GAAgBvG,EAAQ7B,OAAOA,EAAOmI,KAC1C,OAAOC,GAAc/H,eAAe7B,GAAO4J,EAAc5J,GAAOqD,EAAQrD,GAExE,MAAOqD,GAAQrD,IAanBzE,EAASsO,gBAAkB,SAAUxG,EAASyG,EAAmBzC,GAM/D,QAAS0C,GAAqBC,GAC5B,GAAIC,GAAkBC,CAGtB,IAFAA,EAAiB3O,EAASS,UAAWmO,GAEjCL,EACF,IAAKrI,EAAI,EAAGA,EAAIqI,EAAkBrM,OAAQgE,IAAK,CAC7C,GAAI2I,GAAM3O,EAAO4O,WAAWP,EAAkBrI,GAAG,GAC7C2I,GAAIE,UACNJ,EAAiB3O,EAASS,OAAOkO,EAAgBJ,EAAkBrI,GAAG,KAKzE4F,IAAiB2C,GAClB3C,EAAaQ,KAAK,kBAChBoC,gBAAiBA,EACjBC,eAAgBA,IAKtB,QAASK,KACPC,EAAoBhO,QAAQ,SAAS4N,GACnCA,EAAIK,eAAeV,KA5BvB,GACEG,GAEAzI,EAHE0I,EAAc5O,EAASS,UAAWqH,GAEpCmH,IA8BF,KAAK/O,EAAO4O,WACV,KAAM,iEACD,IAAIP,EAET,IAAKrI,EAAI,EAAGA,EAAIqI,EAAkBrM,OAAQgE,IAAK,CAC7C,GAAI2I,GAAM3O,EAAO4O,WAAWP,EAAkBrI,GAAG,GACjD2I,GAAIM,YAAYX,GAChBS,EAAoB9E,KAAK0E,GAM7B,MAFAL,IAAqB,IAGnBQ,0BAA2BA,EAC3BI,kBAAmB,WACjB,MAAOpP,GAASS,UAAWkO,OAKjCzO,OAAQC,SAAUH,GAOnB,SAASE,EAAQC,EAAUH,GAC1B,YAEAA,GAASqP,iBAQTrP,EAASqP,cAAcC,KAAO,WAC5B,MAAO,UAAcC,EAAiBC,GAKpC,IAAI,GAJAC,GAAO,GAAIzP,GAASwF,IAAIkK,KAExBC,GAAO,EAEHzJ,EAAI,EAAGA,EAAIqJ,EAAgBrN,OAAQgE,GAAK,EAAG,CACjD,GAAIhC,GAAOsL,GAAWtJ,EAAI,GAAK,EAGb/B,UAAfD,EAAKvC,MACNgO,GAAO,EAGJA,GAEDF,EAAKG,KAAKL,EAAgBrJ,EAAI,GAAIqJ,EAAgBrJ,IAAI,EAAOhC,GAC7DyL,GAAO,GAEPF,EAAKI,KAAKN,EAAgBrJ,EAAI,GAAIqJ,EAAgBrJ,IAAI,EAAOhC,GAKnE,MAAOuL,KA0BXzP,EAASqP,cAAcS,OAAS,SAAShI,GACvC,GAAIiI,IACF9G,QAAS,EAEXnB,GAAU9H,EAASS,UAAWsP,EAAgBjI,EAE9C,IAAIkI,GAAI,EAAIhN,KAAKC,IAAI,EAAG6E,EAAQmB,QAEhC,OAAO,UAAgBsG,EAAiBC,GAItC,IAAI,GAHAC,GAAO,GAAIzP,GAASwF,IAAIkK,KACxBC,GAAO,EAEHzJ,EAAI,EAAGA,EAAIqJ,EAAgBrN,OAAQgE,GAAK,EAAG,CACjD,GAAI+J,GAAQV,EAAgBrJ,EAAI,GAC5BgK,EAAQX,EAAgBrJ,EAAI,GAC5BiK,EAAQZ,EAAgBrJ,GACxBkK,EAAQb,EAAgBrJ,EAAI,GAC5BhE,GAAUiO,EAAQF,GAASD,EAC3BK,EAAWb,EAAWtJ,EAAI,EAAK,GAC/BoK,EAAWd,EAAUtJ,EAAI,EAEP/B,UAAnBkM,EAAS1O,MACVgO,GAAO,GAGJA,GACDF,EAAKG,KAAKK,EAAOC,GAAO,EAAOG,GAGXlM,SAAnBmM,EAAS3O,QACV8N,EAAKc,MACHN,EAAQ/N,EACRgO,EACAC,EAAQjO,EACRkO,EACAD,EACAC,GACA,EACAE,GAGFX,GAAO,IAKb,MAAOF,KAyBXzP,EAASqP,cAAcmB,SAAW,SAAS1I,GAazC,QAAS2I,GAAkBlB,EAAiBC,GAI1C,IAAI,GAHAkB,MACAf,GAAO,EAEHzJ,EAAI,EAAGA,EAAIqJ,EAAgBrN,OAAQgE,GAAK,EAEhB/B,SAA3BqL,EAAUtJ,EAAI,GAAGvE,MAClBgO,GAAO,GAGJA,IACDe,EAASvG,MACPoF,mBACAC,eAGFG,GAAO,GAITe,EAASA,EAASxO,OAAS,GAAGqN,gBAAgBpF,KAAKoF,EAAgBrJ,GAAIqJ,EAAgBrJ,EAAI,IAC3FwK,EAASA,EAASxO,OAAS,GAAGsN,UAAUrF,KAAKqF,EAAUtJ,EAAI,IAI/D,OAAOwK,GArCT,GAAIX,IACFY,QAAS,EAGX7I,GAAU9H,EAASS,UAAWsP,EAAgBjI,EAE9C,IAAI8I,GAAI5N,KAAK0G,IAAI,EAAG1G,KAAKC,IAAI,EAAG6E,EAAQ6I,UACtCE,EAAI,EAAID,CAiCV,OAAO,SAASJ,GAASjB,EAAiBC,GAGxC,GAAIkB,GAAWD,EAAkBlB,EAAiBC,EAIlD,IAAGkB,EAASxO,OAAS,EAAG,CACtB,GAAI4O,KAMJ,OAJAJ,GAASzP,QAAQ,SAAS8P,GACxBD,EAAM3G,KAAKqG,EAASO,EAAQxB,gBAAiBwB,EAAQvB,cAGhDxP,EAASwF,IAAIkK,KAAKrD,KAAKyE,GAQ9B,GAJAvB,EAAkBmB,EAAS,GAAGnB,gBAC9BC,EAAYkB,EAAS,GAAGlB,UAGrBD,EAAgBrN,QAAU,EAC3B,MAAOlC,GAASqP,cAAcC,OAAOC,EAAiBC,EAMxD,KAAK,GAFHwB,GADEvB,GAAO,GAAIzP,GAASwF,IAAIkK,MAAOE,KAAKL,EAAgB,GAAIA,EAAgB,IAAI,EAAOC,EAAU,IAGxFtJ,EAAI,EAAG+K,EAAO1B,EAAgBrN,OAAQ+O,EAAO,GAAKD,EAAI9K,EAAGA,GAAK,EAAG,CACxE,GAAI2C,KACDG,GAAIuG,EAAgBrJ,EAAI,GAAI0E,GAAI2E,EAAgBrJ,EAAI,KACpD8C,GAAIuG,EAAgBrJ,GAAI0E,GAAI2E,EAAgBrJ,EAAI,KAChD8C,GAAIuG,EAAgBrJ,EAAI,GAAI0E,GAAI2E,EAAgBrJ,EAAI,KACpD8C,GAAIuG,EAAgBrJ,EAAI,GAAI0E,GAAI2E,EAAgBrJ,EAAI,IAEnD8K,GACG9K,EAEM+K,EAAO,IAAM/K,EACtB2C,EAAE,IAAMG,GAAIuG,EAAgB,GAAI3E,GAAI2E,EAAgB,IAC3C0B,EAAO,IAAM/K,IACtB2C,EAAE,IAAMG,GAAIuG,EAAgB,GAAI3E,GAAI2E,EAAgB,IACpD1G,EAAE,IAAMG,GAAIuG,EAAgB,GAAI3E,GAAI2E,EAAgB,KALpD1G,EAAE,IAAMG,GAAIuG,EAAgB0B,EAAO,GAAIrG,GAAI2E,EAAgB0B,EAAO,IAQhEA,EAAO,IAAM/K,EACf2C,EAAE,GAAKA,EAAE,GACC3C,IACV2C,EAAE,IAAMG,GAAIuG,EAAgBrJ,GAAI0E,GAAI2E,EAAgBrJ,EAAI,KAI5DuJ,EAAKc,MACFK,IAAM/H,EAAE,GAAGG,EAAI,EAAIH,EAAE,GAAGG,EAAIH,EAAE,GAAGG,GAAK,EAAM6H,EAAIhI,EAAE,GAAGG,EACrD4H,IAAM/H,EAAE,GAAG+B,EAAI,EAAI/B,EAAE,GAAG+B,EAAI/B,EAAE,GAAG+B,GAAK,EAAMiG,EAAIhI,EAAE,GAAG+B,EACrDgG,GAAK/H,EAAE,GAAGG,EAAI,EAAIH,EAAE,GAAGG,EAAIH,EAAE,GAAGG,GAAK,EAAM6H,EAAIhI,EAAE,GAAGG,EACpD4H,GAAK/H,EAAE,GAAG+B,EAAI,EAAI/B,EAAE,GAAG+B,EAAI/B,EAAE,GAAG+B,GAAK,EAAMiG,EAAIhI,EAAE,GAAG+B,EACrD/B,EAAE,GAAGG,EACLH,EAAE,GAAG+B,GACL,EACA4E,GAAWtJ,EAAI,GAAK,IAIxB,MAAOuJ,KAwBbzP,EAASqP,cAAcxF,KAAO,SAAS/B,GACrC,GAAIiI,IACFmB,UAAU,EAKZ,OAFApJ,GAAU9H,EAASS,UAAWsP,EAAgBjI,GAEvC,SAAcyH,EAAiBC,GAIpC,IAAK,GAHDC,GAAO,GAAIzP,GAASwF,IAAIkK,KACxBC,GAAO,EAEFzJ,EAAI,EAAGA,EAAIqJ,EAAgBrN,OAAQgE,GAAK,EAAG,CAClD,GAAI+J,GAAQV,EAAgBrJ,EAAI,GAC5BgK,EAAQX,EAAgBrJ,EAAI,GAC5BiK,EAAQZ,EAAgBrJ,GACxBkK,EAAQb,EAAgBrJ,EAAI,GAC5BmK,EAAWb,EAAWtJ,EAAI,EAAK,GAC/BoK,EAAWd,EAAUtJ,EAAI,EAGP/B,UAAnBkM,EAAS1O,MACVgO,GAAO,GAGJA,GACDF,EAAKG,KAAKK,EAAOC,GAAO,EAAOG,GAIXlM,SAAnBmM,EAAS3O,QACPmG,EAAQoJ,SAETzB,EAAKI,KAAKM,EAAOD,GAAO,EAAOG,GAG/BZ,EAAKI,KAAKI,EAAOG,GAAO,EAAOE,GAGjCb,EAAKI,KAAKM,EAAOC,GAAO,EAAOE,GAE/BX,GAAO,IAKb,MAAOF,MAIXvP,OAAQC,SAAUH,GAOnB,SAAUE,EAAQC,EAAUH,GAC3B,YAEAA,GAASmR,aAAe,WAUtB,QAASC,GAAgBC,EAAOC,GAC9BC,EAASF,GAASE,EAASF,OAC3BE,EAASF,GAAOlH,KAAKmH,GAUvB,QAASE,GAAmBH,EAAOC,GAE9BC,EAASF,KAEPC,GACDC,EAASF,GAAOI,OAAOF,EAASF,GAAOK,QAAQJ,GAAU,GAC3B,IAA3BC,EAASF,GAAOnP,cACVqP,GAASF,UAIXE,GAASF,IAYtB,QAAS/E,GAAK+E,EAAOnN,GAEhBqN,EAASF,IACVE,EAASF,GAAOpQ,QAAQ,SAASqQ,GAC/BA,EAAQpN,KAKTqN,EAAS,MACVA,EAAS,KAAKtQ,QAAQ,SAAS0Q,GAC7BA,EAAYN,EAAOnN,KAvDzB,GAAIqN,KA4DJ,QACEH,gBAAiBA,EACjBI,mBAAoBA,EACpBlF,KAAMA,KAIVpM,OAAQC,SAAUH,GAOnB,SAASE,EAAQC,EAAUH,GAC1B,YAEA,SAAS4R,GAAYC,GACnB,GAAIhP,KACJ,IAAIgP,EAAK3P,OACP,IAAK,GAAIgE,GAAI,EAAGA,EAAI2L,EAAK3P,OAAQgE,IAC/BrD,EAAIsH,KAAK0H,EAAK3L,GAGlB,OAAOrD,GA4CT,QAASpC,GAAOqR,EAAYC,GAC1B,GAAIC,GAAaD,GAAsBhS,KAAKc,WAAab,EAASiS,MAC9DC,EAAQ5N,OAAO6N,OAAOH,EAE1BhS,GAASiS,MAAMG,iBAAiBF,EAAOJ,EAEvC,IAAIO,GAAS,WACX,GACEC,GADEC,EAAKL,EAAMM,aAAe,YAU9B,OALAF,GAAWvS,OAASC,EAAWsE,OAAO6N,OAAOD,GAASnS,KACtDwS,EAAGpQ,MAAMmQ,EAAU1R,MAAMC,UAAUC,MAAMC,KAAKC,UAAW,IAIlDsR,EAOT,OAJAD,GAAOxR,UAAYqR,EACnBG,EAAAA,SAAeL,EACfK,EAAO5R,OAASV,KAAKU,OAEd4R,EAIT,QAASD,KACP,GAAI/O,GAAOuO,EAAY5Q,WACnBN,EAAS2C,EAAK,EAYlB,OAVAA,GAAKoO,OAAO,EAAGpO,EAAKnB,OAAS,GAAGjB,QAAQ,SAAUC,GAChDoD,OAAOmO,oBAAoBvR,GAAQD,QAAQ,SAAUyR,SAE5ChS,GAAOgS,GAEdpO,OAAOqO,eAAejS,EAAQgS,EAC5BpO,OAAOsO,yBAAyB1R,EAAQwR,QAIvChS,EAGTV,EAASiS,OACPxR,OAAQA,EACR2R,iBAAkBA,IAGpBlS,OAAQC,SAAUH,GAOnB,SAASE,EAAQC,EAAUH,GAC1B,YAgBA,SAAS6S,GAAO3O,EAAM4D,EAASgL,GA2B7B,MA1BG5O,KACDnE,KAAKmE,KAAOA,EAEZnE,KAAK+L,aAAaQ,KAAK,QACrBC,KAAM,SACNrI,KAAMnE,KAAKmE,QAIZ4D,IACD/H,KAAK+H,QAAU9H,EAASS,UAAWqS,EAAW/S,KAAK+H,QAAU/H,KAAKgQ,eAAgBjI,GAI9E/H,KAAKgT,sBACPhT,KAAKuO,gBAAgBU,4BACrBjP,KAAKuO,gBAAkBtO,EAASsO,gBAAgBvO,KAAK+H,QAAS/H,KAAKwO,kBAAmBxO,KAAK+L,gBAK3F/L,KAAKgT,qBACPhT,KAAKiT,YAAYjT,KAAKuO,gBAAgBc,qBAIjCrP,KAQT,QAASkT,KAUP,MAPIlT,MAAKgT,oBAIP7S,EAAOgT,aAAanT,KAAKgT,sBAHzB7S,EAAOiT,oBAAoB,SAAUpT,KAAKqT,gBAC1CrT,KAAKuO,gBAAgBU,6BAKhBjP,KAUT,QAASsT,GAAGhC,EAAOC,GAEjB,MADAvR,MAAK+L,aAAasF,gBAAgBC,EAAOC,GAClCvR,KAUT,QAASuT,GAAIjC,EAAOC,GAElB,MADAvR,MAAK+L,aAAa0F,mBAAmBH,EAAOC,GACrCvR,KAGT,QAASwT,KAEPrT,EAAOsT,iBAAiB,SAAUzT,KAAKqT,gBAIvCrT,KAAKuO,gBAAkBtO,EAASsO,gBAAgBvO,KAAK+H,QAAS/H,KAAKwO,kBAAmBxO,KAAK+L,cAE3F/L,KAAK+L,aAAasF,gBAAgB,iBAAkB,WAClDrR,KAAK8S,UACLpF,KAAK1N,OAIJA,KAAK+H,QAAQ2L,SACd1T,KAAK+H,QAAQ2L,QAAQxS,QAAQ,SAASyS,GACjCA,YAAkB9S,OACnB8S,EAAO,GAAG3T,KAAM2T,EAAO,IAEvBA,EAAO3T,OAET0N,KAAK1N,OAITA,KAAK+L,aAAaQ,KAAK,QACrBC,KAAM,UACNrI,KAAMnE,KAAKmE,OAIbnE,KAAKiT,YAAYjT,KAAKuO,gBAAgBc,qBAItCrP,KAAKgT,oBAAsB5O,OAa7B,QAASwP,GAAK5R,EAAOmC,EAAM6L,EAAgBjI,EAASyG,GAClDxO,KAAK8E,UAAY7E,EAAS8B,cAAcC,GACxChC,KAAKmE,KAAOA,EACZnE,KAAKgQ,eAAiBA,EACtBhQ,KAAK+H,QAAUA,EACf/H,KAAKwO,kBAAoBA,EACzBxO,KAAK+L,aAAe9L,EAASmR,eAC7BpR,KAAK6T,sBAAwB5T,EAASwF,IAAIqO,YAAY,iBACtD9T,KAAK+T,mBAAqB9T,EAASwF,IAAIqO,YAAY,4BACnD9T,KAAKqT,eAAiB,WACpBrT,KAAK8S,UACLpF,KAAK1N,MAEJA,KAAK8E,YAEH9E,KAAK8E,UAAUkP,cAChBhU,KAAK8E,UAAUkP,aAAad,SAG9BlT,KAAK8E,UAAUkP,aAAehU,MAKhCA,KAAKgT,oBAAsBiB,WAAWT,EAAW9F,KAAK1N,MAAO,GAI/DC,EAAS2T,KAAO3T,EAASiS,MAAMxR,QAC7B+R,YAAamB,EACbrF,gBAAiBnK,OACjBU,UAAWV,OACXc,IAAKd,OACL2H,aAAc3H,OACd6O,YAAa,WACX,KAAM,IAAIiB,OAAM,2CAElBpB,OAAQA,EACRI,OAAQA,EACRI,GAAIA,EACJC,IAAKA,EACLrT,QAASD,EAASC,QAClB2T,uBAAuB,KAGzB1T,OAAQC,SAAUH,GAOnB,SAASE,EAAQC,EAAUH,GAC1B,YAuBA,SAASwF,GAAI4I,EAAM8F,EAAYlP,EAAWmP,EAAQC,GAE7ChG,YAAgBiG,SACjBtU,KAAK8F,MAAQuI,GAEbrO,KAAK8F,MAAQ1F,EAASmU,gBAAgBC,EAAOnG,GAGjC,QAATA,GACDrO,KAAK8F,MAAM2O,eAAenP,EAAOrF,EAASqF,MAAMC,cAAetF,EAASqF,MAAMoP,KAG7EP,GACDnU,KAAK0F,KAAKyO,GAGTlP,GACDjF,KAAK2F,SAASV,GAGbmP,IACGC,GAAeD,EAAOtO,MAAM6O,WAC9BP,EAAOtO,MAAM8O,aAAa5U,KAAK8F,MAAOsO,EAAOtO,MAAM6O,YAEnDP,EAAOtO,MAAMD,YAAY7F,KAAK8F,SActC,QAASJ,GAAKyO,EAAYU,GACxB,MAAyB,gBAAfV,GACLU,EACM7U,KAAK8F,MAAMgP,eAAeD,EAAIV,GAE9BnU,KAAK8F,MAAMT,aAAa8O,IAInC5P,OAAOC,KAAK2P,GAAYjT,QAAQ,SAASwD,GAEhBN,SAApB+P,EAAWzP,KAIXmQ,EACD7U,KAAK8F,MAAM2O,eAAeI,GAAK5U,EAASqF,MAAMyP,OAAQ,IAAKrQ,GAAK4H,KAAK,IAAK6H,EAAWzP,IAErF1E,KAAK8F,MAAMkP,aAAatQ,EAAKyP,EAAWzP,MAE1CgJ,KAAK1N,OAEAA,MAaT,QAASqM,GAAKgC,EAAM8F,EAAYlP,EAAWoP,GACzC,MAAO,IAAIpU,GAASwF,IAAI4I,EAAM8F,EAAYlP,EAAWjF,KAAMqU,GAS7D,QAASD,KACP,MAAOpU,MAAK8F,MAAMmP,qBAAsBC,YAAa,GAAIjV,GAASwF,IAAIzF,KAAK8F,MAAMmP,YAAc,KASjG,QAASvV,KAEP,IADA,GAAIyV,GAAOnV,KAAK8F,MACQ,QAAlBqP,EAAKC,UACTD,EAAOA,EAAKF,UAEd,OAAO,IAAIhV,GAASwF,IAAI0P,GAU1B,QAASpT,GAAcsT,GACrB,GAAIC,GAAYtV,KAAK8F,MAAM/D,cAAcsT,EACzC,OAAOC,GAAY,GAAIrV,GAASwF,IAAI6P,GAAa,KAUnD,QAASnQ,GAAiBkQ,GACxB,GAAIE,GAAavV,KAAK8F,MAAMX,iBAAiBkQ,EAC7C,OAAOE,GAAWpT,OAAS,GAAIlC,GAASwF,IAAI+P,KAAKD,GAAc,KAajE,QAAStI,GAAcD,EAASmH,EAAYlP,EAAWoP,GAGrD,GAAsB,gBAAZrH,GAAsB,CAC9B,GAAIlI,GAAY1E,EAASqV,cAAc,MACvC3Q,GAAU4Q,UAAY1I,EACtBA,EAAUlI,EAAU6P,WAItB3H,EAAQgI,aAAa,QAASW,EAI9B,IAAIC,GAAQ5V,KAAKqM,KAAK,gBAAiB8H,EAAYlP,EAAWoP,EAK9D,OAFAuB,GAAM9P,MAAMD,YAAYmH,GAEjB4I,EAUT,QAAS1I,GAAK2D,GAEZ,MADA7Q,MAAK8F,MAAMD,YAAYzF,EAASyV,eAAehF,IACxC7Q,KAST,QAAS8V,KACP,KAAO9V,KAAK8F,MAAM6O,YAChB3U,KAAK8F,MAAMN,YAAYxF,KAAK8F,MAAM6O,WAGpC,OAAO3U,MAST,QAAS+V,KAEP,MADA/V,MAAK8F,MAAMmP,WAAWzP,YAAYxF,KAAK8F,OAChC9F,KAAKoU,SAUd,QAAS3S,GAAQuU,GAEf,MADAhW,MAAK8F,MAAMmP,WAAWgB,aAAaD,EAAWlQ,MAAO9F,KAAK8F,OACnDkQ,EAWT,QAASE,GAAOzJ,EAAS4H,GAOvB,MANGA,IAAerU,KAAK8F,MAAM6O,WAC3B3U,KAAK8F,MAAM8O,aAAanI,EAAQ3G,MAAO9F,KAAK8F,MAAM6O,YAElD3U,KAAK8F,MAAMD,YAAY4G,EAAQ3G,OAG1B9F,KAST,QAAS8L,KACP,MAAO9L,MAAK8F,MAAMT,aAAa,SAAWrF,KAAK8F,MAAMT,aAAa,SAAS8Q,OAAOC,MAAM,UAU1F,QAASzQ,GAAS0Q,GAShB,MARArW,MAAK8F,MAAMkP,aAAa,QACtBhV,KAAK8L,QAAQ9L,KAAK8F,OACfwQ,OAAOD,EAAMF,OAAOC,MAAM,QAC1BhR,OAAO,SAASiH,EAAMH,EAAKqK,GAC1B,MAAOA,GAAK5E,QAAQtF,KAAUH,IAC7BI,KAAK,MAGLtM,KAUT,QAASwW,GAAYH,GACnB,GAAII,GAAiBJ,EAAMF,OAAOC,MAAM,MAMxC,OAJApW,MAAK8F,MAAMkP,aAAa,QAAShV,KAAK8L,QAAQ9L,KAAK8F,OAAOV,OAAO,SAASiJ,GACxE,MAAwC,KAAjCoI,EAAe9E,QAAQtD,KAC7B/B,KAAK,MAEDtM,KAST,QAAS0W,KAGP,MAFA1W,MAAK8F,MAAMkP,aAAa,QAAS,IAE1BhV,KAaT,QAAS2W,GAAgBxB,EAAM/T,GAC7B,IACE,MAAO+T,GAAKyB,UAAUxV,GACtB,MAAMgC,IAER,MAAO,GAUT,QAAS4B,KACP,MAAOhF,MAAK8F,MAAM+Q,cAAgB5T,KAAKU,MAAMgT,EAAgB3W,KAAK8F,MAAO,YAAc9F,KAAK8F,MAAMmP,WAAW4B,aAU/G,QAAS9R,KACP,MAAO/E,MAAK8F,MAAMgR,aAAe7T,KAAKU,MAAMgT,EAAgB3W,KAAK8F,MAAO,WAAa9F,KAAK8F,MAAMmP,WAAW6B,YA4C7G,QAASC,GAAQC,EAAYC,EAAQlL,GA4GnC,MA3Gc3H,UAAX6S,IACDA,GAAS,GAGX1S,OAAOC,KAAKwS,GAAY9V,QAAQ,SAAoCgW,GAElE,QAASC,GAAcC,EAAqBH,GAC1C,GACEF,GACAM,EACAC,EAHEC,IAODH,GAAoBE,SAErBA,EAASF,EAAoBE,iBAAkBzW,OAC7CuW,EAAoBE,OACpBrX,EAASwF,IAAI+R,OAAOJ,EAAoBE,cACnCF,GAAoBE,QAI7BF,EAAoBK,MAAQxX,EAAS4B,WAAWuV,EAAoBK,MAAO,MAC3EL,EAAoBM,IAAMzX,EAAS4B,WAAWuV,EAAoBM,IAAK,MAEpEJ,IACDF,EAAoBO,SAAW,SAC/BP,EAAoBQ,WAAaN,EAAOhL,KAAK,KAC7C8K,EAAoBS,SAAW,OAI9BZ,IACDG,EAAoBU,KAAO,SAE3BP,EAAoBL,GAAaE,EAAoBW,KACrD/X,KAAK0F,KAAK6R,GAIVF,EAAUpX,EAAS0B,UAAUyV,EAAoBK,OAAS,GAC1DL,EAAoBK,MAAQ,cAG9BV,EAAU/W,KAAKqM,KAAK,UAAWpM,EAASS,QACtCsX,cAAed,GACdE,IAEAH,GAEDhD,WAAW,WAIT,IACE8C,EAAQjR,MAAMmS,eACd,MAAMC,GAENX,EAAoBL,GAAaE,EAAoBe,GACrDnY,KAAK0F,KAAK6R,GAEVR,EAAQhB,WAEVrI,KAAK1N,MAAOqX,GAGbtL,GACDgL,EAAQjR,MAAM2N,iBAAiB,aAAc,WAC3C1H,EAAaQ,KAAK,kBAChBE,QAASzM,KACT+W,QAASA,EAAQjR,MACjBsS,OAAQhB,KAEV1J,KAAK1N,OAGT+W,EAAQjR,MAAM2N,iBAAiB,WAAY,WACtC1H,GACDA,EAAaQ,KAAK,gBAChBE,QAASzM,KACT+W,QAASA,EAAQjR,MACjBsS,OAAQhB,IAITH,IAEDM,EAAoBL,GAAaE,EAAoBe,GACrDnY,KAAK0F,KAAK6R,GAEVR,EAAQhB,WAEVrI,KAAK1N,OAINgX,EAAWE,YAAsBrW,OAClCmW,EAAWE,GAAWhW,QAAQ,SAASkW,GACrCD,EAAczJ,KAAK1N,MAAMoX,GAAqB,IAC9C1J,KAAK1N,OAEPmX,EAAczJ,KAAK1N,MAAMgX,EAAWE,GAAYD,IAGlDvJ,KAAK1N,OAEAA,KA+ET,QAASqY,GAAQC,GACf,GAAIxG,GAAO9R,IAEXA,MAAKuY,cACL,KAAI,GAAIpS,GAAI,EAAGA,EAAImS,EAASnW,OAAQgE,IAClCnG,KAAKuY,YAAYnO,KAAK,GAAInK,GAASwF,IAAI6S,EAASnS,IAIlD5B,QAAOC,KAAKvE,EAASwF,IAAI3E,WAAWsE,OAAO,SAASoT,GAClD,MAQ4C,MARpC,cACJ,SACA,gBACA,mBACA,UACA,SACA,UACA,SACA,SAAS7G,QAAQ6G,KACpBtX,QAAQ,SAASsX,GAClB1G,EAAK0G,GAAqB,WACxB,GAAIlV,GAAOzC,MAAMC,UAAUC,MAAMC,KAAKC,UAAW,EAIjD,OAHA6Q,GAAKyG,YAAYrX,QAAQ,SAASuL,GAChCxM,EAASwF,IAAI3E,UAAU0X,GAAmBpW,MAAMqK,EAASnJ,KAEpDwO,KAplBb,GAAI0C,GAAQ,6BACVlP,EAAQ,gCACRqQ,EAAU,8BAEZ1V,GAASqF,OACPC,cAAe,WACfwP,OAAQ,KACRL,IAAK,6CAwePzU,EAASwF,IAAMxF,EAASiS,MAAMxR,QAC5B+R,YAAahN,EACbC,KAAMA,EACN2G,KAAMA,EACN+H,OAAQA,EACR1U,KAAMA,EACNqC,cAAeA,EACfoD,iBAAkBA,EAClB8H,cAAeA,EACfC,KAAMA,EACN4I,MAAOA,EACPC,OAAQA,EACRtU,QAASA,EACTyU,OAAQA,EACRpK,QAASA,EACTnG,SAAUA,EACV6Q,YAAaA,EACbE,iBAAkBA,EAClB1R,OAAQA,EACRD,MAAOA,EACPgS,QAASA,IAUX9W,EAASwF,IAAIqO,YAAc,SAAS2E,GAClC,MAAOrY,GAASsY,eAAeC,WAAW,sCAAwCF,EAAS,OAQ7F,IAAIG,IACFC,YAAa,IAAM,EAAG,KAAO,MAC7BC,aAAc,IAAM,KAAO,KAAO,GAClCC,eAAgB,KAAO,IAAM,IAAM,KACnCC,YAAa,IAAM,KAAO,IAAM,KAChCC,aAAc,IAAM,IAAM,IAAM,KAChCC,eAAgB,KAAO,IAAM,KAAO,MACpCC,aAAc,IAAM,KAAO,KAAO,KAClCC,cAAe,KAAO,IAAM,KAAO,GACnCC,gBAAiB,KAAO,KAAO,KAAO,GACtCC,aAAc,KAAO,IAAM,KAAO,KAClCC,cAAe,KAAO,IAAM,IAAM,GAClCC,gBAAiB,IAAM,EAAG,KAAO,GACjCC,aAAc,KAAO,IAAM,KAAO,KAClCC,cAAe,IAAM,EAAG,IAAM,GAC9BC,gBAAiB,IAAM,EAAG,IAAM,GAChCC,YAAa,IAAM,IAAM,KAAO,MAChCC,aAAc,IAAM,EAAG,IAAM,GAC7BC,eAAgB,EAAG,EAAG,EAAG,GACzBC,YAAa,GAAK,IAAM,IAAM,MAC9BC,aAAc,KAAO,IAAM,KAAO,GAClCC,eAAgB,KAAO,KAAO,IAAM,KACpCC,YAAa,IAAM,IAAM,KAAO,MAChCC,aAAc,KAAO,KAAO,IAAM,OAClCC,eAAgB,KAAO,IAAM,KAAO,MAGtCna,GAASwF,IAAI+R,OAASoB,EAwCtB3Y,EAASwF,IAAI+P,KAAOvV,EAASiS,MAAMxR,QACjC+R,YAAa4F,KAEflY,OAAQC,SAAUH,GAOnB,SAASE,EAAQC,EAAUH,GAC1B,YA0BA,SAASwM,GAAQ4N,EAASjC,EAAQkC,EAAcpO,EAAKqO,EAAUpW,GAC7D,GAAIqW,GAAcva,EAASS,QACzB2Z,QAASE,EAAWF,EAAQI,cAAgBJ,EAAQ9M,eACnD6K,EAAQjU,GAASA,KAAMA,MAE1BmW,GAAa5I,OAAOxF,EAAK,EAAGsO,GAG9B,QAASE,GAAaJ,EAAcvX,GAClCuX,EAAapZ,QAAQ,SAASsZ,EAAaG,GACzCC,EAAoBJ,EAAYH,QAAQI,eAAevZ,QAAQ,SAAS2Z,EAAWC,GACjF/X,EAAGyX,EAAaK,EAAWF,EAAkBG,EAAYR,OAa/D,QAASS,GAAQC,EAAOjT,GACtB/H,KAAKsa,gBACLta,KAAKkM,IAAM,EACXlM,KAAKgb,MAAQA,EACbhb,KAAK+H,QAAU9H,EAASS,UAAWsP,EAAgBjI,GAUrD,QAAS0D,GAASS,GAChB,MAAW9H,UAAR8H,GACDlM,KAAKkM,IAAMjJ,KAAKC,IAAI,EAAGD,KAAK0G,IAAI3J,KAAKsa,aAAanY,OAAQ+J,IACnDlM,MAEAA,KAAKkM,IAWhB,QAAS6J,GAAOkF,GAEd,MADAjb,MAAKsa,aAAa5I,OAAO1R,KAAKkM,IAAK+O,GAC5Bjb,KAaT,QAAS6P,GAAK5G,EAAG4B,EAAG0P,EAAUpW,GAK5B,MAJAsI,GAAQ,KACNxD,GAAIA,EACJ4B,GAAIA,GACH7K,KAAKsa,aAActa,KAAKkM,MAAOqO,EAAUpW,GACrCnE,KAaT,QAAS8P,GAAK7G,EAAG4B,EAAG0P,EAAUpW,GAK5B,MAJAsI,GAAQ,KACNxD,GAAIA,EACJ4B,GAAIA,GACH7K,KAAKsa,aAActa,KAAKkM,MAAOqO,EAAUpW,GACrCnE,KAiBT,QAASwQ,GAAMrH,EAAIoC,EAAInC,EAAIoC,EAAIvC,EAAG4B,EAAG0P,EAAUpW,GAS7C,MARAsI,GAAQ,KACNtD,IAAKA,EACLoC,IAAKA,EACLnC,IAAKA,EACLoC,IAAKA,EACLvC,GAAIA,EACJ4B,GAAIA,GACH7K,KAAKsa,aAActa,KAAKkM,MAAOqO,EAAUpW,GACrCnE,KAkBT,QAASkb,GAAIC,EAAIC,EAAIC,EAAKC,EAAKC,EAAItS,EAAG4B,EAAG0P,EAAUpW,GAUjD,MATAsI,GAAQ,KACN0O,IAAKA,EACLC,IAAKA,EACLC,KAAMA,EACNC,KAAMA,EACNC,IAAKA,EACLtS,GAAIA,EACJ4B,GAAIA,GACH7K,KAAKsa,aAActa,KAAKkM,MAAOqO,EAAUpW,GACrCnE,KAUT,QAAS4E,GAAM8K,GAEb,GAAI8L,GAAS9L,EAAKjO,QAAQ,qBAAsB,SAC7CA,QAAQ,qBAAsB,SAC9B2U,MAAM,UACN3R,OAAO,SAASzB,EAAQyJ,GAMvB,MALGA,GAAQgP,MAAM,aACfzY,EAAOoH,SAGTpH,EAAOA,EAAOb,OAAS,GAAGiI,KAAKqC,GACxBzJ,MAIuC,OAA/CwY,EAAOA,EAAOrZ,OAAS,GAAG,GAAGoL,eAC9BiO,EAAOE,KAKT,IAAIC,GAAWH,EAAOrY,IAAI,SAASyY,GAC/B,GAAIvB,GAAUuB,EAAMC,QAClBC,EAAclB,EAAoBP,EAAQI,cAE5C,OAAOxa,GAASS,QACd2Z,QAASA,GACRyB,EAAYrX,OAAO,SAASzB,EAAQ6X,EAAWxX,GAEhD,MADAL,GAAO6X,IAAce,EAAMvY,GACpBL,UAKT+Y,GAAc/b,KAAKkM,IAAK,EAM5B,OALArL,OAAMC,UAAUsJ,KAAKhI,MAAM2Z,EAAYJ,GACvC9a,MAAMC,UAAU4Q,OAAOtP,MAAMpC,KAAKsa,aAAcyB,GAEhD/b,KAAKkM,KAAOyP,EAASxZ,OAEdnC,KAST,QAASsE,KACP,GAAI0X,GAAqB/Y,KAAKS,IAAI,GAAI1D,KAAK+H,QAAQkU,SAEnD,OAAOjc,MAAKsa,aAAa7V,OAAO,SAASiL,EAAM8K,GAC3C,GAAIpC,GAASwC,EAAoBJ,EAAYH,QAAQI,eAAetX,IAAI,SAAS0X,GAC/E,MAAO7a,MAAK+H,QAAQkU,SACjBhZ,KAAKU,MAAM6W,EAAYK,GAAamB,GAAsBA,EAC3DxB,EAAYK,IACdnN,KAAK1N,MAEP,OAAO0P,GAAO8K,EAAYH,QAAUjC,EAAO9L,KAAK,MAChDoB,KAAK1N,MAAO,KAAOA,KAAKgb,MAAQ,IAAM,IAW5C,QAASkB,GAAMjT,EAAG4B,GAIhB,MAHA6P,GAAa1a,KAAKsa,aAAc,SAASE,EAAaK,GACpDL,EAAYK,IAA+B,MAAjBA,EAAU,GAAa5R,EAAI4B,IAEhD7K,KAWT,QAASmc,GAAUlT,EAAG4B,GAIpB,MAHA6P,GAAa1a,KAAKsa,aAAc,SAASE,EAAaK,GACpDL,EAAYK,IAA+B,MAAjBA,EAAU,GAAa5R,EAAI4B,IAEhD7K,KAeT,QAASoc,GAAUC,GAOjB,MANA3B,GAAa1a,KAAKsa,aAAc,SAASE,EAAaK,EAAWF,EAAkBG,EAAYR,GAC7F,GAAIgC,GAAcD,EAAa7B,EAAaK,EAAWF,EAAkBG,EAAYR,IAClFgC,GAA+B,IAAhBA,KAChB9B,EAAYK,GAAayB,KAGtBtc,KAUT,QAASuc,GAAMvB,GACb,GAAIlK,GAAI,GAAI7Q,GAASwF,IAAIkK,KAAKqL,GAAShb,KAAKgb,MAM5C,OALAlK,GAAE5E,IAAMlM,KAAKkM,IACb4E,EAAEwJ,aAAeta,KAAKsa,aAAavZ,QAAQoC,IAAI,SAAuBqX,GACpE,MAAOva,GAASS,UAAW8Z,KAE7B1J,EAAE/I,QAAU9H,EAASS,UAAWV,KAAK+H,SAC9B+I,EAUT,QAAS0L,GAAenC,GACtB,GAAIjE,IACF,GAAInW,GAASwF,IAAIkK,KAWnB,OARA3P,MAAKsa,aAAapZ,QAAQ,SAASsZ,GAC9BA,EAAYH,UAAYA,EAAQ9M,eAAiE,IAAhD6I,EAAMA,EAAMjU,OAAS,GAAGmY,aAAanY,QACvFiU,EAAMhM,KAAK,GAAInK,GAASwF,IAAIkK,MAG9ByG,EAAMA,EAAMjU,OAAS,GAAGmY,aAAalQ,KAAKoQ,KAGrCpE,EAaT,QAAS9J,GAAKyE,EAAOiK,EAAOjT,GAE1B,IAAI,GADA0U,GAAa,GAAIxc,GAASwF,IAAIkK,KAAKqL,EAAOjT,GACtC5B,EAAI,EAAGA,EAAI4K,EAAM5O,OAAQgE,IAE/B,IAAI,GADAuJ,GAAOqB,EAAM5K,GACTe,EAAI,EAAGA,EAAIwI,EAAK4K,aAAanY,OAAQ+E,IAC3CuV,EAAWnC,aAAalQ,KAAKsF,EAAK4K,aAAapT,GAGnD,OAAOuV,GA3VT,GAAI7B,IACF8B,GAAI,IAAK,KACTC,GAAI,IAAK,KACT7L,GAAI,KAAM,KAAM,KAAM,KAAM,IAAK,KACjC8L,GAAI,KAAM,KAAM,MAAO,MAAO,KAAM,IAAK,MASvC5M,GAEFiM,SAAU,EA+UZhc,GAASwF,IAAIkK,KAAO1P,EAASiS,MAAMxR,QACjC+R,YAAasI,EACbtP,SAAUA,EACVsK,OAAQA,EACRlG,KAAMA,EACNC,KAAMA,EACNU,MAAOA,EACP0K,IAAKA,EACLgB,MAAOA,EACPC,UAAWA,EACXC,UAAWA,EACXxX,MAAOA,EACPN,UAAWA,EACXiY,MAAOA,EACPC,eAAgBA,IAGlBvc,EAASwF,IAAIkK,KAAKiL,oBAAsBA,EACxC3a,EAASwF,IAAIkK,KAAKrD,KAAOA,GACzBnM,OAAQC,SAAUH,GAOnB,SAAUE,EAAQC,EAAUH,GAC3B,YAqBA,SAAS4c,GAAK5Q,EAAOX,EAAWvD,GAC9B/H,KAAKiM,MAAQA,EACbjM,KAAKmM,aAAeF,IAAU6Q,EAAU7T,EAAI6T,EAAUjS,EAAIiS,EAAU7T,EACpEjJ,KAAKsL,UAAYA,EACjBtL,KAAK2H,WAAa2D,EAAUW,EAAM8Q,SAAWzR,EAAUW,EAAM+Q,WAC7Dhd,KAAK8N,WAAaxC,EAAUW,EAAMgR,YAClCjd,KAAK+H,QAAUA,EAzBjB,GAAI+U,IACF7T,GACEiD,IAAK,IACLa,IAAK,QACLkB,IAAK,aACL+O,UAAW,KACXD,QAAS,KACTE,WAAY,MAEdpS,GACEqB,IAAK,IACLa,IAAK,SACLkB,IAAK,WACL+O,UAAW,KACXD,QAAS,KACTE,WAAY,MAahBhd,GAAS4c,KAAO5c,EAASiS,MAAMxR,QAC7B+R,YAAaoK,EACbpP,aAAc,SAAS7L,EAAOyB,EAAOc,GACnC,KAAM,IAAI+P,OAAM,uCAIpBjU,EAAS4c,KAAK5Q,MAAQ6Q,GAEtB3c,OAAQC,SAAUH,GAOnB,SAAUE,EAAQC,EAAUH,GAC3B,YAEA,SAASid,GAAgBC,EAAU7R,EAAWvD,GAC5C9H,EAASid,gBAATjd,SAA+BwS,YAAYzR,KAAKhB,KAC9Cmd,EACA7R,EACAvD,GAEF/H,KAAK4H,OAAS3H,EAASoJ,UAAUrJ,KAAK2H,WAAYI,EAAQO,QAASP,EAAQuB,cAAevB,EAAQwB,eAAgBxB,EAAQyB,aAG5H,QAASiE,GAAa7L,GACpB,OACEsK,IAAKlM,KAAK2H,YAAc/F,EAAQ5B,KAAK4H,OAAO+B,KAAO3J,KAAK4H,OAAOC,MAC/DkF,IAAK9M,EAASyH,cAAc1H,KAAK2H,WAAY3H,KAAK4H,OAAOkC,KAAM9J,KAAK4H,SAIxE3H,EAASid,gBAAkBjd,EAAS4c,KAAKnc,QACvC+R,YAAayK,EACbzP,aAAcA,KAGhBtN,OAAQC,SAAUH,GAOnB,SAAUE,EAAQC,EAAUH,GAC3B,YAEA,SAASmd,GAASD,EAAU7R,EAAWvD,GACrC9H,EAASmd,SAATnd,SAAwBwS,YAAYzR,KAAKhB,KACvCmd,EACA7R,EACAvD,GAEF/H,KAAKqd,WAAard,KAAK2H,YAAcI,EAAQuV,WAAavV,EAAQwV,QAAU,EAAI,IAGlF,QAAS9P,GAAa7L,EAAOyB,GAC3B,OACE6I,IAAKlM,KAAKqd,WAAaha,EACvB0J,IAAK/M,KAAKqd,YAIdpd,EAASmd,SAAWnd,EAAS4c,KAAKnc,QAChC+R,YAAa2K,EACb3P,aAAcA,KAGhBtN,OAAQC,SAAUH,GASnB,SAASE,EAAQC,EAAUH,GAC1B,YAqGA,SAASgT,GAAYlL,GACnB,GAAIyV,MACAC,EAAiBxd,EAAS+G,mBAAmB/G,EAASmG,aAAapG,KAAKmE,KAAM4D,EAAQhC,aAAc/F,KAAKmE,KAAK6B,OAAO7D,OAGzHnC,MAAKkF,IAAMjF,EAAS4E,UAAU7E,KAAK8E,UAAWiD,EAAQhD,MAAOgD,EAAQ/C,OAAQ+C,EAAQgG,WAAW2P,MAEhG,IAAIpS,GAAYrL,EAAS8K,gBAAgB/K,KAAKkF,IAAK6C,EAASiI,EAAetJ,SACvE4B,EAAUrI,EAASkI,WAAWsV,EAAgB1V,GAE9CE,EAAQ,GAAIhI,GAASmd,SAASnd,EAAS4c,KAAK5Q,MAAMhD,EAAGqC,GACvDgS,UAAWtd,KAAKmE,KAAK6B,OAAO7D,OAC5Bob,QAASxV,EAAQ4V,YAGfzS,EAAQ,GAAIjL,GAASid,gBAAgBjd,EAAS4c,KAAK5Q,MAAMpB,EAAGS,GAC9DhD,QAASA,EACTgB,cAAevB,EAAQmD,MAAM5B,cAC7BE,YAAazB,EAAQmD,MAAM1B,cAIzB6D,EAAarN,KAAKkF,IAAImH,KAAK,KAAK1G,SAASoC,EAAQgG,WAAWV,YAC9DD,EAAYpN,KAAKkF,IAAImH,KAAK,KAAK1G,SAASoC,EAAQgG,WAAWX,UAE7DnN,GAASkN,WACPlF,EACAjI,KAAKmE,KAAK6B,OACVsF,EACA8B,EACAC,EACArN,KAAK6T,sBACL9L,EACA/H,KAAK+L,cAGP9L,EAASkN,WACPjC,EACAA,EAAMtD,OAAOuC,OACbmB,EACA8B,EACAC,EACArN,KAAK6T,sBACL9L,EACA/H,KAAK+L,cAIP/L,KAAKmE,KAAK+B,OAAOhF,QAAQ,SAASgF,EAAQ0X,GACxCJ,EAAaI,GAAe5d,KAAKkF,IAAImH,KAAK,KAG1CmR,EAAaI,GAAalY,MACxBmY,cAAe3X,EAAOmI,KACtBjH,KAAQnH,EAASiE,UAAUgC,EAAOkB,OACjCnH,EAASqF,MAAMoP,KAGlB8I,EAAaI,GAAajY,UACxBoC,EAAQgG,WAAW7H,OAClBA,EAAOjB,WAAa8C,EAAQgG,WAAW7H,OAAS,IAAMjG,EAASM,cAAcqd,IAC9EtR,KAAK,KAEP,IAAIkD,MACFsO,IAEFL,GAAeG,GAAa1c,QAAQ,SAASU,EAAOmc,GAClD,GAAIjV,IACFG,EAAGqC,EAAUnC,GAAKlB,EAAMwF,aAAa7L,EAAOmc,EAAYN,EAAeG,IAAc1R,IACrFrB,EAAGS,EAAUC,GAAKL,EAAMuC,aAAa7L,EAAOmc,EAAYN,EAAeG,IAAc1R,IAEvFsD,GAAgBpF,KAAKtB,EAAEG,EAAGH,EAAE+B,GAC5BiT,EAAS1T,MACPxI,MAAOA,EACPmc,WAAYA,EACZ3W,KAAMnH,EAASkH,YAAYjB,EAAQ6X,MAErCrQ,KAAK1N,MAEP,IAAIsO,IACF0P,WAAY/d,EAASmO,gBAAgBlI,EAAQ6B,EAAS,cACtDkW,UAAWhe,EAASmO,gBAAgBlI,EAAQ6B,EAAS,aACrDmW,SAAUje,EAASmO,gBAAgBlI,EAAQ6B,EAAS,YACpDoW,SAAUle,EAASmO,gBAAgBlI,EAAQ6B,EAAS,aAGlDqW,EAAgD,kBAA7B9P,GAAc0P,WACnC1P,EAAc0P,WAAc1P,EAAc0P,WAAa/d,EAASqP,cAAcmB,WAAaxQ,EAASqP,cAAcC,OAGhHG,EAAO0O,EAAU5O,EAAiBsO,EAiCtC,IA5BIxP,EAAc2P,WAEhBvO,EAAK4K,aAAapZ,QAAQ,SAASsZ,GACjC,GAAI6D,GAAQb,EAAaI,GAAavR,KAAK,QACzClD,GAAIqR,EAAYvR,EAChBsC,GAAIiP,EAAY3P,EAChBzB,GAAIoR,EAAYvR,EAAI,IACpBuC,GAAIgP,EAAY3P,GACf9C,EAAQgG,WAAWsQ,OAAO3Y,MAC3B9D,MAAS4Y,EAAYrW,KAAKvC,MAC1BwF,KAAQoT,EAAYrW,KAAKiD,MACxBnH,EAASqF,MAAMoP,IAElB1U,MAAK+L,aAAaQ,KAAK,QACrBC,KAAM,QACN5K,MAAO4Y,EAAYrW,KAAKvC,MACxByB,MAAOmX,EAAYrW,KAAK4Z,WACxB3W,KAAMoT,EAAYrW,KAAKiD,KACvBlB,OAAQA,EACR0X,YAAaA,EACb/R,MAAO2R,EAAaI,GACpBnR,QAAS4R,EACTpV,EAAGuR,EAAYvR,EACf4B,EAAG2P,EAAY3P,KAEjB6C,KAAK1N,OAGNsO,EAAc4P,SAAU,CACzB,GAAIpO,GAAO0N,EAAaI,GAAavR,KAAK,QACxC4D,EAAGP,EAAKpL,aACPyD,EAAQgG,WAAW+B,MAAM,GAAMpK,MAChCyE,OAAUsT,EAAeG,IACxB3d,EAASqF,MAAMoP,IAElB1U,MAAK+L,aAAaQ,KAAK,QACrBC,KAAM,OACNrC,OAAQsT,EAAeG,GACvBlO,KAAMA,EAAK6M,QACXjR,UAAWA,EACXjI,MAAOua,EACP1X,OAAQA,EACR0X,YAAaA,EACb/R,MAAO2R,EAAaI,GACpBnR,QAASqD,IAIb,GAAGxB,EAAc6P,SAAU,CAGzB,GAAIG,GAAWrb,KAAKC,IAAID,KAAK0G,IAAI5B,EAAQuW,SAAUpT,EAAMtD,OAAO1E,KAAMgI,EAAMtD,OAAO+B,KAG/E4U,EAAoBjT,EAAUC,GAAKL,EAAMuC,aAAa6Q,GAAUpS,GAGpEwD,GAAK8M,eAAe,KAAKpX,OAAO,SAA2BoZ,GAEzD,MAAOA,GAAYlE,aAAanY,OAAS,IACxCgB,IAAI,SAAuBsb,GAE5B,GAAIC,GAAeD,EAAkBnE,aAAa,GAC9CqE,EAAcF,EAAkBnE,aAAamE,EAAkBnE,aAAanY,OAAS,EAMzF,OAAOsc,GAAkBlC,OAAM,GAC5B9Q,SAAS,GACTsK,OAAO,GACPlG,KAAK6O,EAAazV,EAAGsV,GACrBzO,KAAK4O,EAAazV,EAAGyV,EAAa7T,GAClCY,SAASgT,EAAkBnE,aAAanY,OAAS,GACjD2N,KAAK6O,EAAY1V,EAAGsV,KAEtBrd,QAAQ,SAAoB0d,GAG7B,GAAIC,GAAOrB,EAAaI,GAAavR,KAAK,QACxC4D,EAAG2O,EAASta,aACXyD,EAAQgG,WAAW8Q,MAAM,GAAMnZ,MAChCyE,OAAUsT,EAAeG,IACxB3d,EAASqF,MAAMoP,IAGlB1U,MAAK+L,aAAaQ,KAAK,QACrBC,KAAM,OACNrC,OAAQsT,EAAeG,GACvBlO,KAAMkP,EAASrC,QACfrW,OAAQA,EACR0X,YAAaA,EACbtS,UAAWA,EACXjI,MAAOua,EACP/R,MAAO2R,EAAaI,GACpBnR,QAASoS,KAEXnR,KAAK1N,SAET0N,KAAK1N,OAEPA,KAAK+L,aAAaQ,KAAK,WACrB3E,OAAQsD,EAAMtD,OACd0D,UAAWA,EACXrD,MAAOA,EACPiD,MAAOA,EACPhG,IAAKlF,KAAKkF,IACV6C,QAASA,IAqFb,QAAS+W,GAAK9c,EAAOmC,EAAM4D,EAASyG,GAClCvO,EAAS6e,KAAT7e,SAAoBwS,YAAYzR,KAAKhB,KACnCgC,EACAmC,EACA6L,EACA/P,EAASS,UAAWsP,EAAgBjI,GACpCyG,GApYJ,GAAIwB,IAEF/H,OAEEC,OAAQ,GAERuD,SAAU,MAEVmB,aACE3D,EAAG,EACH4B,EAAG,GAGLqD,WAAW,EAEXL,UAAU,EAEVD,sBAAuB3N,EAASI,KAEhCmJ,aAAa,GAGf0B,OAEEhD,OAAQ,GAERuD,SAAU,QAEVmB,aACE3D,EAAG,EACH4B,EAAG,GAGLqD,WAAW,EAEXL,UAAU,EAEVD,sBAAuB3N,EAASI,KAEhCiJ,cAAe,GAEfE,aAAa,GAGfzE,MAAOX,OAEPY,OAAQZ,OAER8Z,UAAU,EAEVD,WAAW,EAEXE,UAAU,EAEVG,SAAU,EAEVN,YAAY,EAEZvV,IAAKrE,OAELmE,KAAMnE,OAEN4D,cACEpB,IAAK,GACLC,MAAO,GACPC,OAAQ,EACRC,KAAM,IAGR4W,WAAW,EAEX5X,aAAa,EAEbgI,YACE2P,MAAO,gBACPvP,MAAO,WACPd,WAAY,YACZnH,OAAQ,YACR4J,KAAM,UACNuO,MAAO,WACPQ,KAAM,UACN7Q,KAAM,UACNZ,UAAW,WACX2R,SAAU,cACVC,WAAY,gBACZC,MAAO,WACPC,IAAK,UAkTTjf,GAAS6e,KAAO7e,EAAS2T,KAAKlT,QAC5B+R,YAAaqM,EACb7L,YAAaA,KAGf9S,OAAQC,SAAUH,GAOnB,SAASE,EAAQC,EAAUH,GAC1B,YAoGA,SAASgT,GAAYlL,GACnB,GAKIO,GALAkV,KACArZ,EAAOlE,EAASmG,aAAapG,KAAKmE,KAAM4D,EAAQhC,aAChD0X,EAAiB1V,EAAQoX,iBAAmBhb,EAAKhB,IAAI,SAASvB,GAChE,OAAQA,KACL3B,EAAS+G,mBAAmB7C,EAAMnE,KAAKmE,KAAK6B,OAAO7D,OAWxD,IAPAnC,KAAKkF,IAAMjF,EAAS4E,UAClB7E,KAAK8E,UACLiD,EAAQhD,MACRgD,EAAQ/C,OACR+C,EAAQgG,WAAW2P,OAAS3V,EAAQqX,eAAiB,IAAMrX,EAAQgG,WAAWqR,eAAiB,KAG9FrX,EAAQsX,UAAW,CAEpB,GAAIC,GAAarf,EAAS4C,UAAU4a,EAAgB,WAClD,MAAO5c,OAAMC,UAAUC,MAAMC,KAAKC,WAAWwD,OAAOxE,EAASoC,IAAK,IAGpEiG,GAAUrI,EAASkI,YAAYmX,GAAavX,OAE5CO,GAAUrI,EAASkI,WAAWsV,EAAgB1V,EAGhDO,GAAQC,MAAQR,EAAQQ,OAA0B,IAAjBR,EAAQQ,KAAa,EAAID,EAAQC,MAClED,EAAQG,KAAOV,EAAQU,MAAwB,IAAhBV,EAAQU,IAAY,EAAIH,EAAQG,IAE/D,IAEI8W,GACFC,EACAC,EACAxX,EACAiD,EANEI,EAAYrL,EAAS8K,gBAAgB/K,KAAKkF,IAAK6C,EAASiI,EAAetJ,QAYzE8Y,GAHCzX,EAAQoX,mBAAqBpX,EAAQsX,UAGjB5B,EAAetb,OAC5B4F,EAAQoX,kBAAoBpX,EAAQsX,UAGvB,EAIArf,KAAKmE,KAAK6B,OAAO7D,OAIrC4F,EAAQqX,gBACTK,EAAYvU,EAAQ,GAAIjL,GAASmd,SAASnd,EAAS4c,KAAK5Q,MAAMpB,EAAGS,GAC/DgS,UAAWkC,IAGbD,EAAYtX,EAAQ,GAAIhI,GAASid,gBAAgBjd,EAAS4c,KAAK5Q,MAAMhD,EAAGqC,GACtEhD,QAASA,EACTgB,cAAevB,EAAQE,MAAMqB,cAC7BE,YAAazB,EAAQE,MAAMuB,YAC3BD,eAAgB,MAGlBkW,EAAYxX,EAAQ,GAAIhI,GAASmd,SAASnd,EAAS4c,KAAK5Q,MAAMhD,EAAGqC,GAC/DgS,UAAWkC,IAGbD,EAAYrU,EAAQ,GAAIjL,GAASid,gBAAgBjd,EAAS4c,KAAK5Q,MAAMpB,EAAGS,GACtEhD,QAASA,EACTgB,cAAevB,EAAQmD,MAAM5B,cAC7BE,YAAazB,EAAQmD,MAAM1B,YAC3BD,eAAgB,IAKpB,IAAI8D,GAAarN,KAAKkF,IAAImH,KAAK,KAAK1G,SAASoC,EAAQgG,WAAWV,YAC9DD,EAAYpN,KAAKkF,IAAImH,KAAK,KAAK1G,SAASoC,EAAQgG,WAAWX,WAE3DsS,EAAY3X,EAAQqX,eAAkB9T,EAAUnC,GAAKoW,EAAU9R,aAAa,GAAGvB,IAAQZ,EAAUC,GAAKgU,EAAU9R,aAAa,GAAGvB,IAEhIyT,IAEF1f,GAASkN,WACPsS,EACAzf,KAAKmE,KAAK6B,OACVsF,EACA8B,EACAC,EACArN,KAAK6T,sBACL9L,EACA/H,KAAK+L,cAGP9L,EAASkN,WACPoS,EACAA,EAAU3X,OAAOuC,OACjBmB,EACA8B,EACAC,EACArN,KAAK6T,sBACL9L,EACA/H,KAAK+L,cAIP/L,KAAKmE,KAAK+B,OAAOhF,QAAQ,SAASgF,EAAQ0X,GAExC,GAEEgC,GAFEC,EAAQjC,GAAe5d,KAAKmE,KAAK+B,OAAO/D,OAAS,GAAK,CAQxDyd,GAHC7X,EAAQoX,mBAAqBpX,EAAQsX,UAGnBI,EAAU9X,WAAa8V,EAAetb,OAAS,EAC1D4F,EAAQoX,kBAAoBpX,EAAQsX,UAGzBI,EAAU9X,WAAa,EAGvB8X,EAAU9X,WAAa8V,EAAeG,GAAazb,OAAS,EAGjFqb,EAAaI,GAAe5d,KAAKkF,IAAImH,KAAK,KAG1CmR,EAAaI,GAAalY,MACxBmY,cAAe3X,EAAOmI,KACtBjH,KAAQnH,EAASiE,UAAUgC,EAAOkB,OACjCnH,EAASqF,MAAMoP,KAGlB8I,EAAaI,GAAajY,UACxBoC,EAAQgG,WAAW7H,OAClBA,EAAOjB,WAAa8C,EAAQgG,WAAW7H,OAAS,IAAMjG,EAASM,cAAcqd,IAC9EtR,KAAK,MAEPmR,EAAeG,GAAa1c,QAAQ,SAASU,EAAOmc,GAClD,GAAI+B,GACFC,EACAC,EACAC,CAuCF,IAjCEA,EAHClY,EAAQoX,mBAAqBpX,EAAQsX,UAGhBzB,EACd7V,EAAQoX,kBAAoBpX,EAAQsX,UAGtB,EAGAtB,EAKtB+B,EADC/X,EAAQqX,gBAEPnW,EAAGqC,EAAUnC,GAAKoW,EAAU9R,aAAa7L,GAAS,EAAGmc,EAAYN,EAAeG,IAAc1R,IAC9FrB,EAAGS,EAAUC,GAAKkU,EAAUhS,aAAa7L,GAAS,EAAGqe,EAAqBxC,EAAeG,IAAc1R,MAIvGjD,EAAGqC,EAAUnC,GAAKsW,EAAUhS,aAAa7L,GAAS,EAAGqe,EAAqBxC,EAAeG,IAAc1R,IACvGrB,EAAGS,EAAUC,GAAKgU,EAAU9R,aAAa7L,GAAS,EAAGmc,EAAYN,EAAeG,IAAc1R,KAKlG4T,EAAUL,EAAUxT,MAAMC,MAAQ0T,GAAoB7X,EAAQqX,eAAiB,GAAK,GAEpFU,EAAUL,EAAUxT,MAAMC,MAASnE,EAAQsX,WAAatX,EAAQoX,iBAAoB,EAAIU,EAAQ9X,EAAQmY,mBAAqBnY,EAAQqX,eAAiB,GAAK,GAG3JY,EAAgBL,EAAiB5B,IAAe2B,EAChDC,EAAiB5B,GAAciC,GAAiBN,EAAYI,EAAUL,EAAUtT,aAAaD,MAGhF9H,SAAVxC,EAAH,CAIA,GAAIue,KACJA,GAAUV,EAAUxT,MAAMC,IAAM,KAAO4T,EAAUL,EAAUxT,MAAMC,KACjEiU,EAAUV,EAAUxT,MAAMC,IAAM,KAAO4T,EAAUL,EAAUxT,MAAMC,KAEjEiU,EAAUV,EAAUtT,aAAaD,IAAM,KAAOnE,EAAQsX,UAAYW,EAAgBN,EAClFS,EAAUV,EAAUtT,aAAaD,IAAM,KAAOnE,EAAQsX,UAAYM,EAAiB5B,GAAc+B,EAAUL,EAAUtT,aAAaD,KAElI6T,EAAMvC,EAAaI,GAAavR,KAAK,OAAQ8T,EAAWpY,EAAQgG,WAAWgS,KAAKra,MAC9E9D,MAASA,EACTwF,KAAQnH,EAASkH,YAAYjB,EAAQ6X,IACpC9d,EAASqF,MAAMoP,KAElB1U,KAAK+L,aAAaQ,KAAK,OAAQtM,EAASS,QACtC8L,KAAM,MACN5K,MAAOA,EACPyB,MAAO0a,EACP3W,KAAMnH,EAASkH,YAAYjB,EAAQ6X,GACnC7X,OAAQA,EACR0X,YAAaA,EACbtS,UAAWA,EACXO,MAAO2R,EAAaI,GACpBnR,QAASsT,GACRI,MACHzS,KAAK1N,QACP0N,KAAK1N,OAEPA,KAAK+L,aAAaQ,KAAK,WACrB3E,OAAQ2X,EAAU3X,OAClB0D,UAAWA,EACXrD,MAAOA,EACPiD,MAAOA,EACPhG,IAAKlF,KAAKkF,IACV6C,QAASA,IAyCb,QAASqY,GAAIpe,EAAOmC,EAAM4D,EAASyG,GACjCvO,EAASmgB,IAATngB,SAAmBwS,YAAYzR,KAAKhB,KAClCgC,EACAmC,EACA6L,EACA/P,EAASS,UAAWsP,EAAgBjI,GACpCyG,GA1WJ,GAAIwB,IAEF/H,OAEEC,OAAQ,GAERuD,SAAU,MAEVmB,aACE3D,EAAG,EACH4B,EAAG,GAGLqD,WAAW,EAEXL,UAAU,EAEVD,sBAAuB3N,EAASI,KAEhCiJ,cAAe,GAEfE,aAAa,GAGf0B,OAEEhD,OAAQ,GAERuD,SAAU,QAEVmB,aACE3D,EAAG,EACH4B,EAAG,GAGLqD,WAAW,EAEXL,UAAU,EAEVD,sBAAuB3N,EAASI,KAEhCiJ,cAAe,GAEfE,aAAa,GAGfzE,MAAOX,OAEPY,OAAQZ,OAERmE,KAAMnE,OAENqE,IAAKrE,OAELoF,aAAa,EAEbxB,cACEpB,IAAK,GACLC,MAAO,GACPC,OAAQ,EACRC,KAAM,IAGRmZ,kBAAmB,GAEnBb,WAAW,EAEXD,gBAAgB,EAEhBD,kBAAkB,EAElBpZ,aAAa,EAEbgI,YACE2P,MAAO,eACP0B,eAAgB,qBAChBjR,MAAO,WACPd,WAAY,YACZnH,OAAQ,YACR6Z,IAAK,SACL/R,KAAM,UACNZ,UAAW,WACX2R,SAAU,cACVC,WAAY,gBACZC,MAAO,WACPC,IAAK,UAyRTjf,GAASmgB,IAAMngB,EAAS2T,KAAKlT,QAC3B+R,YAAa2N,EACbnN,YAAaA,KAGf9S,OAAQC,SAAUH,GAOnB,SAASE,EAAQC,EAAUH,GAC1B,YAqDA,SAASogB,GAAwBC,EAAQnS,EAAOoS,GAC9C,GAAIC,GAAarS,EAAMlF,EAAIqX,EAAOrX,CAElC,OAAGuX,IAA4B,YAAdD,IACdC,GAA4B,YAAdD,EACR,QACCC,GAA4B,YAAdD,IACrBC,GAA4B,YAAdD,EACR,MAEA,SASX,QAAStN,GAAYlL,GACnB,GACEuD,GACAd,EACAiW,EACAC,EAJElD,KAKFmD,EAAa5Y,EAAQ4Y,WACrB1Z,EAAYhH,EAASmG,aAAapG,KAAKmE,KAAM4D,EAAQhC,YAGvD/F,MAAKkF,IAAMjF,EAAS4E,UAAU7E,KAAK8E,UAAWiD,EAAQhD,MAAOgD,EAAQ/C,OAAO+C,EAAQ6Y,MAAQ7Y,EAAQgG,WAAW8S,WAAa9Y,EAAQgG,WAAW+S,UAE/IxV,EAAYrL,EAAS8K,gBAAgB/K,KAAKkF,IAAK6C,EAASiI,EAAetJ,SAEvE8D,EAASvH,KAAK0G,IAAI2B,EAAUvG,QAAU,EAAGuG,EAAUtG,SAAW,GAE9D0b,EAAe3Y,EAAQgZ,OAAS9Z,EAAUxC,OAAO,SAASuc,EAAeC,GACvE,MAAOD,GAAgBC,GACtB,GAKHzW,GAAUzC,EAAQ6Y,MAAQ7Y,EAAQmZ,WAAa,EAAK,EAKlDT,EAD2B,YAA1B1Y,EAAQoZ,eAA+BpZ,EAAQ6Y,MAClCpW,EACoB,WAA1BzC,EAAQoZ,cAEF,EAIA3W,EAAS,EAGzBiW,GAAe1Y,EAAQ6E,WAevB,KAAK,GAZD0T,IACFrX,EAAGqC,EAAUnC,GAAKmC,EAAUvG,QAAU,EACtC8F,EAAGS,EAAUE,GAAKF,EAAUtG,SAAW,GAIrCoc,EAEU,IAFaphB,KAAKmE,KAAK+B,OAAOd,OAAO,SAASic,GAC1D,MAAOA,GAAI9a,eAAe,SAAyB,IAAd8a,EAAIzf,MAAsB,IAARyf,IACtDlf,OAIMgE,EAAI,EAAGA,EAAInG,KAAKmE,KAAK+B,OAAO/D,OAAQgE,IAAK,CAChD,GAAID,GAASlG,KAAKmE,KAAK+B,OAAOC,EAC9BqX,GAAarX,GAAKnG,KAAKkF,IAAImH,KAAK,IAAK,KAAM,MAAM,GAGjDmR,EAAarX,GAAGT,MACdmY,cAAe3X,EAAOmI,MACrBpO,EAASqF,MAAMoP,KAGlB8I,EAAarX,GAAGR,UACdoC,EAAQgG,WAAW7H,OAClBA,EAAOjB,WAAa8C,EAAQgG,WAAW7H,OAAS,IAAMjG,EAASM,cAAc4F,IAC9EmG,KAAK,KAEP,IAAIgV,GAAWX,EAAa1Z,EAAUd,GAAKua,EAAe,GAGvDY,GAAWX,IAAe,MAC3BW,GAAY,IAGd,IAAIrC,GAAQhf,EAASoK,iBAAiBiW,EAAOrX,EAAGqX,EAAOzV,EAAGL,EAAQmW,GAAoB,IAANxa,GAAWib,EAAuB,EAAI,KACpHlC,EAAMjf,EAASoK,iBAAiBiW,EAAOrX,EAAGqX,EAAOzV,EAAGL,EAAQ8W,GAG1D5R,EAAO,GAAIzP,GAASwF,IAAIkK,MAAM5H,EAAQ6Y,OACvC/Q,KAAKqP,EAAIjW,EAAGiW,EAAIrU,GAChBqQ,IAAI1Q,EAAQA,EAAQ,EAAG8W,EAAWX,EAAa,IAAK,EAAG1B,EAAMhW,EAAGgW,EAAMpU,EAGrE9C,GAAQ6Y,OACVlR,EAAKI,KAAKwQ,EAAOrX,EAAGqX,EAAOzV,EAK7B,IAAI2P,GAAcgD,EAAarX,GAAGkG,KAAK,QACrC4D,EAAGP,EAAKpL,aACPyD,EAAQ6Y,MAAQ7Y,EAAQgG,WAAWwT,WAAaxZ,EAAQgG,WAAWyT,SAiCtE,IA9BAhH,EAAY9U,MACV9D,MAASqF,EAAUd,GACnBiB,KAAQnH,EAASiE,UAAUgC,EAAOkB,OACjCnH,EAASqF,MAAMoP,KAGf3M,EAAQ6Y,OACTpG,EAAY9U;AACVE,MAAS,mBAAqBmC,EAAQmZ,WAAc,OAKxDlhB,KAAK+L,aAAaQ,KAAK,QACrBC,KAAM,QACN5K,MAAOqF,EAAUd,GACjBua,aAAcA,EACdrd,MAAO8C,EACPiB,KAAMlB,EAAOkB,KACblB,OAAQA,EACR2F,MAAO2R,EAAarX,GACpBsG,QAAS+N,EACT9K,KAAMA,EAAK6M,QACX+D,OAAQA,EACR9V,OAAQA,EACRmW,WAAYA,EACZW,SAAUA,IAITvZ,EAAQmG,UAAW,CAEpB,GAAIiT,GAAgBlhB,EAASoK,iBAAiBiW,EAAOrX,EAAGqX,EAAOzV,EAAG4V,EAAaE,GAAcW,EAAWX,GAAc,GACpHc,EAAoB1Z,EAAQ6F,sBAAsB5N,KAAKmE,KAAK6B,OAAShG,KAAKmE,KAAK6B,OAAOG,GAAKc,EAAUd,GAAIA,EAE3G,IAAGsb,GAA2C,IAAtBA,EAAyB,CAC/C,GAAI3U,GAAe0Q,EAAarX,GAAGkG,KAAK,QACtCqV,GAAIP,EAAclY,EAClB0Y,GAAIR,EAActW,EAClB+W,cAAevB,EAAwBC,EAAQa,EAAepZ,EAAQ8Z,iBACrE9Z,EAAQgG,WAAWI,OAAOjB,KAAK,GAAKuU,EAGvCzhB,MAAK+L,aAAaQ,KAAK,QACrBC,KAAM,QACNnJ,MAAO8C,EACP0F,MAAO2R,EAAarX,GACpBsG,QAASK,EACTI,KAAM,GAAKuU,EACXxY,EAAGkY,EAAclY,EACjB4B,EAAGsW,EAActW,KAOvB8V,EAAaW,EAGfthB,KAAK+L,aAAaQ,KAAK,WACrBjB,UAAWA,EACXpG,IAAKlF,KAAKkF,IACV6C,QAASA,IAwEb,QAAS+Z,GAAI9f,EAAOmC,EAAM4D,EAASyG,GACjCvO,EAAS6hB,IAAT7hB,SAAmBwS,YAAYzR,KAAKhB,KAClCgC,EACAmC,EACA6L,EACA/P,EAASS,UAAWsP,EAAgBjI,GACpCyG,GA7SJ,GAAIwB,IAEFjL,MAAOX,OAEPY,OAAQZ,OAER4D,aAAc,EAEd+F,YACE+S,SAAU,eACVD,WAAY,iBACZ3a,OAAQ,YACRsb,SAAU,eACVD,WAAY,iBACZpT,MAAO,YAGTwS,WAAY,EAEZI,MAAO3c,OAEPwc,OAAO,EAEPM,WAAY,GAEZhT,WAAW,EAEXtB,YAAa,EAEbuU,cAAe,SAEfvT,sBAAuB3N,EAASI,KAEhCwhB,eAAgB,UAEhB9b,aAAa,EA8Qf9F,GAAS6hB,IAAM7hB,EAAS2T,KAAKlT,QAC3B+R,YAAaqP,EACb7O,YAAaA,EACboN,wBAAyBA,KAG3BlgB,OAAQC,SAAUH,GAEbA", "sourcesContent": ["(function (root, factory) {\n  if (typeof define === 'function' && define.amd) {\n    // AMD. Register as an anonymous module unless amdModuleId is set\n    define([], function () {\n      return (root['Chartist'] = factory());\n    });\n  } else if (typeof exports === 'object') {\n    // Node. Does not work with strict CommonJS, but\n    // only CommonJS-like environments that support module.exports,\n    // like Node.\n    module.exports = factory();\n  } else {\n    root['Chartist'] = factory();\n  }\n}(this, function () {\n\n/* Chartist.js 0.8.3\n * Copyright © 2015 Gion Kunz\n * Free to use under the WTFPL license.\n * http://www.wtfpl.net/\n */\n/**\n * The core module of Chartist that is mainly providing static functions and higher level functions for chart modules.\n *\n * @module Chartist.Core\n */\nvar Chartist = {\n  version: '0.8.3'\n};\n\n(function (window, document, Chartist) {\n  'use strict';\n\n  /**\n   * Helps to simplify functional style code\n   *\n   * @memberof Chartist.Core\n   * @param {*} n This exact value will be returned by the noop function\n   * @return {*} The same value that was provided to the n parameter\n   */\n  Chartist.noop = function (n) {\n    return n;\n  };\n\n  /**\n   * Generates a-z from a number 0 to 26\n   *\n   * @memberof Chartist.Core\n   * @param {Number} n A number from 0 to 26 that will result in a letter a-z\n   * @return {String} A character from a-z based on the input number n\n   */\n  Chartist.alphaNumerate = function (n) {\n    // Limit to a-z\n    return String.fromCharCode(97 + n % 26);\n  };\n\n  /**\n   * Simple recursive object extend\n   *\n   * @memberof Chartist.Core\n   * @param {Object} target Target object where the source will be merged into\n   * @param {Object...} sources This object (objects) will be merged into target and then target is returned\n   * @return {Object} An object that has the same reference as target but is extended and merged with the properties of source\n   */\n  Chartist.extend = function (target) {\n    target = target || {};\n\n    var sources = Array.prototype.slice.call(arguments, 1);\n    sources.forEach(function(source) {\n      for (var prop in source) {\n        if (typeof source[prop] === 'object' && source[prop] !== null && !(source[prop] instanceof Array)) {\n          target[prop] = Chartist.extend({}, target[prop], source[prop]);\n        } else {\n          target[prop] = source[prop];\n        }\n      }\n    });\n\n    return target;\n  };\n\n  /**\n   * Replaces all occurrences of subStr in str with newSubStr and returns a new string.\n   *\n   * @memberof Chartist.Core\n   * @param {String} str\n   * @param {String} subStr\n   * @param {String} newSubStr\n   * @return {String}\n   */\n  Chartist.replaceAll = function(str, subStr, newSubStr) {\n    return str.replace(new RegExp(subStr, 'g'), newSubStr);\n  };\n\n  /**\n   * Converts a string to a number while removing the unit if present. If a number is passed then this will be returned unmodified.\n   *\n   * @memberof Chartist.Core\n   * @param {String|Number} value\n   * @return {Number} Returns the string as number or NaN if the passed length could not be converted to pixel\n   */\n  Chartist.stripUnit = function(value) {\n    if(typeof value === 'string') {\n      value = value.replace(/[^0-9\\+-\\.]/g, '');\n    }\n\n    return +value;\n  };\n\n  /**\n   * Converts a number to a string with a unit. If a string is passed then this will be returned unmodified.\n   *\n   * @memberof Chartist.Core\n   * @param {Number} value\n   * @param {String} unit\n   * @return {String} Returns the passed number value with unit.\n   */\n  Chartist.ensureUnit = function(value, unit) {\n    if(typeof value === 'number') {\n      value = value + unit;\n    }\n\n    return value;\n  };\n\n  /**\n   * This is a wrapper around document.querySelector that will return the query if it's already of type Node\n   *\n   * @memberof Chartist.Core\n   * @param {String|Node} query The query to use for selecting a Node or a DOM node that will be returned directly\n   * @return {Node}\n   */\n  Chartist.querySelector = function(query) {\n    return query instanceof Node ? query : document.querySelector(query);\n  };\n\n  /**\n   * Functional style helper to produce array with given length initialized with undefined values\n   *\n   * @memberof Chartist.Core\n   * @param length\n   * @return {Array}\n   */\n  Chartist.times = function(length) {\n    return Array.apply(null, new Array(length));\n  };\n\n  /**\n   * Sum helper to be used in reduce functions\n   *\n   * @memberof Chartist.Core\n   * @param previous\n   * @param current\n   * @return {*}\n   */\n  Chartist.sum = function(previous, current) {\n    return previous + (current ? current : 0);\n  };\n\n  /**\n   * Multiply helper to be used in `Array.map` for multiplying each value of an array with a factor.\n   *\n   * @memberof Chartist.Core\n   * @param {Number} factor\n   * @returns {Function} Function that can be used in `Array.map` to multiply each value in an array\n   */\n  Chartist.mapMultiply = function(factor) {\n    return function(num) {\n      return num * factor;\n    };\n  };\n\n  /**\n   * Add helper to be used in `Array.map` for adding a addend to each value of an array.\n   *\n   * @memberof Chartist.Core\n   * @param {Number} addend\n   * @returns {Function} Function that can be used in `Array.map` to add a addend to each value in an array\n   */\n  Chartist.mapAdd = function(addend) {\n    return function(num) {\n      return num + addend;\n    };\n  };\n\n  /**\n   * Map for multi dimensional arrays where their nested arrays will be mapped in serial. The output array will have the length of the largest nested array. The callback function is called with variable arguments where each argument is the nested array value (or undefined if there are no more values).\n   *\n   * @memberof Chartist.Core\n   * @param arr\n   * @param cb\n   * @return {Array}\n   */\n  Chartist.serialMap = function(arr, cb) {\n    var result = [],\n        length = Math.max.apply(null, arr.map(function(e) {\n          return e.length;\n        }));\n\n    Chartist.times(length).forEach(function(e, index) {\n      var args = arr.map(function(e) {\n        return e[index];\n      });\n\n      result[index] = cb.apply(null, args);\n    });\n\n    return result;\n  };\n\n  /**\n   * This helper function can be used to round values with certain precision level after decimal. This is used to prevent rounding errors near float point precision limit.\n   *\n   * @memberof Chartist.Core\n   * @param {Number} value The value that should be rounded with precision\n   * @param {Number} [digits] The number of digits after decimal used to do the rounding\n   * @returns {number} Rounded value\n   */\n  Chartist.roundWithPrecision = function(value, digits) {\n    var precision = Math.pow(10, digits || Chartist.precision);\n    return Math.round(value * precision) / precision;\n  };\n\n  /**\n   * Precision level used internally in Chartist for rounding. If you require more decimal places you can increase this number.\n   *\n   * @memberof Chartist.Core\n   * @type {number}\n   */\n  Chartist.precision = 8;\n\n  /**\n   * A map with characters to escape for strings to be safely used as attribute values.\n   *\n   * @memberof Chartist.Core\n   * @type {Object}\n   */\n  Chartist.escapingMap = {\n    '&': '&amp;',\n    '<': '&lt;',\n    '>': '&gt;',\n    '\"': '&quot;',\n    '\\'': '&#039;'\n  };\n\n  /**\n   * This function serializes arbitrary data to a string. In case of data that can't be easily converted to a string, this function will create a wrapper object and serialize the data using JSON.stringify. The outcoming string will always be escaped using Chartist.escapingMap.\n   * If called with null or undefined the function will return immediately with null or undefined.\n   *\n   * @memberof Chartist.Core\n   * @param {Number|String|Object} data\n   * @return {String}\n   */\n  Chartist.serialize = function(data) {\n    if(data === null || data === undefined) {\n      return data;\n    } else if(typeof data === 'number') {\n      data = ''+data;\n    } else if(typeof data === 'object') {\n      data = JSON.stringify({data: data});\n    }\n\n    return Object.keys(Chartist.escapingMap).reduce(function(result, key) {\n      return Chartist.replaceAll(result, key, Chartist.escapingMap[key]);\n    }, data);\n  };\n\n  /**\n   * This function de-serializes a string previously serialized with Chartist.serialize. The string will always be unescaped using Chartist.escapingMap before it's returned. Based on the input value the return type can be Number, String or Object. JSON.parse is used with try / catch to see if the unescaped string can be parsed into an Object and this Object will be returned on success.\n   *\n   * @memberof Chartist.Core\n   * @param {String} data\n   * @return {String|Number|Object}\n   */\n  Chartist.deserialize = function(data) {\n    if(typeof data !== 'string') {\n      return data;\n    }\n\n    data = Object.keys(Chartist.escapingMap).reduce(function(result, key) {\n      return Chartist.replaceAll(result, Chartist.escapingMap[key], key);\n    }, data);\n\n    try {\n      data = JSON.parse(data);\n      data = data.data !== undefined ? data.data : data;\n    } catch(e) {}\n\n    return data;\n  };\n\n  /**\n   * Create or reinitialize the SVG element for the chart\n   *\n   * @memberof Chartist.Core\n   * @param {Node} container The containing DOM Node object that will be used to plant the SVG element\n   * @param {String} width Set the width of the SVG element. Default is 100%\n   * @param {String} height Set the height of the SVG element. Default is 100%\n   * @param {String} className Specify a class to be added to the SVG element\n   * @return {Object} The created/reinitialized SVG element\n   */\n  Chartist.createSvg = function (container, width, height, className) {\n    var svg;\n\n    width = width || '100%';\n    height = height || '100%';\n\n    // Check if there is a previous SVG element in the container that contains the Chartist XML namespace and remove it\n    // Since the DOM API does not support namespaces we need to manually search the returned list http://www.w3.org/TR/selectors-api/\n    Array.prototype.slice.call(container.querySelectorAll('svg')).filter(function filterChartistSvgObjects(svg) {\n      return svg.getAttribute(Chartist.xmlNs.qualifiedName);\n    }).forEach(function removePreviousElement(svg) {\n      container.removeChild(svg);\n    });\n\n    // Create svg object with width and height or use 100% as default\n    svg = new Chartist.Svg('svg').attr({\n      width: width,\n      height: height\n    }).addClass(className).attr({\n      style: 'width: ' + width + '; height: ' + height + ';'\n    });\n\n    // Add the DOM node to our container\n    container.appendChild(svg._node);\n\n    return svg;\n  };\n\n\n  /**\n   * Reverses the series, labels and series data arrays.\n   *\n   * @memberof Chartist.Core\n   * @param data\n   */\n  Chartist.reverseData = function(data) {\n    data.labels.reverse();\n    data.series.reverse();\n    for (var i = 0; i < data.series.length; i++) {\n      if(typeof(data.series[i]) === 'object' && data.series[i].data !== undefined) {\n        data.series[i].data.reverse();\n      } else if(data.series[i] instanceof Array) {\n        data.series[i].reverse();\n      }\n    }\n  };\n\n  /**\n   * Convert data series into plain array\n   *\n   * @memberof Chartist.Core\n   * @param {Object} data The series object that contains the data to be visualized in the chart\n   * @param {Boolean} reverse If true the whole data is reversed by the getDataArray call. This will modify the data object passed as first parameter. The labels as well as the series order is reversed. The whole series data arrays are reversed too.\n   * @return {Array} A plain array that contains the data to be visualized in the chart\n   */\n  Chartist.getDataArray = function (data, reverse) {\n    // If the data should be reversed but isn't we need to reverse it\n    // If it's reversed but it shouldn't we need to reverse it back\n    // That's required to handle data updates correctly and to reflect the responsive configurations\n    if(reverse && !data.reversed || !reverse && data.reversed) {\n      Chartist.reverseData(data);\n      data.reversed = !data.reversed;\n    }\n\n    // Rcursively walks through nested arrays and convert string values to numbers and objects with value properties\n    // to values. Check the tests in data core -> data normalization for a detailed specification of expected values\n    function recursiveConvert(value) {\n      if(value === undefined || value === null || (typeof value === 'number' && isNaN(value))) {\n        return undefined;\n      } else if((value.data || value) instanceof Array) {\n        return (value.data || value).map(recursiveConvert);\n      } else if(value.hasOwnProperty('value')) {\n        return recursiveConvert(value.value);\n      } else {\n        return +value;\n      }\n    }\n\n    return data.series.map(recursiveConvert);\n  };\n\n  /**\n   * Converts a number into a padding object.\n   *\n   * @memberof Chartist.Core\n   * @param {Object|Number} padding\n   * @param {Number} [fallback] This value is used to fill missing values if a incomplete padding object was passed\n   * @returns {Object} Returns a padding object containing top, right, bottom, left properties filled with the padding number passed in as argument. If the argument is something else than a number (presumably already a correct padding object) then this argument is directly returned.\n   */\n  Chartist.normalizePadding = function(padding, fallback) {\n    fallback = fallback || 0;\n\n    return typeof padding === 'number' ? {\n      top: padding,\n      right: padding,\n      bottom: padding,\n      left: padding\n    } : {\n      top: typeof padding.top === 'number' ? padding.top : fallback,\n      right: typeof padding.right === 'number' ? padding.right : fallback,\n      bottom: typeof padding.bottom === 'number' ? padding.bottom : fallback,\n      left: typeof padding.left === 'number' ? padding.left : fallback\n    };\n  };\n\n  /**\n   * Adds missing values at the end of the array. This array contains the data, that will be visualized in the chart\n   *\n   * @memberof Chartist.Core\n   * @param {Array} dataArray The array that contains the data to be visualized in the chart. The array in this parameter will be modified by function.\n   * @param {Number} length The length of the x-axis data array.\n   * @return {Array} The array that got updated with missing values.\n   */\n  Chartist.normalizeDataArray = function (dataArray, length) {\n    for (var i = 0; i < dataArray.length; i++) {\n      if (dataArray[i].length === length) {\n        continue;\n      }\n\n      for (var j = dataArray[i].length; j < length; j++) {\n        dataArray[i][j] = undefined;\n      }\n    }\n\n    return dataArray;\n  };\n\n  Chartist.getMetaData = function(series, index) {\n    var value = series.data ? series.data[index] : series[index];\n    return value ? Chartist.serialize(value.meta) : undefined;\n  };\n\n  /**\n   * Calculate the order of magnitude for the chart scale\n   *\n   * @memberof Chartist.Core\n   * @param {Number} value The value Range of the chart\n   * @return {Number} The order of magnitude\n   */\n  Chartist.orderOfMagnitude = function (value) {\n    return Math.floor(Math.log(Math.abs(value)) / Math.LN10);\n  };\n\n  /**\n   * Project a data length into screen coordinates (pixels)\n   *\n   * @memberof Chartist.Core\n   * @param {Object} axisLength The svg element for the chart\n   * @param {Number} length Single data value from a series array\n   * @param {Object} bounds All the values to set the bounds of the chart\n   * @return {Number} The projected data length in pixels\n   */\n  Chartist.projectLength = function (axisLength, length, bounds) {\n    return length / bounds.range * axisLength;\n  };\n\n  /**\n   * Get the height of the area in the chart for the data series\n   *\n   * @memberof Chartist.Core\n   * @param {Object} svg The svg element for the chart\n   * @param {Object} options The Object that contains all the optional values for the chart\n   * @return {Number} The height of the area in the chart for the data series\n   */\n  Chartist.getAvailableHeight = function (svg, options) {\n    return Math.max((Chartist.stripUnit(options.height) || svg.height()) - (options.chartPadding.top +  options.chartPadding.bottom) - options.axisX.offset, 0);\n  };\n\n  /**\n   * Get highest and lowest value of data array. This Array contains the data that will be visualized in the chart.\n   *\n   * @memberof Chartist.Core\n   * @param {Array} dataArray The array that contains the data to be visualized in the chart\n   * @param {Object} options The Object that contains all the optional values for the chart\n   * @return {Object} An object that contains the highest and lowest value that will be visualized on the chart.\n   */\n  Chartist.getHighLow = function (dataArray, options) {\n    var highLow = {\n        high: options.high === undefined ? -Number.MAX_VALUE : +options.high,\n        low: options.low === undefined ? Number.MAX_VALUE : +options.low\n      },\n      findHigh = options.high === undefined,\n      findLow = options.low === undefined;\n\n    // Function to recursively walk through arrays and find highest and lowest number\n    function recursiveHighLow(data) {\n      if(data instanceof Array) {\n        for (var i = 0; i < data.length; i++) {\n          recursiveHighLow(data[i]);\n        }\n      } else {\n        if (findHigh && data > highLow.high) {\n          highLow.high = data;\n        }\n\n        if (findLow && data < highLow.low) {\n          highLow.low = data;\n        }\n      }\n    }\n\n    // Start to find highest and lowest number recursively\n    recursiveHighLow(dataArray);\n\n    // If high and low are the same because of misconfiguration or flat data (only the same value) we need\n    // to set the high or low to 0 depending on the polarity\n    if (highLow.high <= highLow.low) {\n      // If both values are 0 we set high to 1\n      if (highLow.low === 0) {\n        highLow.high = 1;\n      } else if (highLow.low < 0) {\n        // If we have the same negative value for the bounds we set bounds.high to 0\n        highLow.high = 0;\n      } else {\n        // If we have the same positive value for the bounds we set bounds.low to 0\n        highLow.low = 0;\n      }\n    }\n\n    return highLow;\n  };\n\n  /**\n   * Pollard Rho Algorithm to find smallest factor of an integer value. There are more efficient algorithms for factorization, but this one is quite efficient and not so complex.\n   *\n   * @memberof Chartist.Core\n   * @param {Number} num An integer number where the smallest factor should be searched for\n   * @returns {Number} The smallest integer factor of the parameter num.\n   */\n  Chartist.rho = function(num) {\n    if(num === 1) {\n      return num;\n    }\n\n    function gcd(p, q) {\n      if (p % q === 0) {\n        return q;\n      } else {\n        return gcd(q, p % q);\n      }\n    }\n\n    function f(x) {\n      return x * x + 1;\n    }\n\n    var x1 = 2, x2 = 2, divisor;\n    if (num % 2 === 0) {\n      return 2;\n    }\n\n    do {\n      x1 = f(x1) % num;\n      x2 = f(f(x2)) % num;\n      divisor = gcd(Math.abs(x1 - x2), num);\n    } while (divisor === 1);\n\n    return divisor;\n  };\n\n  /**\n   * Calculate and retrieve all the bounds for the chart and return them in one array\n   *\n   * @memberof Chartist.Core\n   * @param {Number} axisLength The length of the Axis used for\n   * @param {Object} highLow An object containing a high and low property indicating the value range of the chart.\n   * @param {Number} scaleMinSpace The minimum projected length a step should result in\n   * @param {Number} referenceValue The reference value for the chart.\n   * @param {Boolean} onlyInteger\n   * @return {Object} All the values to set the bounds of the chart\n   */\n  Chartist.getBounds = function (axisLength, highLow, scaleMinSpace, referenceValue, onlyInteger) {\n    var i,\n      newMin,\n      newMax,\n      bounds = {\n        high: highLow.high,\n        low: highLow.low\n      };\n\n    // Overrides of high / low based on reference value, it will make sure that the invisible reference value is\n    // used to generate the chart. This is useful when the chart always needs to contain the position of the\n    // invisible reference value in the view i.e. for bipolar scales.\n    if (referenceValue || referenceValue === 0) {\n      bounds.high = Math.max(referenceValue, bounds.high);\n      bounds.low = Math.min(referenceValue, bounds.low);\n    }\n\n    bounds.valueRange = bounds.high - bounds.low;\n    bounds.oom = Chartist.orderOfMagnitude(bounds.valueRange);\n    bounds.step = Math.pow(10, bounds.oom);\n    bounds.min = Math.floor(bounds.low / bounds.step) * bounds.step;\n    bounds.max = Math.ceil(bounds.high / bounds.step) * bounds.step;\n    bounds.range = bounds.max - bounds.min;\n    bounds.numberOfSteps = Math.round(bounds.range / bounds.step);\n\n    // Optimize scale step by checking if subdivision is possible based on horizontalGridMinSpace\n    // If we are already below the scaleMinSpace value we will scale up\n    var length = Chartist.projectLength(axisLength, bounds.step, bounds);\n    var scaleUp = length < scaleMinSpace;\n    var smallestFactor = onlyInteger ? Chartist.rho(bounds.range) : 0;\n\n    // First check if we should only use integer steps and if step 1 is still larger than scaleMinSpace so we can use 1\n    if(onlyInteger && Chartist.projectLength(axisLength, 1, bounds) >= scaleMinSpace) {\n      bounds.step = 1;\n    } else if(onlyInteger && smallestFactor < bounds.step && Chartist.projectLength(axisLength, smallestFactor, bounds) >= scaleMinSpace) {\n      // If step 1 was too small, we can try the smallest factor of range\n      // If the smallest factor is smaller than the current bounds.step and the projected length of smallest factor\n      // is larger than the scaleMinSpace we should go for it.\n      bounds.step = smallestFactor;\n    } else {\n      // Trying to divide or multiply by 2 and find the best step value\n      while (true) {\n        if (scaleUp && Chartist.projectLength(axisLength, bounds.step, bounds) <= scaleMinSpace) {\n          bounds.step *= 2;\n        } else if (!scaleUp && Chartist.projectLength(axisLength, bounds.step / 2, bounds) >= scaleMinSpace) {\n          bounds.step /= 2;\n          if(onlyInteger && bounds.step % 1 !== 0) {\n            bounds.step *= 2;\n            break;\n          }\n        } else {\n          break;\n        }\n      }\n    }\n\n    // Narrow min and max based on new step\n    newMin = bounds.min;\n    newMax = bounds.max;\n    while(newMin + bounds.step <= bounds.low) {\n      newMin += bounds.step;\n    }\n    while(newMax - bounds.step >= bounds.high) {\n      newMax -= bounds.step;\n    }\n    bounds.min = newMin;\n    bounds.max = newMax;\n    bounds.range = bounds.max - bounds.min;\n\n    bounds.values = [];\n    for (i = bounds.min; i <= bounds.max; i += bounds.step) {\n      bounds.values.push(Chartist.roundWithPrecision(i));\n    }\n\n    return bounds;\n  };\n\n  /**\n   * Calculate cartesian coordinates of polar coordinates\n   *\n   * @memberof Chartist.Core\n   * @param {Number} centerX X-axis coordinates of center point of circle segment\n   * @param {Number} centerY X-axis coordinates of center point of circle segment\n   * @param {Number} radius Radius of circle segment\n   * @param {Number} angleInDegrees Angle of circle segment in degrees\n   * @return {Number} Coordinates of point on circumference\n   */\n  Chartist.polarToCartesian = function (centerX, centerY, radius, angleInDegrees) {\n    var angleInRadians = (angleInDegrees - 90) * Math.PI / 180.0;\n\n    return {\n      x: centerX + (radius * Math.cos(angleInRadians)),\n      y: centerY + (radius * Math.sin(angleInRadians))\n    };\n  };\n\n  /**\n   * Initialize chart drawing rectangle (area where chart is drawn) x1,y1 = bottom left / x2,y2 = top right\n   *\n   * @memberof Chartist.Core\n   * @param {Object} svg The svg element for the chart\n   * @param {Object} options The Object that contains all the optional values for the chart\n   * @param {Number} [fallbackPadding] The fallback padding if partial padding objects are used\n   * @return {Object} The chart rectangles coordinates inside the svg element plus the rectangles measurements\n   */\n  Chartist.createChartRect = function (svg, options, fallbackPadding) {\n    var hasAxis = !!(options.axisX || options.axisY);\n    var yAxisOffset = hasAxis ? options.axisY.offset : 0;\n    var xAxisOffset = hasAxis ? options.axisX.offset : 0;\n    // If width or height results in invalid value (including 0) we fallback to the unitless settings or even 0\n    var width = svg.width() || Chartist.stripUnit(options.width) || 0;\n    var height = svg.height() || Chartist.stripUnit(options.height) || 0;\n    var normalizedPadding = Chartist.normalizePadding(options.chartPadding, fallbackPadding);\n\n    // If settings were to small to cope with offset (legacy) and padding, we'll adjust\n    width = Math.max(width, xAxisOffset + normalizedPadding.left + normalizedPadding.right);\n    height = Math.max(height, yAxisOffset + normalizedPadding.top + normalizedPadding.bottom);\n\n    var chartRect = {\n      padding: normalizedPadding,\n      width: function () {\n        return this.x2 - this.x1;\n      },\n      height: function () {\n        return this.y1 - this.y2;\n      }\n    };\n\n    if(hasAxis) {\n      if (options.axisX.position === 'start') {\n        chartRect.y2 = normalizedPadding.top + xAxisOffset;\n        chartRect.y1 = Math.max(height - normalizedPadding.bottom, chartRect.y2 + 1);\n      } else {\n        chartRect.y2 = normalizedPadding.top;\n        chartRect.y1 = Math.max(height - normalizedPadding.bottom - xAxisOffset, chartRect.y2 + 1);\n      }\n\n      if (options.axisY.position === 'start') {\n        chartRect.x1 = normalizedPadding.left + yAxisOffset;\n        chartRect.x2 = Math.max(width - normalizedPadding.right, chartRect.x1 + 1);\n      } else {\n        chartRect.x1 = normalizedPadding.left;\n        chartRect.x2 = Math.max(width - normalizedPadding.right - yAxisOffset, chartRect.x1 + 1);\n      }\n    } else {\n      chartRect.x1 = normalizedPadding.left;\n      chartRect.x2 = Math.max(width - normalizedPadding.right, chartRect.x1 + 1);\n      chartRect.y2 = normalizedPadding.top;\n      chartRect.y1 = Math.max(height - normalizedPadding.bottom, chartRect.y2 + 1);\n    }\n\n    return chartRect;\n  };\n\n  /**\n   * Creates a grid line based on a projected value.\n   *\n   * @memberof Chartist.Core\n   * @param projectedValue\n   * @param index\n   * @param axis\n   * @param offset\n   * @param length\n   * @param group\n   * @param classes\n   * @param eventEmitter\n   */\n  Chartist.createGrid = function(projectedValue, index, axis, offset, length, group, classes, eventEmitter) {\n    var positionalData = {};\n    positionalData[axis.units.pos + '1'] = projectedValue.pos;\n    positionalData[axis.units.pos + '2'] = projectedValue.pos;\n    positionalData[axis.counterUnits.pos + '1'] = offset;\n    positionalData[axis.counterUnits.pos + '2'] = offset + length;\n\n    var gridElement = group.elem('line', positionalData, classes.join(' '));\n\n    // Event for grid draw\n    eventEmitter.emit('draw',\n      Chartist.extend({\n        type: 'grid',\n        axis: axis,\n        index: index,\n        group: group,\n        element: gridElement\n      }, positionalData)\n    );\n  };\n\n  /**\n   * Creates a label based on a projected value and an axis.\n   *\n   * @memberof Chartist.Core\n   * @param projectedValue\n   * @param index\n   * @param labels\n   * @param axis\n   * @param axisOffset\n   * @param labelOffset\n   * @param group\n   * @param classes\n   * @param useForeignObject\n   * @param eventEmitter\n   */\n  Chartist.createLabel = function(projectedValue, index, labels, axis, axisOffset, labelOffset, group, classes, useForeignObject, eventEmitter) {\n    var labelElement;\n    var positionalData = {};\n\n    positionalData[axis.units.pos] = projectedValue.pos + labelOffset[axis.units.pos];\n    positionalData[axis.counterUnits.pos] = labelOffset[axis.counterUnits.pos];\n    positionalData[axis.units.len] = projectedValue.len;\n    positionalData[axis.counterUnits.len] = axisOffset - 10;\n\n    if(useForeignObject) {\n      // We need to set width and height explicitly to px as span will not expand with width and height being\n      // 100% in all browsers\n      var content = '<span class=\"' + classes.join(' ') + '\" style=\"' +\n        axis.units.len + ': ' + Math.round(positionalData[axis.units.len]) + 'px; ' +\n        axis.counterUnits.len + ': ' + Math.round(positionalData[axis.counterUnits.len]) + 'px\">' +\n        labels[index] + '</span>';\n\n      labelElement = group.foreignObject(content, Chartist.extend({\n        style: 'overflow: visible;'\n      }, positionalData));\n    } else {\n      labelElement = group.elem('text', positionalData, classes.join(' ')).text(labels[index]);\n    }\n\n    eventEmitter.emit('draw', Chartist.extend({\n      type: 'label',\n      axis: axis,\n      index: index,\n      group: group,\n      element: labelElement,\n      text: labels[index]\n    }, positionalData));\n  };\n\n  /**\n   * This function creates a whole axis with its grid lines and labels based on an axis model and a chartRect.\n   *\n   * @memberof Chartist.Core\n   * @param axis\n   * @param data\n   * @param chartRect\n   * @param gridGroup\n   * @param labelGroup\n   * @param useForeignObject\n   * @param options\n   * @param eventEmitter\n   */\n  Chartist.createAxis = function(axis, data, chartRect, gridGroup, labelGroup, useForeignObject, options, eventEmitter) {\n    var axisOptions = options['axis' + axis.units.pos.toUpperCase()];\n    var projectedValues = data.map(axis.projectValue.bind(axis));\n    var labelValues = data.map(axisOptions.labelInterpolationFnc);\n\n    projectedValues.forEach(function(projectedValue, index) {\n      var labelOffset = {\n        x: 0,\n        y: 0\n      };\n\n      // Skip grid lines and labels where interpolated label values are falsey (execpt for 0)\n      if(!labelValues[index] && labelValues[index] !== 0) {\n        return;\n      }\n\n      // Transform to global coordinates using the chartRect\n      // We also need to set the label offset for the createLabel function\n      if(axis.units.pos === 'x') {\n        projectedValue.pos = chartRect.x1 + projectedValue.pos;\n        labelOffset.x = options.axisX.labelOffset.x;\n\n        // If the labels should be positioned in start position (top side for vertical axis) we need to set a\n        // different offset as for positioned with end (bottom)\n        if(options.axisX.position === 'start') {\n          labelOffset.y = chartRect.padding.top + options.axisX.labelOffset.y + (useForeignObject ? 5 : 20);\n        } else {\n          labelOffset.y = chartRect.y1 + options.axisX.labelOffset.y + (useForeignObject ? 5 : 20);\n        }\n      } else {\n        projectedValue.pos = chartRect.y1 - projectedValue.pos;\n        labelOffset.y = options.axisY.labelOffset.y - (useForeignObject ? projectedValue.len : 0);\n\n        // If the labels should be positioned in start position (left side for horizontal axis) we need to set a\n        // different offset as for positioned with end (right side)\n        if(options.axisY.position === 'start') {\n          labelOffset.x = useForeignObject ? chartRect.padding.left + options.axisY.labelOffset.x : chartRect.x1 - 10;\n        } else {\n          labelOffset.x = chartRect.x2 + options.axisY.labelOffset.x + 10;\n        }\n      }\n\n      if(axisOptions.showGrid) {\n        Chartist.createGrid(projectedValue, index, axis, axis.gridOffset, chartRect[axis.counterUnits.len](), gridGroup, [\n          options.classNames.grid,\n          options.classNames[axis.units.dir]\n        ], eventEmitter);\n      }\n\n      if(axisOptions.showLabel) {\n        Chartist.createLabel(projectedValue, index, labelValues, axis, axisOptions.offset, labelOffset, labelGroup, [\n          options.classNames.label,\n          options.classNames[axis.units.dir],\n          options.classNames[axisOptions.position]\n        ], useForeignObject, eventEmitter);\n      }\n    });\n  };\n\n  /**\n   * Helper to read series specific options from options object. It automatically falls back to the global option if\n   * there is no option in the series options.\n   *\n   * @param {Object} series Series object\n   * @param {Object} options Chartist options object\n   * @param {string} key The options key that should be used to obtain the options\n   * @returns {*}\n   */\n  Chartist.getSeriesOption = function(series, options, key) {\n    if(series.name && options.series && options.series[series.name]) {\n      var seriesOptions = options.series[series.name];\n      return seriesOptions.hasOwnProperty(key) ? seriesOptions[key] : options[key];\n    } else {\n      return options[key];\n    }\n  };\n\n  /**\n   * Provides options handling functionality with callback for options changes triggered by responsive options and media query matches\n   *\n   * @memberof Chartist.Core\n   * @param {Object} options Options set by user\n   * @param {Array} responsiveOptions Optional functions to add responsive behavior to chart\n   * @param {Object} eventEmitter The event emitter that will be used to emit the options changed events\n   * @return {Object} The consolidated options object from the defaults, base and matching responsive options\n   */\n  Chartist.optionsProvider = function (options, responsiveOptions, eventEmitter) {\n    var baseOptions = Chartist.extend({}, options),\n      currentOptions,\n      mediaQueryListeners = [],\n      i;\n\n    function updateCurrentOptions(preventChangedEvent) {\n      var previousOptions = currentOptions;\n      currentOptions = Chartist.extend({}, baseOptions);\n\n      if (responsiveOptions) {\n        for (i = 0; i < responsiveOptions.length; i++) {\n          var mql = window.matchMedia(responsiveOptions[i][0]);\n          if (mql.matches) {\n            currentOptions = Chartist.extend(currentOptions, responsiveOptions[i][1]);\n          }\n        }\n      }\n\n      if(eventEmitter && !preventChangedEvent) {\n        eventEmitter.emit('optionsChanged', {\n          previousOptions: previousOptions,\n          currentOptions: currentOptions\n        });\n      }\n    }\n\n    function removeMediaQueryListeners() {\n      mediaQueryListeners.forEach(function(mql) {\n        mql.removeListener(updateCurrentOptions);\n      });\n    }\n\n    if (!window.matchMedia) {\n      throw 'window.matchMedia not found! Make sure you\\'re using a polyfill.';\n    } else if (responsiveOptions) {\n\n      for (i = 0; i < responsiveOptions.length; i++) {\n        var mql = window.matchMedia(responsiveOptions[i][0]);\n        mql.addListener(updateCurrentOptions);\n        mediaQueryListeners.push(mql);\n      }\n    }\n    // Execute initially so we get the correct options\n    updateCurrentOptions(true);\n\n    return {\n      removeMediaQueryListeners: removeMediaQueryListeners,\n      getCurrentOptions: function getCurrentOptions() {\n        return Chartist.extend({}, currentOptions);\n      }\n    };\n  };\n\n}(window, document, Chartist));\n;/**\n * Chartist path interpolation functions.\n *\n * @module Chartist.Interpolation\n */\n/* global Chartist */\n(function(window, document, Chartist) {\n  'use strict';\n\n  Chartist.Interpolation = {};\n\n  /**\n   * This interpolation function does not smooth the path and the result is only containing lines and no curves.\n   *\n   * @memberof Chartist.Interpolation\n   * @return {Function}\n   */\n  Chartist.Interpolation.none = function() {\n    return function none(pathCoordinates, valueData) {\n      var path = new Chartist.Svg.Path();\n      // We need to assume that the first value is a \"hole\"\n      var hole = true;\n\n      for(var i = 1; i < pathCoordinates.length; i += 2) {\n        var data = valueData[(i - 1) / 2];\n\n        // If the current value is undefined we should treat it as a hole start\n        if(data.value === undefined) {\n          hole = true;\n        } else {\n          // If this value is valid we need to check if we're coming out of a hole\n          if(hole) {\n            // If we are coming out of a hole we should first make a move and also reset the hole flag\n            path.move(pathCoordinates[i - 1], pathCoordinates[i], false, data);\n            hole = false;\n          } else {\n            path.line(pathCoordinates[i - 1], pathCoordinates[i], false, data);\n          }\n        }\n      }\n\n      return path;\n    };\n  };\n\n  /**\n   * Simple smoothing creates horizontal handles that are positioned with a fraction of the length between two data points. You can use the divisor option to specify the amount of smoothing.\n   *\n   * Simple smoothing can be used instead of `Chartist.Smoothing.cardinal` if you'd like to get rid of the artifacts it produces sometimes. Simple smoothing produces less flowing lines but is accurate by hitting the points and it also doesn't swing below or above the given data point.\n   *\n   * All smoothing functions within Chartist are factory functions that accept an options parameter. The simple interpolation function accepts one configuration parameter `divisor`, between 1 and ∞, which controls the smoothing characteristics.\n   *\n   * @example\n   * var chart = new Chartist.Line('.ct-chart', {\n   *   labels: [1, 2, 3, 4, 5],\n   *   series: [[1, 2, 8, 1, 7]]\n   * }, {\n   *   lineSmooth: Chartist.Interpolation.simple({\n   *     divisor: 2\n   *   })\n   * });\n   *\n   *\n   * @memberof Chartist.Interpolation\n   * @param {Object} options The options of the simple interpolation factory function.\n   * @return {Function}\n   */\n  Chartist.Interpolation.simple = function(options) {\n    var defaultOptions = {\n      divisor: 2\n    };\n    options = Chartist.extend({}, defaultOptions, options);\n\n    var d = 1 / Math.max(1, options.divisor);\n\n    return function simple(pathCoordinates, valueData) {\n      var path = new Chartist.Svg.Path();\n      var hole = true;\n\n      for(var i = 2; i < pathCoordinates.length; i += 2) {\n        var prevX = pathCoordinates[i - 2];\n        var prevY = pathCoordinates[i - 1];\n        var currX = pathCoordinates[i];\n        var currY = pathCoordinates[i + 1];\n        var length = (currX - prevX) * d;\n        var prevData = valueData[(i / 2) - 1];\n        var currData = valueData[i / 2];\n\n        if(prevData.value === undefined) {\n          hole = true;\n        } else {\n\n          if(hole) {\n            path.move(prevX, prevY, false, prevData);\n          }\n\n          if(currData.value !== undefined) {\n            path.curve(\n              prevX + length,\n              prevY,\n              currX - length,\n              currY,\n              currX,\n              currY,\n              false,\n              currData\n            );\n\n            hole = false;\n          }\n        }\n      }\n\n      return path;\n    };\n  };\n\n  /**\n   * Cardinal / Catmull-Rome spline interpolation is the default smoothing function in Chartist. It produces nice results where the splines will always meet the points. It produces some artifacts though when data values are increased or decreased rapidly. The line may not follow a very accurate path and if the line should be accurate this smoothing function does not produce the best results.\n   *\n   * Cardinal splines can only be created if there are more than two data points. If this is not the case this smoothing will fallback to `Chartist.Smoothing.none`.\n   *\n   * All smoothing functions within Chartist are factory functions that accept an options parameter. The cardinal interpolation function accepts one configuration parameter `tension`, between 0 and 1, which controls the smoothing intensity.\n   *\n   * @example\n   * var chart = new Chartist.Line('.ct-chart', {\n   *   labels: [1, 2, 3, 4, 5],\n   *   series: [[1, 2, 8, 1, 7]]\n   * }, {\n   *   lineSmooth: Chartist.Interpolation.cardinal({\n   *     tension: 1\n   *   })\n   * });\n   *\n   * @memberof Chartist.Interpolation\n   * @param {Object} options The options of the cardinal factory function.\n   * @return {Function}\n   */\n  Chartist.Interpolation.cardinal = function(options) {\n    var defaultOptions = {\n      tension: 1\n    };\n\n    options = Chartist.extend({}, defaultOptions, options);\n\n    var t = Math.min(1, Math.max(0, options.tension)),\n      c = 1 - t;\n\n    // This function will help us to split pathCoordinates and valueData into segments that also contain pathCoordinates\n    // and valueData. This way the existing functions can be reused and the segment paths can be joined afterwards.\n    // This functionality is necessary to treat \"holes\" in the line charts\n    function splitIntoSegments(pathCoordinates, valueData) {\n      var segments = [];\n      var hole = true;\n\n      for(var i = 0; i < pathCoordinates.length; i += 2) {\n        // If this value is a \"hole\" we set the hole flag\n        if(valueData[i / 2].value === undefined) {\n          hole = true;\n        } else {\n          // If it's a valid value we need to check if we're coming out of a hole and create a new empty segment\n          if(hole) {\n            segments.push({\n              pathCoordinates: [],\n              valueData: []\n            });\n            // As we have a valid value now, we are not in a \"hole\" anymore\n            hole = false;\n          }\n\n          // Add to the segment pathCoordinates and valueData\n          segments[segments.length - 1].pathCoordinates.push(pathCoordinates[i], pathCoordinates[i + 1]);\n          segments[segments.length - 1].valueData.push(valueData[i / 2]);\n        }\n      }\n\n      return segments;\n    }\n\n    return function cardinal(pathCoordinates, valueData) {\n      // First we try to split the coordinates into segments\n      // This is necessary to treat \"holes\" in line charts\n      var segments = splitIntoSegments(pathCoordinates, valueData);\n\n      // If the split resulted in more that one segment we need to interpolate each segment individually and join them\n      // afterwards together into a single path.\n      if(segments.length > 1) {\n        var paths = [];\n        // For each segment we will recurse the cardinal function\n        segments.forEach(function(segment) {\n          paths.push(cardinal(segment.pathCoordinates, segment.valueData));\n        });\n        // Join the segment path data into a single path and return\n        return Chartist.Svg.Path.join(paths);\n      } else {\n        // If there was only one segment we can proceed regularly by using pathCoordinates and valueData from the first\n        // segment\n        pathCoordinates = segments[0].pathCoordinates;\n        valueData = segments[0].valueData;\n\n        // If less than two points we need to fallback to no smoothing\n        if(pathCoordinates.length <= 4) {\n          return Chartist.Interpolation.none()(pathCoordinates, valueData);\n        }\n\n        var path = new Chartist.Svg.Path().move(pathCoordinates[0], pathCoordinates[1], false, valueData[0]),\n          z;\n\n        for (var i = 0, iLen = pathCoordinates.length; iLen - 2 * !z > i; i += 2) {\n          var p = [\n            {x: +pathCoordinates[i - 2], y: +pathCoordinates[i - 1]},\n            {x: +pathCoordinates[i], y: +pathCoordinates[i + 1]},\n            {x: +pathCoordinates[i + 2], y: +pathCoordinates[i + 3]},\n            {x: +pathCoordinates[i + 4], y: +pathCoordinates[i + 5]}\n          ];\n          if (z) {\n            if (!i) {\n              p[0] = {x: +pathCoordinates[iLen - 2], y: +pathCoordinates[iLen - 1]};\n            } else if (iLen - 4 === i) {\n              p[3] = {x: +pathCoordinates[0], y: +pathCoordinates[1]};\n            } else if (iLen - 2 === i) {\n              p[2] = {x: +pathCoordinates[0], y: +pathCoordinates[1]};\n              p[3] = {x: +pathCoordinates[2], y: +pathCoordinates[3]};\n            }\n          } else {\n            if (iLen - 4 === i) {\n              p[3] = p[2];\n            } else if (!i) {\n              p[0] = {x: +pathCoordinates[i], y: +pathCoordinates[i + 1]};\n            }\n          }\n\n          path.curve(\n            (t * (-p[0].x + 6 * p[1].x + p[2].x) / 6) + (c * p[2].x),\n            (t * (-p[0].y + 6 * p[1].y + p[2].y) / 6) + (c * p[2].y),\n            (t * (p[1].x + 6 * p[2].x - p[3].x) / 6) + (c * p[2].x),\n            (t * (p[1].y + 6 * p[2].y - p[3].y) / 6) + (c * p[2].y),\n            p[2].x,\n            p[2].y,\n            false,\n            valueData[(i + 2) / 2]\n          );\n        }\n\n        return path;\n      }\n    };\n  };\n\n  /**\n   * Step interpolation will cause the line chart to move in steps rather than diagonal or smoothed lines. This interpolation will create additional points that will also be drawn when the `showPoint` option is enabled.\n   *\n   * All smoothing functions within Chartist are factory functions that accept an options parameter. The step interpolation function accepts one configuration parameter `postpone`, that can be `true` or `false`. The default value is `true` and will cause the step to occur where the value actually changes. If a different behaviour is needed where the step is shifted to the left and happens before the actual value, this option can be set to `false`.\n   *\n   * @example\n   * var chart = new Chartist.Line('.ct-chart', {\n   *   labels: [1, 2, 3, 4, 5],\n   *   series: [[1, 2, 8, 1, 7]]\n   * }, {\n   *   lineSmooth: Chartist.Interpolation.step({\n   *     postpone: true\n   *   })\n   * });\n   *\n   * @memberof Chartist.Interpolation\n   * @param options\n   * @returns {Function}\n   */\n  Chartist.Interpolation.step = function(options) {\n    var defaultOptions = {\n      postpone: true\n    };\n\n    options = Chartist.extend({}, defaultOptions, options);\n\n    return function step(pathCoordinates, valueData) {\n      var path = new Chartist.Svg.Path();\n      var hole = true;\n\n      for (var i = 2; i < pathCoordinates.length; i += 2) {\n        var prevX = pathCoordinates[i - 2];\n        var prevY = pathCoordinates[i - 1];\n        var currX = pathCoordinates[i];\n        var currY = pathCoordinates[i + 1];\n        var prevData = valueData[(i / 2) - 1];\n        var currData = valueData[i / 2];\n\n        // If last point is a \"hole\"\n        if(prevData.value === undefined) {\n          hole = true;\n        } else {\n          // If last point is not a \"hole\" but we just came back out of a \"hole\" we need to move first\n          if(hole) {\n            path.move(prevX, prevY, false, prevData);\n          }\n\n          // If the current point is also not a hole we can draw the step lines\n          if(currData.value !== undefined) {\n            if(options.postpone) {\n              // If postponed we should draw the step line with the value of the previous value\n              path.line(currX, prevY, false, prevData);\n            } else {\n              // If not postponed we should draw the step line with the value of the current value\n              path.line(prevX, currY, false, currData);\n            }\n            // Line to the actual point (this should only be a Y-Axis movement\n            path.line(currX, currY, false, currData);\n            // Reset the \"hole\" flag as previous and current point have valid values\n            hole = false;\n          }\n        }\n      }\n\n      return path;\n    };\n  };\n\n}(window, document, Chartist));\n;/**\n * A very basic event module that helps to generate and catch events.\n *\n * @module Chartist.Event\n */\n/* global Chartist */\n(function (window, document, Chartist) {\n  'use strict';\n\n  Chartist.EventEmitter = function () {\n    var handlers = [];\n\n    /**\n     * Add an event handler for a specific event\n     *\n     * @memberof Chartist.Event\n     * @param {String} event The event name\n     * @param {Function} handler A event handler function\n     */\n    function addEventHandler(event, handler) {\n      handlers[event] = handlers[event] || [];\n      handlers[event].push(handler);\n    }\n\n    /**\n     * Remove an event handler of a specific event name or remove all event handlers for a specific event.\n     *\n     * @memberof Chartist.Event\n     * @param {String} event The event name where a specific or all handlers should be removed\n     * @param {Function} [handler] An optional event handler function. If specified only this specific handler will be removed and otherwise all handlers are removed.\n     */\n    function removeEventHandler(event, handler) {\n      // Only do something if there are event handlers with this name existing\n      if(handlers[event]) {\n        // If handler is set we will look for a specific handler and only remove this\n        if(handler) {\n          handlers[event].splice(handlers[event].indexOf(handler), 1);\n          if(handlers[event].length === 0) {\n            delete handlers[event];\n          }\n        } else {\n          // If no handler is specified we remove all handlers for this event\n          delete handlers[event];\n        }\n      }\n    }\n\n    /**\n     * Use this function to emit an event. All handlers that are listening for this event will be triggered with the data parameter.\n     *\n     * @memberof Chartist.Event\n     * @param {String} event The event name that should be triggered\n     * @param {*} data Arbitrary data that will be passed to the event handler callback functions\n     */\n    function emit(event, data) {\n      // Only do something if there are event handlers with this name existing\n      if(handlers[event]) {\n        handlers[event].forEach(function(handler) {\n          handler(data);\n        });\n      }\n\n      // Emit event to star event handlers\n      if(handlers['*']) {\n        handlers['*'].forEach(function(starHandler) {\n          starHandler(event, data);\n        });\n      }\n    }\n\n    return {\n      addEventHandler: addEventHandler,\n      removeEventHandler: removeEventHandler,\n      emit: emit\n    };\n  };\n\n}(window, document, Chartist));\n;/**\n * This module provides some basic prototype inheritance utilities.\n *\n * @module Chartist.Class\n */\n/* global Chartist */\n(function(window, document, Chartist) {\n  'use strict';\n\n  function listToArray(list) {\n    var arr = [];\n    if (list.length) {\n      for (var i = 0; i < list.length; i++) {\n        arr.push(list[i]);\n      }\n    }\n    return arr;\n  }\n\n  /**\n   * Method to extend from current prototype.\n   *\n   * @memberof Chartist.Class\n   * @param {Object} properties The object that serves as definition for the prototype that gets created for the new class. This object should always contain a constructor property that is the desired constructor for the newly created class.\n   * @param {Object} [superProtoOverride] By default extens will use the current class prototype or Chartist.class. With this parameter you can specify any super prototype that will be used.\n   * @return {Function} Constructor function of the new class\n   *\n   * @example\n   * var Fruit = Class.extend({\n     * color: undefined,\n     *   sugar: undefined,\n     *\n     *   constructor: function(color, sugar) {\n     *     this.color = color;\n     *     this.sugar = sugar;\n     *   },\n     *\n     *   eat: function() {\n     *     this.sugar = 0;\n     *     return this;\n     *   }\n     * });\n   *\n   * var Banana = Fruit.extend({\n     *   length: undefined,\n     *\n     *   constructor: function(length, sugar) {\n     *     Banana.super.constructor.call(this, 'Yellow', sugar);\n     *     this.length = length;\n     *   }\n     * });\n   *\n   * var banana = new Banana(20, 40);\n   * console.log('banana instanceof Fruit', banana instanceof Fruit);\n   * console.log('Fruit is prototype of banana', Fruit.prototype.isPrototypeOf(banana));\n   * console.log('bananas prototype is Fruit', Object.getPrototypeOf(banana) === Fruit.prototype);\n   * console.log(banana.sugar);\n   * console.log(banana.eat().sugar);\n   * console.log(banana.color);\n   */\n  function extend(properties, superProtoOverride) {\n    var superProto = superProtoOverride || this.prototype || Chartist.Class;\n    var proto = Object.create(superProto);\n\n    Chartist.Class.cloneDefinitions(proto, properties);\n\n    var constr = function() {\n      var fn = proto.constructor || function () {},\n        instance;\n\n      // If this is linked to the Chartist namespace the constructor was not called with new\n      // To provide a fallback we will instantiate here and return the instance\n      instance = this === Chartist ? Object.create(proto) : this;\n      fn.apply(instance, Array.prototype.slice.call(arguments, 0));\n\n      // If this constructor was not called with new we need to return the instance\n      // This will not harm when the constructor has been called with new as the returned value is ignored\n      return instance;\n    };\n\n    constr.prototype = proto;\n    constr.super = superProto;\n    constr.extend = this.extend;\n\n    return constr;\n  }\n\n  // Variable argument list clones args > 0 into args[0] and retruns modified args[0]\n  function cloneDefinitions() {\n    var args = listToArray(arguments);\n    var target = args[0];\n\n    args.splice(1, args.length - 1).forEach(function (source) {\n      Object.getOwnPropertyNames(source).forEach(function (propName) {\n        // If this property already exist in target we delete it first\n        delete target[propName];\n        // Define the property with the descriptor from source\n        Object.defineProperty(target, propName,\n          Object.getOwnPropertyDescriptor(source, propName));\n      });\n    });\n\n    return target;\n  }\n\n  Chartist.Class = {\n    extend: extend,\n    cloneDefinitions: cloneDefinitions\n  };\n\n}(window, document, Chartist));\n;/**\n * Base for all chart types. The methods in Chartist.Base are inherited to all chart types.\n *\n * @module Chartist.Base\n */\n/* global Chartist */\n(function(window, document, Chartist) {\n  'use strict';\n\n  // TODO: Currently we need to re-draw the chart on window resize. This is usually very bad and will affect performance.\n  // This is done because we can't work with relative coordinates when drawing the chart because SVG Path does not\n  // work with relative positions yet. We need to check if we can do a viewBox hack to switch to percentage.\n  // See http://mozilla.6506.n7.nabble.com/Specyfing-paths-with-percentages-unit-td247474.html\n  // Update: can be done using the above method tested here: http://codepen.io/gionkunz/pen/KDvLj\n  // The problem is with the label offsets that can't be converted into percentage and affecting the chart container\n  /**\n   * Updates the chart which currently does a full reconstruction of the SVG DOM\n   *\n   * @param {Object} [data] Optional data you'd like to set for the chart before it will update. If not specified the update method will use the data that is already configured with the chart.\n   * @param {Object} [options] Optional options you'd like to add to the previous options for the chart before it will update. If not specified the update method will use the options that have been already configured with the chart.\n   * @param {Boolean} [override] If set to true, the passed options will be used to extend the options that have been configured already. Otherwise the chart default options will be used as the base\n   * @memberof Chartist.Base\n   */\n  function update(data, options, override) {\n    if(data) {\n      this.data = data;\n      // Event for data transformation that allows to manipulate the data before it gets rendered in the charts\n      this.eventEmitter.emit('data', {\n        type: 'update',\n        data: this.data\n      });\n    }\n\n    if(options) {\n      this.options = Chartist.extend({}, override ? this.options : this.defaultOptions, options);\n\n      // If chartist was not initialized yet, we just set the options and leave the rest to the initialization\n      // Otherwise we re-create the optionsProvider at this point\n      if(!this.initializeTimeoutId) {\n        this.optionsProvider.removeMediaQueryListeners();\n        this.optionsProvider = Chartist.optionsProvider(this.options, this.responsiveOptions, this.eventEmitter);\n      }\n    }\n\n    // Only re-created the chart if it has been initialized yet\n    if(!this.initializeTimeoutId) {\n      this.createChart(this.optionsProvider.getCurrentOptions());\n    }\n\n    // Return a reference to the chart object to chain up calls\n    return this;\n  }\n\n  /**\n   * This method can be called on the API object of each chart and will un-register all event listeners that were added to other components. This currently includes a window.resize listener as well as media query listeners if any responsive options have been provided. Use this function if you need to destroy and recreate Chartist charts dynamically.\n   *\n   * @memberof Chartist.Base\n   */\n  function detach() {\n    // Only detach if initialization already occurred on this chart. If this chart still hasn't initialized (therefore\n    // the initializationTimeoutId is still a valid timeout reference, we will clear the timeout\n    if(!this.initializeTimeoutId) {\n      window.removeEventListener('resize', this.resizeListener);\n      this.optionsProvider.removeMediaQueryListeners();\n    } else {\n      window.clearTimeout(this.initializeTimeoutId);\n    }\n\n    return this;\n  }\n\n  /**\n   * Use this function to register event handlers. The handler callbacks are synchronous and will run in the main thread rather than the event loop.\n   *\n   * @memberof Chartist.Base\n   * @param {String} event Name of the event. Check the examples for supported events.\n   * @param {Function} handler The handler function that will be called when an event with the given name was emitted. This function will receive a data argument which contains event data. See the example for more details.\n   */\n  function on(event, handler) {\n    this.eventEmitter.addEventHandler(event, handler);\n    return this;\n  }\n\n  /**\n   * Use this function to un-register event handlers. If the handler function parameter is omitted all handlers for the given event will be un-registered.\n   *\n   * @memberof Chartist.Base\n   * @param {String} event Name of the event for which a handler should be removed\n   * @param {Function} [handler] The handler function that that was previously used to register a new event handler. This handler will be removed from the event handler list. If this parameter is omitted then all event handlers for the given event are removed from the list.\n   */\n  function off(event, handler) {\n    this.eventEmitter.removeEventHandler(event, handler);\n    return this;\n  }\n\n  function initialize() {\n    // Add window resize listener that re-creates the chart\n    window.addEventListener('resize', this.resizeListener);\n\n    // Obtain current options based on matching media queries (if responsive options are given)\n    // This will also register a listener that is re-creating the chart based on media changes\n    this.optionsProvider = Chartist.optionsProvider(this.options, this.responsiveOptions, this.eventEmitter);\n    // Register options change listener that will trigger a chart update\n    this.eventEmitter.addEventHandler('optionsChanged', function() {\n      this.update();\n    }.bind(this));\n\n    // Before the first chart creation we need to register us with all plugins that are configured\n    // Initialize all relevant plugins with our chart object and the plugin options specified in the config\n    if(this.options.plugins) {\n      this.options.plugins.forEach(function(plugin) {\n        if(plugin instanceof Array) {\n          plugin[0](this, plugin[1]);\n        } else {\n          plugin(this);\n        }\n      }.bind(this));\n    }\n\n    // Event for data transformation that allows to manipulate the data before it gets rendered in the charts\n    this.eventEmitter.emit('data', {\n      type: 'initial',\n      data: this.data\n    });\n\n    // Create the first chart\n    this.createChart(this.optionsProvider.getCurrentOptions());\n\n    // As chart is initialized from the event loop now we can reset our timeout reference\n    // This is important if the chart gets initialized on the same element twice\n    this.initializeTimeoutId = undefined;\n  }\n\n  /**\n   * Constructor of chart base class.\n   *\n   * @param query\n   * @param data\n   * @param defaultOptions\n   * @param options\n   * @param responsiveOptions\n   * @constructor\n   */\n  function Base(query, data, defaultOptions, options, responsiveOptions) {\n    this.container = Chartist.querySelector(query);\n    this.data = data;\n    this.defaultOptions = defaultOptions;\n    this.options = options;\n    this.responsiveOptions = responsiveOptions;\n    this.eventEmitter = Chartist.EventEmitter();\n    this.supportsForeignObject = Chartist.Svg.isSupported('Extensibility');\n    this.supportsAnimations = Chartist.Svg.isSupported('AnimationEventsAttribute');\n    this.resizeListener = function resizeListener(){\n      this.update();\n    }.bind(this);\n\n    if(this.container) {\n      // If chartist was already initialized in this container we are detaching all event listeners first\n      if(this.container.__chartist__) {\n        this.container.__chartist__.detach();\n      }\n\n      this.container.__chartist__ = this;\n    }\n\n    // Using event loop for first draw to make it possible to register event listeners in the same call stack where\n    // the chart was created.\n    this.initializeTimeoutId = setTimeout(initialize.bind(this), 0);\n  }\n\n  // Creating the chart base class\n  Chartist.Base = Chartist.Class.extend({\n    constructor: Base,\n    optionsProvider: undefined,\n    container: undefined,\n    svg: undefined,\n    eventEmitter: undefined,\n    createChart: function() {\n      throw new Error('Base chart type can\\'t be instantiated!');\n    },\n    update: update,\n    detach: detach,\n    on: on,\n    off: off,\n    version: Chartist.version,\n    supportsForeignObject: false\n  });\n\n}(window, document, Chartist));\n;/**\n * Chartist SVG module for simple SVG DOM abstraction\n *\n * @module Chartist.Svg\n */\n/* global Chartist */\n(function(window, document, Chartist) {\n  'use strict';\n\n  var svgNs = 'http://www.w3.org/2000/svg',\n    xmlNs = 'http://www.w3.org/2000/xmlns/',\n    xhtmlNs = 'http://www.w3.org/1999/xhtml';\n\n  Chartist.xmlNs = {\n    qualifiedName: 'xmlns:ct',\n    prefix: 'ct',\n    uri: 'http://gionkunz.github.com/chartist-js/ct'\n  };\n\n  /**\n   * Chartist.Svg creates a new SVG object wrapper with a starting element. You can use the wrapper to fluently create sub-elements and modify them.\n   *\n   * @memberof Chartist.Svg\n   * @constructor\n   * @param {String|Element} name The name of the SVG element to create or an SVG dom element which should be wrapped into Chartist.Svg\n   * @param {Object} attributes An object with properties that will be added as attributes to the SVG element that is created. Attributes with undefined values will not be added.\n   * @param {String} className This class or class list will be added to the SVG element\n   * @param {Object} parent The parent SVG wrapper object where this newly created wrapper and it's element will be attached to as child\n   * @param {Boolean} insertFirst If this param is set to true in conjunction with a parent element the newly created element will be added as first child element in the parent element\n   */\n  function Svg(name, attributes, className, parent, insertFirst) {\n    // If Svg is getting called with an SVG element we just return the wrapper\n    if(name instanceof Element) {\n      this._node = name;\n    } else {\n      this._node = document.createElementNS(svgNs, name);\n\n      // If this is an SVG element created then custom namespace\n      if(name === 'svg') {\n        this._node.setAttributeNS(xmlNs, Chartist.xmlNs.qualifiedName, Chartist.xmlNs.uri);\n      }\n\n      if(attributes) {\n        this.attr(attributes);\n      }\n\n      if(className) {\n        this.addClass(className);\n      }\n\n      if(parent) {\n        if (insertFirst && parent._node.firstChild) {\n          parent._node.insertBefore(this._node, parent._node.firstChild);\n        } else {\n          parent._node.appendChild(this._node);\n        }\n      }\n    }\n  }\n\n  /**\n   * Set attributes on the current SVG element of the wrapper you're currently working on.\n   *\n   * @memberof Chartist.Svg\n   * @param {Object|String} attributes An object with properties that will be added as attributes to the SVG element that is created. Attributes with undefined values will not be added. If this parameter is a String then the function is used as a getter and will return the attribute value.\n   * @param {String} ns If specified, the attributes will be set as namespace attributes with ns as prefix.\n   * @return {Object|String} The current wrapper object will be returned so it can be used for chaining or the attribute value if used as getter function.\n   */\n  function attr(attributes, ns) {\n    if(typeof attributes === 'string') {\n      if(ns) {\n        return this._node.getAttributeNS(ns, attributes);\n      } else {\n        return this._node.getAttribute(attributes);\n      }\n    }\n\n    Object.keys(attributes).forEach(function(key) {\n      // If the attribute value is undefined we can skip this one\n      if(attributes[key] === undefined) {\n        return;\n      }\n\n      if(ns) {\n        this._node.setAttributeNS(ns, [Chartist.xmlNs.prefix, ':', key].join(''), attributes[key]);\n      } else {\n        this._node.setAttribute(key, attributes[key]);\n      }\n    }.bind(this));\n\n    return this;\n  }\n\n  /**\n   * Create a new SVG element whose wrapper object will be selected for further operations. This way you can also create nested groups easily.\n   *\n   * @memberof Chartist.Svg\n   * @param {String} name The name of the SVG element that should be created as child element of the currently selected element wrapper\n   * @param {Object} [attributes] An object with properties that will be added as attributes to the SVG element that is created. Attributes with undefined values will not be added.\n   * @param {String} [className] This class or class list will be added to the SVG element\n   * @param {Boolean} [insertFirst] If this param is set to true in conjunction with a parent element the newly created element will be added as first child element in the parent element\n   * @return {Chartist.Svg} Returns a Chartist.Svg wrapper object that can be used to modify the containing SVG data\n   */\n  function elem(name, attributes, className, insertFirst) {\n    return new Chartist.Svg(name, attributes, className, this, insertFirst);\n  }\n\n  /**\n   * Returns the parent Chartist.SVG wrapper object\n   *\n   * @memberof Chartist.Svg\n   * @return {Chartist.Svg} Returns a Chartist.Svg wrapper around the parent node of the current node. If the parent node is not existing or it's not an SVG node then this function will return null.\n   */\n  function parent() {\n    return this._node.parentNode instanceof SVGElement ? new Chartist.Svg(this._node.parentNode) : null;\n  }\n\n  /**\n   * This method returns a Chartist.Svg wrapper around the root SVG element of the current tree.\n   *\n   * @memberof Chartist.Svg\n   * @return {Chartist.Svg} The root SVG element wrapped in a Chartist.Svg element\n   */\n  function root() {\n    var node = this._node;\n    while(node.nodeName !== 'svg') {\n      node = node.parentNode;\n    }\n    return new Chartist.Svg(node);\n  }\n\n  /**\n   * Find the first child SVG element of the current element that matches a CSS selector. The returned object is a Chartist.Svg wrapper.\n   *\n   * @memberof Chartist.Svg\n   * @param {String} selector A CSS selector that is used to query for child SVG elements\n   * @return {Chartist.Svg} The SVG wrapper for the element found or null if no element was found\n   */\n  function querySelector(selector) {\n    var foundNode = this._node.querySelector(selector);\n    return foundNode ? new Chartist.Svg(foundNode) : null;\n  }\n\n  /**\n   * Find the all child SVG elements of the current element that match a CSS selector. The returned object is a Chartist.Svg.List wrapper.\n   *\n   * @memberof Chartist.Svg\n   * @param {String} selector A CSS selector that is used to query for child SVG elements\n   * @return {Chartist.Svg.List} The SVG wrapper list for the element found or null if no element was found\n   */\n  function querySelectorAll(selector) {\n    var foundNodes = this._node.querySelectorAll(selector);\n    return foundNodes.length ? new Chartist.Svg.List(foundNodes) : null;\n  }\n\n  /**\n   * This method creates a foreignObject (see https://developer.mozilla.org/en-US/docs/Web/SVG/Element/foreignObject) that allows to embed HTML content into a SVG graphic. With the help of foreignObjects you can enable the usage of regular HTML elements inside of SVG where they are subject for SVG positioning and transformation but the Browser will use the HTML rendering capabilities for the containing DOM.\n   *\n   * @memberof Chartist.Svg\n   * @param {Node|String} content The DOM Node, or HTML string that will be converted to a DOM Node, that is then placed into and wrapped by the foreignObject\n   * @param {String} [attributes] An object with properties that will be added as attributes to the foreignObject element that is created. Attributes with undefined values will not be added.\n   * @param {String} [className] This class or class list will be added to the SVG element\n   * @param {Boolean} [insertFirst] Specifies if the foreignObject should be inserted as first child\n   * @return {Chartist.Svg} New wrapper object that wraps the foreignObject element\n   */\n  function foreignObject(content, attributes, className, insertFirst) {\n    // If content is string then we convert it to DOM\n    // TODO: Handle case where content is not a string nor a DOM Node\n    if(typeof content === 'string') {\n      var container = document.createElement('div');\n      container.innerHTML = content;\n      content = container.firstChild;\n    }\n\n    // Adding namespace to content element\n    content.setAttribute('xmlns', xhtmlNs);\n\n    // Creating the foreignObject without required extension attribute (as described here\n    // http://www.w3.org/TR/SVG/extend.html#ForeignObjectElement)\n    var fnObj = this.elem('foreignObject', attributes, className, insertFirst);\n\n    // Add content to foreignObjectElement\n    fnObj._node.appendChild(content);\n\n    return fnObj;\n  }\n\n  /**\n   * This method adds a new text element to the current Chartist.Svg wrapper.\n   *\n   * @memberof Chartist.Svg\n   * @param {String} t The text that should be added to the text element that is created\n   * @return {Chartist.Svg} The same wrapper object that was used to add the newly created element\n   */\n  function text(t) {\n    this._node.appendChild(document.createTextNode(t));\n    return this;\n  }\n\n  /**\n   * This method will clear all child nodes of the current wrapper object.\n   *\n   * @memberof Chartist.Svg\n   * @return {Chartist.Svg} The same wrapper object that got emptied\n   */\n  function empty() {\n    while (this._node.firstChild) {\n      this._node.removeChild(this._node.firstChild);\n    }\n\n    return this;\n  }\n\n  /**\n   * This method will cause the current wrapper to remove itself from its parent wrapper. Use this method if you'd like to get rid of an element in a given DOM structure.\n   *\n   * @memberof Chartist.Svg\n   * @return {Chartist.Svg} The parent wrapper object of the element that got removed\n   */\n  function remove() {\n    this._node.parentNode.removeChild(this._node);\n    return this.parent();\n  }\n\n  /**\n   * This method will replace the element with a new element that can be created outside of the current DOM.\n   *\n   * @memberof Chartist.Svg\n   * @param {Chartist.Svg} newElement The new Chartist.Svg object that will be used to replace the current wrapper object\n   * @return {Chartist.Svg} The wrapper of the new element\n   */\n  function replace(newElement) {\n    this._node.parentNode.replaceChild(newElement._node, this._node);\n    return newElement;\n  }\n\n  /**\n   * This method will append an element to the current element as a child.\n   *\n   * @memberof Chartist.Svg\n   * @param {Chartist.Svg} element The Chartist.Svg element that should be added as a child\n   * @param {Boolean} [insertFirst] Specifies if the element should be inserted as first child\n   * @return {Chartist.Svg} The wrapper of the appended object\n   */\n  function append(element, insertFirst) {\n    if(insertFirst && this._node.firstChild) {\n      this._node.insertBefore(element._node, this._node.firstChild);\n    } else {\n      this._node.appendChild(element._node);\n    }\n\n    return this;\n  }\n\n  /**\n   * Returns an array of class names that are attached to the current wrapper element. This method can not be chained further.\n   *\n   * @memberof Chartist.Svg\n   * @return {Array} A list of classes or an empty array if there are no classes on the current element\n   */\n  function classes() {\n    return this._node.getAttribute('class') ? this._node.getAttribute('class').trim().split(/\\s+/) : [];\n  }\n\n  /**\n   * Adds one or a space separated list of classes to the current element and ensures the classes are only existing once.\n   *\n   * @memberof Chartist.Svg\n   * @param {String} names A white space separated list of class names\n   * @return {Chartist.Svg} The wrapper of the current element\n   */\n  function addClass(names) {\n    this._node.setAttribute('class',\n      this.classes(this._node)\n        .concat(names.trim().split(/\\s+/))\n        .filter(function(elem, pos, self) {\n          return self.indexOf(elem) === pos;\n        }).join(' ')\n    );\n\n    return this;\n  }\n\n  /**\n   * Removes one or a space separated list of classes from the current element.\n   *\n   * @memberof Chartist.Svg\n   * @param {String} names A white space separated list of class names\n   * @return {Chartist.Svg} The wrapper of the current element\n   */\n  function removeClass(names) {\n    var removedClasses = names.trim().split(/\\s+/);\n\n    this._node.setAttribute('class', this.classes(this._node).filter(function(name) {\n      return removedClasses.indexOf(name) === -1;\n    }).join(' '));\n\n    return this;\n  }\n\n  /**\n   * Removes all classes from the current element.\n   *\n   * @memberof Chartist.Svg\n   * @return {Chartist.Svg} The wrapper of the current element\n   */\n  function removeAllClasses() {\n    this._node.setAttribute('class', '');\n\n    return this;\n  }\n\n  /**\n   * \"Save\" way to get property value from svg BoundingBox.\n   * This is a workaround. Firefox throws an NS_ERROR_FAILURE error if getBBox() is called on an invisible node.\n   * See [NS_ERROR_FAILURE: Component returned failure code: 0x80004005](http://jsfiddle.net/sym3tri/kWWDK/)\n   *\n   * @memberof Chartist.Svg\n   * @param {SVGElement} node The svg node to\n   * @param {String} prop The property to fetch (ex.: height, width, ...)\n   * @returns {Number} The value of the given bbox property\n   */\n  function getBBoxProperty(node, prop) {\n    try {\n      return node.getBBox()[prop];\n    } catch(e) {}\n\n    return 0;\n  }\n\n  /**\n   * Get element height with fallback to svg BoundingBox or parent container dimensions:\n   * See [bugzilla.mozilla.org](https://bugzilla.mozilla.org/show_bug.cgi?id=530985)\n   *\n   * @memberof Chartist.Svg\n   * @return {Number} The elements height in pixels\n   */\n  function height() {\n    return this._node.clientHeight || Math.round(getBBoxProperty(this._node, 'height')) || this._node.parentNode.clientHeight;\n  }\n\n  /**\n   * Get element width with fallback to svg BoundingBox or parent container dimensions:\n   * See [bugzilla.mozilla.org](https://bugzilla.mozilla.org/show_bug.cgi?id=530985)\n   *\n   * @memberof Chartist.Core\n   * @return {Number} The elements width in pixels\n   */\n  function width() {\n    return this._node.clientWidth || Math.round(getBBoxProperty(this._node, 'width')) || this._node.parentNode.clientWidth;\n  }\n\n  /**\n   * The animate function lets you animate the current element with SMIL animations. You can add animations for multiple attributes at the same time by using an animation definition object. This object should contain SMIL animation attributes. Please refer to http://www.w3.org/TR/SVG/animate.html for a detailed specification about the available animation attributes. Additionally an easing property can be passed in the animation definition object. This can be a string with a name of an easing function in `Chartist.Svg.Easing` or an array with four numbers specifying a cubic Bézier curve.\n   * **An animations object could look like this:**\n   * ```javascript\n   * element.animate({\n   *   opacity: {\n   *     dur: 1000,\n   *     from: 0,\n   *     to: 1\n   *   },\n   *   x1: {\n   *     dur: '1000ms',\n   *     from: 100,\n   *     to: 200,\n   *     easing: 'easeOutQuart'\n   *   },\n   *   y1: {\n   *     dur: '2s',\n   *     from: 0,\n   *     to: 100\n   *   }\n   * });\n   * ```\n   * **Automatic unit conversion**\n   * For the `dur` and the `begin` animate attribute you can also omit a unit by passing a number. The number will automatically be converted to milli seconds.\n   * **Guided mode**\n   * The default behavior of SMIL animations with offset using the `begin` attribute is that the attribute will keep it's original value until the animation starts. Mostly this behavior is not desired as you'd like to have your element attributes already initialized with the animation `from` value even before the animation starts. Also if you don't specify `fill=\"freeze\"` on an animate element or if you delete the animation after it's done (which is done in guided mode) the attribute will switch back to the initial value. This behavior is also not desired when performing simple one-time animations. For one-time animations you'd want to trigger animations immediately instead of relative to the document begin time. That's why in guided mode Chartist.Svg will also use the `begin` property to schedule a timeout and manually start the animation after the timeout. If you're using multiple SMIL definition objects for an attribute (in an array), guided mode will be disabled for this attribute, even if you explicitly enabled it.\n   * If guided mode is enabled the following behavior is added:\n   * - Before the animation starts (even when delayed with `begin`) the animated attribute will be set already to the `from` value of the animation\n   * - `begin` is explicitly set to `indefinite` so it can be started manually without relying on document begin time (creation)\n   * - The animate element will be forced to use `fill=\"freeze\"`\n   * - The animation will be triggered with `beginElement()` in a timeout where `begin` of the definition object is interpreted in milli seconds. If no `begin` was specified the timeout is triggered immediately.\n   * - After the animation the element attribute value will be set to the `to` value of the animation\n   * - The animate element is deleted from the DOM\n   *\n   * @memberof Chartist.Svg\n   * @param {Object} animations An animations object where the property keys are the attributes you'd like to animate. The properties should be objects again that contain the SMIL animation attributes (usually begin, dur, from, and to). The property begin and dur is auto converted (see Automatic unit conversion). You can also schedule multiple animations for the same attribute by passing an Array of SMIL definition objects. Attributes that contain an array of SMIL definition objects will not be executed in guided mode.\n   * @param {Boolean} guided Specify if guided mode should be activated for this animation (see Guided mode). If not otherwise specified, guided mode will be activated.\n   * @param {Object} eventEmitter If specified, this event emitter will be notified when an animation starts or ends.\n   * @return {Chartist.Svg} The current element where the animation was added\n   */\n  function animate(animations, guided, eventEmitter) {\n    if(guided === undefined) {\n      guided = true;\n    }\n\n    Object.keys(animations).forEach(function createAnimateForAttributes(attribute) {\n\n      function createAnimate(animationDefinition, guided) {\n        var attributeProperties = {},\n          animate,\n          timeout,\n          easing;\n\n        // Check if an easing is specified in the definition object and delete it from the object as it will not\n        // be part of the animate element attributes.\n        if(animationDefinition.easing) {\n          // If already an easing Bézier curve array we take it or we lookup a easing array in the Easing object\n          easing = animationDefinition.easing instanceof Array ?\n            animationDefinition.easing :\n            Chartist.Svg.Easing[animationDefinition.easing];\n          delete animationDefinition.easing;\n        }\n\n        // If numeric dur or begin was provided we assume milli seconds\n        animationDefinition.begin = Chartist.ensureUnit(animationDefinition.begin, 'ms');\n        animationDefinition.dur = Chartist.ensureUnit(animationDefinition.dur, 'ms');\n\n        if(easing) {\n          animationDefinition.calcMode = 'spline';\n          animationDefinition.keySplines = easing.join(' ');\n          animationDefinition.keyTimes = '0;1';\n        }\n\n        // Adding \"fill: freeze\" if we are in guided mode and set initial attribute values\n        if(guided) {\n          animationDefinition.fill = 'freeze';\n          // Animated property on our element should already be set to the animation from value in guided mode\n          attributeProperties[attribute] = animationDefinition.from;\n          this.attr(attributeProperties);\n\n          // In guided mode we also set begin to indefinite so we can trigger the start manually and put the begin\n          // which needs to be in ms aside\n          timeout = Chartist.stripUnit(animationDefinition.begin || 0);\n          animationDefinition.begin = 'indefinite';\n        }\n\n        animate = this.elem('animate', Chartist.extend({\n          attributeName: attribute\n        }, animationDefinition));\n\n        if(guided) {\n          // If guided we take the value that was put aside in timeout and trigger the animation manually with a timeout\n          setTimeout(function() {\n            // If beginElement fails we set the animated attribute to the end position and remove the animate element\n            // This happens if the SMIL ElementTimeControl interface is not supported or any other problems occured in\n            // the browser. (Currently FF 34 does not support animate elements in foreignObjects)\n            try {\n              animate._node.beginElement();\n            } catch(err) {\n              // Set animated attribute to current animated value\n              attributeProperties[attribute] = animationDefinition.to;\n              this.attr(attributeProperties);\n              // Remove the animate element as it's no longer required\n              animate.remove();\n            }\n          }.bind(this), timeout);\n        }\n\n        if(eventEmitter) {\n          animate._node.addEventListener('beginEvent', function handleBeginEvent() {\n            eventEmitter.emit('animationBegin', {\n              element: this,\n              animate: animate._node,\n              params: animationDefinition\n            });\n          }.bind(this));\n        }\n\n        animate._node.addEventListener('endEvent', function handleEndEvent() {\n          if(eventEmitter) {\n            eventEmitter.emit('animationEnd', {\n              element: this,\n              animate: animate._node,\n              params: animationDefinition\n            });\n          }\n\n          if(guided) {\n            // Set animated attribute to current animated value\n            attributeProperties[attribute] = animationDefinition.to;\n            this.attr(attributeProperties);\n            // Remove the animate element as it's no longer required\n            animate.remove();\n          }\n        }.bind(this));\n      }\n\n      // If current attribute is an array of definition objects we create an animate for each and disable guided mode\n      if(animations[attribute] instanceof Array) {\n        animations[attribute].forEach(function(animationDefinition) {\n          createAnimate.bind(this)(animationDefinition, false);\n        }.bind(this));\n      } else {\n        createAnimate.bind(this)(animations[attribute], guided);\n      }\n\n    }.bind(this));\n\n    return this;\n  }\n\n  Chartist.Svg = Chartist.Class.extend({\n    constructor: Svg,\n    attr: attr,\n    elem: elem,\n    parent: parent,\n    root: root,\n    querySelector: querySelector,\n    querySelectorAll: querySelectorAll,\n    foreignObject: foreignObject,\n    text: text,\n    empty: empty,\n    remove: remove,\n    replace: replace,\n    append: append,\n    classes: classes,\n    addClass: addClass,\n    removeClass: removeClass,\n    removeAllClasses: removeAllClasses,\n    height: height,\n    width: width,\n    animate: animate\n  });\n\n  /**\n   * This method checks for support of a given SVG feature like Extensibility, SVG-animation or the like. Check http://www.w3.org/TR/SVG11/feature for a detailed list.\n   *\n   * @memberof Chartist.Svg\n   * @param {String} feature The SVG 1.1 feature that should be checked for support.\n   * @return {Boolean} True of false if the feature is supported or not\n   */\n  Chartist.Svg.isSupported = function(feature) {\n    return document.implementation.hasFeature('www.http://w3.org/TR/SVG11/feature#' + feature, '1.1');\n  };\n\n  /**\n   * This Object contains some standard easing cubic bezier curves. Then can be used with their name in the `Chartist.Svg.animate`. You can also extend the list and use your own name in the `animate` function. Click the show code button to see the available bezier functions.\n   *\n   * @memberof Chartist.Svg\n   */\n  var easingCubicBeziers = {\n    easeInSine: [0.47, 0, 0.745, 0.715],\n    easeOutSine: [0.39, 0.575, 0.565, 1],\n    easeInOutSine: [0.445, 0.05, 0.55, 0.95],\n    easeInQuad: [0.55, 0.085, 0.68, 0.53],\n    easeOutQuad: [0.25, 0.46, 0.45, 0.94],\n    easeInOutQuad: [0.455, 0.03, 0.515, 0.955],\n    easeInCubic: [0.55, 0.055, 0.675, 0.19],\n    easeOutCubic: [0.215, 0.61, 0.355, 1],\n    easeInOutCubic: [0.645, 0.045, 0.355, 1],\n    easeInQuart: [0.895, 0.03, 0.685, 0.22],\n    easeOutQuart: [0.165, 0.84, 0.44, 1],\n    easeInOutQuart: [0.77, 0, 0.175, 1],\n    easeInQuint: [0.755, 0.05, 0.855, 0.06],\n    easeOutQuint: [0.23, 1, 0.32, 1],\n    easeInOutQuint: [0.86, 0, 0.07, 1],\n    easeInExpo: [0.95, 0.05, 0.795, 0.035],\n    easeOutExpo: [0.19, 1, 0.22, 1],\n    easeInOutExpo: [1, 0, 0, 1],\n    easeInCirc: [0.6, 0.04, 0.98, 0.335],\n    easeOutCirc: [0.075, 0.82, 0.165, 1],\n    easeInOutCirc: [0.785, 0.135, 0.15, 0.86],\n    easeInBack: [0.6, -0.28, 0.735, 0.045],\n    easeOutBack: [0.175, 0.885, 0.32, 1.275],\n    easeInOutBack: [0.68, -0.55, 0.265, 1.55]\n  };\n\n  Chartist.Svg.Easing = easingCubicBeziers;\n\n  /**\n   * This helper class is to wrap multiple `Chartist.Svg` elements into a list where you can call the `Chartist.Svg` functions on all elements in the list with one call. This is helpful when you'd like to perform calls with `Chartist.Svg` on multiple elements.\n   * An instance of this class is also returned by `Chartist.Svg.querySelectorAll`.\n   *\n   * @memberof Chartist.Svg\n   * @param {Array<Node>|NodeList} nodeList An Array of SVG DOM nodes or a SVG DOM NodeList (as returned by document.querySelectorAll)\n   * @constructor\n   */\n  function SvgList(nodeList) {\n    var list = this;\n\n    this.svgElements = [];\n    for(var i = 0; i < nodeList.length; i++) {\n      this.svgElements.push(new Chartist.Svg(nodeList[i]));\n    }\n\n    // Add delegation methods for Chartist.Svg\n    Object.keys(Chartist.Svg.prototype).filter(function(prototypeProperty) {\n      return ['constructor',\n          'parent',\n          'querySelector',\n          'querySelectorAll',\n          'replace',\n          'append',\n          'classes',\n          'height',\n          'width'].indexOf(prototypeProperty) === -1;\n    }).forEach(function(prototypeProperty) {\n      list[prototypeProperty] = function() {\n        var args = Array.prototype.slice.call(arguments, 0);\n        list.svgElements.forEach(function(element) {\n          Chartist.Svg.prototype[prototypeProperty].apply(element, args);\n        });\n        return list;\n      };\n    });\n  }\n\n  Chartist.Svg.List = Chartist.Class.extend({\n    constructor: SvgList\n  });\n}(window, document, Chartist));\n;/**\n * Chartist SVG path module for SVG path description creation and modification.\n *\n * @module Chartist.Svg.Path\n */\n/* global Chartist */\n(function(window, document, Chartist) {\n  'use strict';\n\n  /**\n   * Contains the descriptors of supported element types in a SVG path. Currently only move, line and curve are supported.\n   *\n   * @memberof Chartist.Svg.Path\n   * @type {Object}\n   */\n  var elementDescriptions = {\n    m: ['x', 'y'],\n    l: ['x', 'y'],\n    c: ['x1', 'y1', 'x2', 'y2', 'x', 'y'],\n    a: ['rx', 'ry', 'xAr', 'lAf', 'sf', 'x', 'y']\n  };\n\n  /**\n   * Default options for newly created SVG path objects.\n   *\n   * @memberof Chartist.Svg.Path\n   * @type {Object}\n   */\n  var defaultOptions = {\n    // The accuracy in digit count after the decimal point. This will be used to round numbers in the SVG path. If this option is set to false then no rounding will be performed.\n    accuracy: 3\n  };\n\n  function element(command, params, pathElements, pos, relative, data) {\n    var pathElement = Chartist.extend({\n      command: relative ? command.toLowerCase() : command.toUpperCase()\n    }, params, data ? { data: data } : {} );\n\n    pathElements.splice(pos, 0, pathElement);\n  }\n\n  function forEachParam(pathElements, cb) {\n    pathElements.forEach(function(pathElement, pathElementIndex) {\n      elementDescriptions[pathElement.command.toLowerCase()].forEach(function(paramName, paramIndex) {\n        cb(pathElement, paramName, pathElementIndex, paramIndex, pathElements);\n      });\n    });\n  }\n\n  /**\n   * Used to construct a new path object.\n   *\n   * @memberof Chartist.Svg.Path\n   * @param {Boolean} close If set to true then this path will be closed when stringified (with a Z at the end)\n   * @param {Object} options Options object that overrides the default objects. See default options for more details.\n   * @constructor\n   */\n  function SvgPath(close, options) {\n    this.pathElements = [];\n    this.pos = 0;\n    this.close = close;\n    this.options = Chartist.extend({}, defaultOptions, options);\n  }\n\n  /**\n   * Gets or sets the current position (cursor) inside of the path. You can move around the cursor freely but limited to 0 or the count of existing elements. All modifications with element functions will insert new elements at the position of this cursor.\n   *\n   * @memberof Chartist.Svg.Path\n   * @param {Number} [pos] If a number is passed then the cursor is set to this position in the path element array.\n   * @return {Chartist.Svg.Path|Number} If the position parameter was passed then the return value will be the path object for easy call chaining. If no position parameter was passed then the current position is returned.\n   */\n  function position(pos) {\n    if(pos !== undefined) {\n      this.pos = Math.max(0, Math.min(this.pathElements.length, pos));\n      return this;\n    } else {\n      return this.pos;\n    }\n  }\n\n  /**\n   * Removes elements from the path starting at the current position.\n   *\n   * @memberof Chartist.Svg.Path\n   * @param {Number} count Number of path elements that should be removed from the current position.\n   * @return {Chartist.Svg.Path} The current path object for easy call chaining.\n   */\n  function remove(count) {\n    this.pathElements.splice(this.pos, count);\n    return this;\n  }\n\n  /**\n   * Use this function to add a new move SVG path element.\n   *\n   * @memberof Chartist.Svg.Path\n   * @param {Number} x The x coordinate for the move element.\n   * @param {Number} y The y coordinate for the move element.\n   * @param {Boolean} [relative] If set to true the move element will be created with relative coordinates (lowercase letter)\n   * @param {*} [data] Any data that should be stored with the element object that will be accessible in pathElement\n   * @return {Chartist.Svg.Path} The current path object for easy call chaining.\n   */\n  function move(x, y, relative, data) {\n    element('M', {\n      x: +x,\n      y: +y\n    }, this.pathElements, this.pos++, relative, data);\n    return this;\n  }\n\n  /**\n   * Use this function to add a new line SVG path element.\n   *\n   * @memberof Chartist.Svg.Path\n   * @param {Number} x The x coordinate for the line element.\n   * @param {Number} y The y coordinate for the line element.\n   * @param {Boolean} [relative] If set to true the line element will be created with relative coordinates (lowercase letter)\n   * @param {*} [data] Any data that should be stored with the element object that will be accessible in pathElement\n   * @return {Chartist.Svg.Path} The current path object for easy call chaining.\n   */\n  function line(x, y, relative, data) {\n    element('L', {\n      x: +x,\n      y: +y\n    }, this.pathElements, this.pos++, relative, data);\n    return this;\n  }\n\n  /**\n   * Use this function to add a new curve SVG path element.\n   *\n   * @memberof Chartist.Svg.Path\n   * @param {Number} x1 The x coordinate for the first control point of the bezier curve.\n   * @param {Number} y1 The y coordinate for the first control point of the bezier curve.\n   * @param {Number} x2 The x coordinate for the second control point of the bezier curve.\n   * @param {Number} y2 The y coordinate for the second control point of the bezier curve.\n   * @param {Number} x The x coordinate for the target point of the curve element.\n   * @param {Number} y The y coordinate for the target point of the curve element.\n   * @param {Boolean} [relative] If set to true the curve element will be created with relative coordinates (lowercase letter)\n   * @param {*} [data] Any data that should be stored with the element object that will be accessible in pathElement\n   * @return {Chartist.Svg.Path} The current path object for easy call chaining.\n   */\n  function curve(x1, y1, x2, y2, x, y, relative, data) {\n    element('C', {\n      x1: +x1,\n      y1: +y1,\n      x2: +x2,\n      y2: +y2,\n      x: +x,\n      y: +y\n    }, this.pathElements, this.pos++, relative, data);\n    return this;\n  }\n\n  /**\n   * Use this function to add a new non-bezier curve SVG path element.\n   *\n   * @memberof Chartist.Svg.Path\n   * @param {Number} rx The radius to be used for the x-axis of the arc.\n   * @param {Number} ry The radius to be used for the y-axis of the arc.\n   * @param {Number} xAr Defines the orientation of the arc\n   * @param {Number} lAf Large arc flag\n   * @param {Number} sf Sweep flag\n   * @param {Number} x The x coordinate for the target point of the curve element.\n   * @param {Number} y The y coordinate for the target point of the curve element.\n   * @param {Boolean} [relative] If set to true the curve element will be created with relative coordinates (lowercase letter)\n   * @param {*} [data] Any data that should be stored with the element object that will be accessible in pathElement\n   * @return {Chartist.Svg.Path} The current path object for easy call chaining.\n   */\n  function arc(rx, ry, xAr, lAf, sf, x, y, relative, data) {\n    element('A', {\n      rx: +rx,\n      ry: +ry,\n      xAr: +xAr,\n      lAf: +lAf,\n      sf: +sf,\n      x: +x,\n      y: +y\n    }, this.pathElements, this.pos++, relative, data);\n    return this;\n  }\n\n  /**\n   * Parses an SVG path seen in the d attribute of path elements, and inserts the parsed elements into the existing path object at the current cursor position. Any closing path indicators (Z at the end of the path) will be ignored by the parser as this is provided by the close option in the options of the path object.\n   *\n   * @memberof Chartist.Svg.Path\n   * @param {String} path Any SVG path that contains move (m), line (l) or curve (c) components.\n   * @return {Chartist.Svg.Path} The current path object for easy call chaining.\n   */\n  function parse(path) {\n    // Parsing the SVG path string into an array of arrays [['M', '10', '10'], ['L', '100', '100']]\n    var chunks = path.replace(/([A-Za-z])([0-9])/g, '$1 $2')\n      .replace(/([0-9])([A-Za-z])/g, '$1 $2')\n      .split(/[\\s,]+/)\n      .reduce(function(result, element) {\n        if(element.match(/[A-Za-z]/)) {\n          result.push([]);\n        }\n\n        result[result.length - 1].push(element);\n        return result;\n      }, []);\n\n    // If this is a closed path we remove the Z at the end because this is determined by the close option\n    if(chunks[chunks.length - 1][0].toUpperCase() === 'Z') {\n      chunks.pop();\n    }\n\n    // Using svgPathElementDescriptions to map raw path arrays into objects that contain the command and the parameters\n    // For example {command: 'M', x: '10', y: '10'}\n    var elements = chunks.map(function(chunk) {\n        var command = chunk.shift(),\n          description = elementDescriptions[command.toLowerCase()];\n\n        return Chartist.extend({\n          command: command\n        }, description.reduce(function(result, paramName, index) {\n          result[paramName] = +chunk[index];\n          return result;\n        }, {}));\n      });\n\n    // Preparing a splice call with the elements array as var arg params and insert the parsed elements at the current position\n    var spliceArgs = [this.pos, 0];\n    Array.prototype.push.apply(spliceArgs, elements);\n    Array.prototype.splice.apply(this.pathElements, spliceArgs);\n    // Increase the internal position by the element count\n    this.pos += elements.length;\n\n    return this;\n  }\n\n  /**\n   * This function renders to current SVG path object into a final SVG string that can be used in the d attribute of SVG path elements. It uses the accuracy option to round big decimals. If the close parameter was set in the constructor of this path object then a path closing Z will be appended to the output string.\n   *\n   * @memberof Chartist.Svg.Path\n   * @return {String}\n   */\n  function stringify() {\n    var accuracyMultiplier = Math.pow(10, this.options.accuracy);\n\n    return this.pathElements.reduce(function(path, pathElement) {\n        var params = elementDescriptions[pathElement.command.toLowerCase()].map(function(paramName) {\n          return this.options.accuracy ?\n            (Math.round(pathElement[paramName] * accuracyMultiplier) / accuracyMultiplier) :\n            pathElement[paramName];\n        }.bind(this));\n\n        return path + pathElement.command + params.join(',');\n      }.bind(this), '') + (this.close ? 'Z' : '');\n  }\n\n  /**\n   * Scales all elements in the current SVG path object. There is an individual parameter for each coordinate. Scaling will also be done for control points of curves, affecting the given coordinate.\n   *\n   * @memberof Chartist.Svg.Path\n   * @param {Number} x The number which will be used to scale the x, x1 and x2 of all path elements.\n   * @param {Number} y The number which will be used to scale the y, y1 and y2 of all path elements.\n   * @return {Chartist.Svg.Path} The current path object for easy call chaining.\n   */\n  function scale(x, y) {\n    forEachParam(this.pathElements, function(pathElement, paramName) {\n      pathElement[paramName] *= paramName[0] === 'x' ? x : y;\n    });\n    return this;\n  }\n\n  /**\n   * Translates all elements in the current SVG path object. The translation is relative and there is an individual parameter for each coordinate. Translation will also be done for control points of curves, affecting the given coordinate.\n   *\n   * @memberof Chartist.Svg.Path\n   * @param {Number} x The number which will be used to translate the x, x1 and x2 of all path elements.\n   * @param {Number} y The number which will be used to translate the y, y1 and y2 of all path elements.\n   * @return {Chartist.Svg.Path} The current path object for easy call chaining.\n   */\n  function translate(x, y) {\n    forEachParam(this.pathElements, function(pathElement, paramName) {\n      pathElement[paramName] += paramName[0] === 'x' ? x : y;\n    });\n    return this;\n  }\n\n  /**\n   * This function will run over all existing path elements and then loop over their attributes. The callback function will be called for every path element attribute that exists in the current path.\n   * The method signature of the callback function looks like this:\n   * ```javascript\n   * function(pathElement, paramName, pathElementIndex, paramIndex, pathElements)\n   * ```\n   * If something else than undefined is returned by the callback function, this value will be used to replace the old value. This allows you to build custom transformations of path objects that can't be achieved using the basic transformation functions scale and translate.\n   *\n   * @memberof Chartist.Svg.Path\n   * @param {Function} transformFnc The callback function for the transformation. Check the signature in the function description.\n   * @return {Chartist.Svg.Path} The current path object for easy call chaining.\n   */\n  function transform(transformFnc) {\n    forEachParam(this.pathElements, function(pathElement, paramName, pathElementIndex, paramIndex, pathElements) {\n      var transformed = transformFnc(pathElement, paramName, pathElementIndex, paramIndex, pathElements);\n      if(transformed || transformed === 0) {\n        pathElement[paramName] = transformed;\n      }\n    });\n    return this;\n  }\n\n  /**\n   * This function clones a whole path object with all its properties. This is a deep clone and path element objects will also be cloned.\n   *\n   * @memberof Chartist.Svg.Path\n   * @param {Boolean} [close] Optional option to set the new cloned path to closed. If not specified or false, the original path close option will be used.\n   * @return {Chartist.Svg.Path}\n   */\n  function clone(close) {\n    var c = new Chartist.Svg.Path(close || this.close);\n    c.pos = this.pos;\n    c.pathElements = this.pathElements.slice().map(function cloneElements(pathElement) {\n      return Chartist.extend({}, pathElement);\n    });\n    c.options = Chartist.extend({}, this.options);\n    return c;\n  }\n\n  /**\n   * Split a Svg.Path object by a specific command in the path chain. The path chain will be split and an array of newly created paths objects will be returned. This is useful if you'd like to split an SVG path by it's move commands, for example, in order to isolate chunks of drawings.\n   *\n   * @memberof Chartist.Svg.Path\n   * @param {String} command The command you'd like to use to split the path\n   * @return {Array<Chartist.Svg.Path>}\n   */\n  function splitByCommand(command) {\n    var split = [\n      new Chartist.Svg.Path()\n    ];\n\n    this.pathElements.forEach(function(pathElement) {\n      if(pathElement.command === command.toUpperCase() && split[split.length - 1].pathElements.length !== 0) {\n        split.push(new Chartist.Svg.Path());\n      }\n\n      split[split.length - 1].pathElements.push(pathElement);\n    });\n\n    return split;\n  }\n\n  /**\n   * This static function on `Chartist.Svg.Path` is joining multiple paths together into one paths.\n   *\n   * @memberof Chartist.Svg.Path\n   * @param {Array<Chartist.Svg.Path>} paths A list of paths to be joined together. The order is important.\n   * @param {boolean} close If the newly created path should be a closed path\n   * @param {Object} options Path options for the newly created path.\n   * @return {Chartist.Svg.Path}\n   */\n\n  function join(paths, close, options) {\n    var joinedPath = new Chartist.Svg.Path(close, options);\n    for(var i = 0; i < paths.length; i++) {\n      var path = paths[i];\n      for(var j = 0; j < path.pathElements.length; j++) {\n        joinedPath.pathElements.push(path.pathElements[j]);\n      }\n    }\n    return joinedPath;\n  }\n\n  Chartist.Svg.Path = Chartist.Class.extend({\n    constructor: SvgPath,\n    position: position,\n    remove: remove,\n    move: move,\n    line: line,\n    curve: curve,\n    arc: arc,\n    scale: scale,\n    translate: translate,\n    transform: transform,\n    parse: parse,\n    stringify: stringify,\n    clone: clone,\n    splitByCommand: splitByCommand\n  });\n\n  Chartist.Svg.Path.elementDescriptions = elementDescriptions;\n  Chartist.Svg.Path.join = join;\n}(window, document, Chartist));\n;/**\n * Axis base class used to implement different axis types\n *\n * @module Chartist.Axis\n */\n/* global Chartist */\n(function (window, document, Chartist) {\n  'use strict';\n\n  var axisUnits = {\n    x: {\n      pos: 'x',\n      len: 'width',\n      dir: 'horizontal',\n      rectStart: 'x1',\n      rectEnd: 'x2',\n      rectOffset: 'y2'\n    },\n    y: {\n      pos: 'y',\n      len: 'height',\n      dir: 'vertical',\n      rectStart: 'y2',\n      rectEnd: 'y1',\n      rectOffset: 'x1'\n    }\n  };\n\n  function Axis(units, chartRect, options) {\n    this.units = units;\n    this.counterUnits = units === axisUnits.x ? axisUnits.y : axisUnits.x;\n    this.chartRect = chartRect;\n    this.axisLength = chartRect[units.rectEnd] - chartRect[units.rectStart];\n    this.gridOffset = chartRect[units.rectOffset];\n    this.options = options;\n  }\n\n  Chartist.Axis = Chartist.Class.extend({\n    constructor: Axis,\n    projectValue: function(value, index, data) {\n      throw new Error('Base axis can\\'t be instantiated!');\n    }\n  });\n\n  Chartist.Axis.units = axisUnits;\n\n}(window, document, Chartist));\n;/**\n * The linear scale axis uses standard linear scale projection of values along an axis.\n *\n * @module Chartist.LinearScaleAxis\n */\n/* global Chartist */\n(function (window, document, Chartist) {\n  'use strict';\n\n  function LinearScaleAxis(axisUnit, chartRect, options) {\n    Chartist.LinearScaleAxis.super.constructor.call(this,\n      axisUnit,\n      chartRect,\n      options);\n\n    this.bounds = Chartist.getBounds(this.axisLength, options.highLow, options.scaleMinSpace, options.referenceValue, options.onlyInteger);\n  }\n\n  function projectValue(value) {\n    return {\n      pos: this.axisLength * (value - this.bounds.min) / this.bounds.range,\n      len: Chartist.projectLength(this.axisLength, this.bounds.step, this.bounds)\n    };\n  }\n\n  Chartist.LinearScaleAxis = Chartist.Axis.extend({\n    constructor: LinearScaleAxis,\n    projectValue: projectValue\n  });\n\n}(window, document, Chartist));\n;/**\n * Step axis for step based charts like bar chart or step based line chart\n *\n * @module Chartist.StepAxis\n */\n/* global Chartist */\n(function (window, document, Chartist) {\n  'use strict';\n\n  function StepAxis(axisUnit, chartRect, options) {\n    Chartist.StepAxis.super.constructor.call(this,\n      axisUnit,\n      chartRect,\n      options);\n\n    this.stepLength = this.axisLength / (options.stepCount - (options.stretch ? 1 : 0));\n  }\n\n  function projectValue(value, index) {\n    return {\n      pos: this.stepLength * index,\n      len: this.stepLength\n    };\n  }\n\n  Chartist.StepAxis = Chartist.Axis.extend({\n    constructor: StepAxis,\n    projectValue: projectValue\n  });\n\n}(window, document, Chartist));\n;/**\n * The Chartist line chart can be used to draw Line or Scatter charts. If used in the browser you can access the global `Chartist` namespace where you find the `Line` function as a main entry point.\n *\n * For examples on how to use the line chart please check the examples of the `Chartist.Line` method.\n *\n * @module Chartist.Line\n */\n/* global Chartist */\n(function(window, document, Chartist){\n  'use strict';\n\n  /**\n   * Default options in line charts. Expand the code view to see a detailed list of options with comments.\n   *\n   * @memberof Chartist.Line\n   */\n  var defaultOptions = {\n    // Options for X-Axis\n    axisX: {\n      // The offset of the labels to the chart area\n      offset: 30,\n      // Position where labels are placed. Can be set to `start` or `end` where `start` is equivalent to left or top on vertical axis and `end` is equivalent to right or bottom on horizontal axis.\n      position: 'end',\n      // Allows you to correct label positioning on this axis by positive or negative x and y offset.\n      labelOffset: {\n        x: 0,\n        y: 0\n      },\n      // If labels should be shown or not\n      showLabel: true,\n      // If the axis grid should be drawn or not\n      showGrid: true,\n      // Interpolation function that allows you to intercept the value from the axis label\n      labelInterpolationFnc: Chartist.noop,\n      // Use only integer values (whole numbers) for the scale steps\n      onlyInteger: false\n    },\n    // Options for Y-Axis\n    axisY: {\n      // The offset of the labels to the chart area\n      offset: 40,\n      // Position where labels are placed. Can be set to `start` or `end` where `start` is equivalent to left or top on vertical axis and `end` is equivalent to right or bottom on horizontal axis.\n      position: 'start',\n      // Allows you to correct label positioning on this axis by positive or negative x and y offset.\n      labelOffset: {\n        x: 0,\n        y: 0\n      },\n      // If labels should be shown or not\n      showLabel: true,\n      // If the axis grid should be drawn or not\n      showGrid: true,\n      // Interpolation function that allows you to intercept the value from the axis label\n      labelInterpolationFnc: Chartist.noop,\n      // This value specifies the minimum height in pixel of the scale steps\n      scaleMinSpace: 20,\n      // Use only integer values (whole numbers) for the scale steps\n      onlyInteger: false\n    },\n    // Specify a fixed width for the chart as a string (i.e. '100px' or '50%')\n    width: undefined,\n    // Specify a fixed height for the chart as a string (i.e. '100px' or '50%')\n    height: undefined,\n    // If the line should be drawn or not\n    showLine: true,\n    // If dots should be drawn or not\n    showPoint: true,\n    // If the line chart should draw an area\n    showArea: false,\n    // The base for the area chart that will be used to close the area shape (is normally 0)\n    areaBase: 0,\n    // Specify if the lines should be smoothed. This value can be true or false where true will result in smoothing using the default smoothing interpolation function Chartist.Interpolation.cardinal and false results in Chartist.Interpolation.none. You can also choose other smoothing / interpolation functions available in the Chartist.Interpolation module, or write your own interpolation function. Check the examples for a brief description.\n    lineSmooth: true,\n    // Overriding the natural low of the chart allows you to zoom in or limit the charts lowest displayed value\n    low: undefined,\n    // Overriding the natural high of the chart allows you to zoom in or limit the charts highest displayed value\n    high: undefined,\n    // Padding of the chart drawing area to the container element and labels as a number or padding object {top: 5, right: 5, bottom: 5, left: 5}\n    chartPadding: {\n      top: 15,\n      right: 15,\n      bottom: 5,\n      left: 10\n    },\n    // When set to true, the last grid line on the x-axis is not drawn and the chart elements will expand to the full available width of the chart. For the last label to be drawn correctly you might need to add chart padding or offset the last label with a draw event handler.\n    fullWidth: false,\n    // If true the whole data is reversed including labels, the series order as well as the whole series data arrays.\n    reverseData: false,\n    // Override the class names that get used to generate the SVG structure of the chart\n    classNames: {\n      chart: 'ct-chart-line',\n      label: 'ct-label',\n      labelGroup: 'ct-labels',\n      series: 'ct-series',\n      line: 'ct-line',\n      point: 'ct-point',\n      area: 'ct-area',\n      grid: 'ct-grid',\n      gridGroup: 'ct-grids',\n      vertical: 'ct-vertical',\n      horizontal: 'ct-horizontal',\n      start: 'ct-start',\n      end: 'ct-end'\n    }\n  };\n\n  /**\n   * Creates a new chart\n   *\n   */\n  function createChart(options) {\n    var seriesGroups = [];\n    var normalizedData = Chartist.normalizeDataArray(Chartist.getDataArray(this.data, options.reverseData), this.data.labels.length);\n\n    // Create new svg object\n    this.svg = Chartist.createSvg(this.container, options.width, options.height, options.classNames.chart);\n\n    var chartRect = Chartist.createChartRect(this.svg, options, defaultOptions.padding);\n    var highLow = Chartist.getHighLow(normalizedData, options);\n\n    var axisX = new Chartist.StepAxis(Chartist.Axis.units.x, chartRect, {\n      stepCount: this.data.labels.length,\n      stretch: options.fullWidth\n    });\n\n    var axisY = new Chartist.LinearScaleAxis(Chartist.Axis.units.y, chartRect, {\n      highLow: highLow,\n      scaleMinSpace: options.axisY.scaleMinSpace,\n      onlyInteger: options.axisY.onlyInteger\n    });\n\n    // Start drawing\n    var labelGroup = this.svg.elem('g').addClass(options.classNames.labelGroup),\n      gridGroup = this.svg.elem('g').addClass(options.classNames.gridGroup);\n\n    Chartist.createAxis(\n      axisX,\n      this.data.labels,\n      chartRect,\n      gridGroup,\n      labelGroup,\n      this.supportsForeignObject,\n      options,\n      this.eventEmitter\n    );\n\n    Chartist.createAxis(\n      axisY,\n      axisY.bounds.values,\n      chartRect,\n      gridGroup,\n      labelGroup,\n      this.supportsForeignObject,\n      options,\n      this.eventEmitter\n    );\n\n    // Draw the series\n    this.data.series.forEach(function(series, seriesIndex) {\n      seriesGroups[seriesIndex] = this.svg.elem('g');\n\n      // Write attributes to series group element. If series name or meta is undefined the attributes will not be written\n      seriesGroups[seriesIndex].attr({\n        'series-name': series.name,\n        'meta': Chartist.serialize(series.meta)\n      }, Chartist.xmlNs.uri);\n\n      // Use series class from series data or if not set generate one\n      seriesGroups[seriesIndex].addClass([\n        options.classNames.series,\n        (series.className || options.classNames.series + '-' + Chartist.alphaNumerate(seriesIndex))\n      ].join(' '));\n\n      var pathCoordinates = [],\n        pathData = [];\n\n      normalizedData[seriesIndex].forEach(function(value, valueIndex) {\n        var p = {\n          x: chartRect.x1 + axisX.projectValue(value, valueIndex, normalizedData[seriesIndex]).pos,\n          y: chartRect.y1 - axisY.projectValue(value, valueIndex, normalizedData[seriesIndex]).pos\n        };\n        pathCoordinates.push(p.x, p.y);\n        pathData.push({\n          value: value,\n          valueIndex: valueIndex,\n          meta: Chartist.getMetaData(series, valueIndex)\n        });\n      }.bind(this));\n\n      var seriesOptions = {\n        lineSmooth: Chartist.getSeriesOption(series, options, 'lineSmooth'),\n        showPoint: Chartist.getSeriesOption(series, options, 'showPoint'),\n        showLine: Chartist.getSeriesOption(series, options, 'showLine'),\n        showArea: Chartist.getSeriesOption(series, options, 'showArea')\n      };\n\n      var smoothing = typeof seriesOptions.lineSmooth === 'function' ?\n        seriesOptions.lineSmooth : (seriesOptions.lineSmooth ? Chartist.Interpolation.cardinal() : Chartist.Interpolation.none());\n      // Interpolating path where pathData will be used to annotate each path element so we can trace back the original\n      // index, value and meta data\n      var path = smoothing(pathCoordinates, pathData);\n\n      // If we should show points we need to create them now to avoid secondary loop\n      // Points are drawn from the pathElements returned by the interpolation function\n      // Small offset for Firefox to render squares correctly\n      if (seriesOptions.showPoint) {\n\n        path.pathElements.forEach(function(pathElement) {\n          var point = seriesGroups[seriesIndex].elem('line', {\n            x1: pathElement.x,\n            y1: pathElement.y,\n            x2: pathElement.x + 0.01,\n            y2: pathElement.y\n          }, options.classNames.point).attr({\n            'value': pathElement.data.value,\n            'meta': pathElement.data.meta\n          }, Chartist.xmlNs.uri);\n\n          this.eventEmitter.emit('draw', {\n            type: 'point',\n            value: pathElement.data.value,\n            index: pathElement.data.valueIndex,\n            meta: pathElement.data.meta,\n            series: series,\n            seriesIndex: seriesIndex,\n            group: seriesGroups[seriesIndex],\n            element: point,\n            x: pathElement.x,\n            y: pathElement.y\n          });\n        }.bind(this));\n      }\n\n      if(seriesOptions.showLine) {\n        var line = seriesGroups[seriesIndex].elem('path', {\n          d: path.stringify()\n        }, options.classNames.line, true).attr({\n          'values': normalizedData[seriesIndex]\n        }, Chartist.xmlNs.uri);\n\n        this.eventEmitter.emit('draw', {\n          type: 'line',\n          values: normalizedData[seriesIndex],\n          path: path.clone(),\n          chartRect: chartRect,\n          index: seriesIndex,\n          series: series,\n          seriesIndex: seriesIndex,\n          group: seriesGroups[seriesIndex],\n          element: line\n        });\n      }\n\n      if(seriesOptions.showArea) {\n        // If areaBase is outside the chart area (< low or > high) we need to set it respectively so that\n        // the area is not drawn outside the chart area.\n        var areaBase = Math.max(Math.min(options.areaBase, axisY.bounds.max), axisY.bounds.min);\n\n        // We project the areaBase value into screen coordinates\n        var areaBaseProjected = chartRect.y1 - axisY.projectValue(areaBase).pos;\n\n        // In order to form the area we'll first split the path by move commands so we can chunk it up into segments\n        path.splitByCommand('M').filter(function onlySolidSegments(pathSegment) {\n          // We filter only \"solid\" segments that contain more than one point. Otherwise there's no need for an area\n          return pathSegment.pathElements.length > 1;\n        }).map(function convertToArea(solidPathSegments) {\n          // Receiving the filtered solid path segments we can now convert those segments into fill areas\n          var firstElement = solidPathSegments.pathElements[0];\n          var lastElement = solidPathSegments.pathElements[solidPathSegments.pathElements.length - 1];\n\n          // Cloning the solid path segment with closing option and removing the first move command from the clone\n          // We then insert a new move that should start at the area base and draw a straight line up or down\n          // at the end of the path we add an additional straight line to the projected area base value\n          // As the closing option is set our path will be automatically closed\n          return solidPathSegments.clone(true)\n            .position(0)\n            .remove(1)\n            .move(firstElement.x, areaBaseProjected)\n            .line(firstElement.x, firstElement.y)\n            .position(solidPathSegments.pathElements.length + 1)\n            .line(lastElement.x, areaBaseProjected);\n\n        }).forEach(function createArea(areaPath) {\n          // For each of our newly created area paths, we'll now create path elements by stringifying our path objects\n          // and adding the created DOM elements to the correct series group\n          var area = seriesGroups[seriesIndex].elem('path', {\n            d: areaPath.stringify()\n          }, options.classNames.area, true).attr({\n            'values': normalizedData[seriesIndex]\n          }, Chartist.xmlNs.uri);\n\n          // Emit an event for each area that was drawn\n          this.eventEmitter.emit('draw', {\n            type: 'area',\n            values: normalizedData[seriesIndex],\n            path: areaPath.clone(),\n            series: series,\n            seriesIndex: seriesIndex,\n            chartRect: chartRect,\n            index: seriesIndex,\n            group: seriesGroups[seriesIndex],\n            element: area\n          });\n        }.bind(this));\n      }\n    }.bind(this));\n\n    this.eventEmitter.emit('created', {\n      bounds: axisY.bounds,\n      chartRect: chartRect,\n      axisX: axisX,\n      axisY: axisY,\n      svg: this.svg,\n      options: options\n    });\n  }\n\n  /**\n   * This method creates a new line chart.\n   *\n   * @memberof Chartist.Line\n   * @param {String|Node} query A selector query string or directly a DOM element\n   * @param {Object} data The data object that needs to consist of a labels and a series array\n   * @param {Object} [options] The options object with options that override the default options. Check the examples for a detailed list.\n   * @param {Array} [responsiveOptions] Specify an array of responsive option arrays which are a media query and options object pair => [[mediaQueryString, optionsObject],[more...]]\n   * @return {Object} An object which exposes the API for the created chart\n   *\n   * @example\n   * // Create a simple line chart\n   * var data = {\n   *   // A labels array that can contain any sort of values\n   *   labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'],\n   *   // Our series array that contains series objects or in this case series data arrays\n   *   series: [\n   *     [5, 2, 4, 2, 0]\n   *   ]\n   * };\n   *\n   * // As options we currently only set a static size of 300x200 px\n   * var options = {\n   *   width: '300px',\n   *   height: '200px'\n   * };\n   *\n   * // In the global name space Chartist we call the Line function to initialize a line chart. As a first parameter we pass in a selector where we would like to get our chart created. Second parameter is the actual data object and as a third parameter we pass in our options\n   * new Chartist.Line('.ct-chart', data, options);\n   *\n   * @example\n   * // Use specific interpolation function with configuration from the Chartist.Interpolation module\n   *\n   * var chart = new Chartist.Line('.ct-chart', {\n   *   labels: [1, 2, 3, 4, 5],\n   *   series: [\n   *     [1, 1, 8, 1, 7]\n   *   ]\n   * }, {\n   *   lineSmooth: Chartist.Interpolation.cardinal({\n   *     tension: 0.2\n   *   })\n   * });\n   *\n   * @example\n   * // Create a line chart with responsive options\n   *\n   * var data = {\n   *   // A labels array that can contain any sort of values\n   *   labels: ['Monday', 'Tuesday', 'Wednesday', 'Thursday', 'Friday'],\n   *   // Our series array that contains series objects or in this case series data arrays\n   *   series: [\n   *     [5, 2, 4, 2, 0]\n   *   ]\n   * };\n   *\n   * // In adition to the regular options we specify responsive option overrides that will override the default configutation based on the matching media queries.\n   * var responsiveOptions = [\n   *   ['screen and (min-width: 641px) and (max-width: 1024px)', {\n   *     showPoint: false,\n   *     axisX: {\n   *       labelInterpolationFnc: function(value) {\n   *         // Will return Mon, Tue, Wed etc. on medium screens\n   *         return value.slice(0, 3);\n   *       }\n   *     }\n   *   }],\n   *   ['screen and (max-width: 640px)', {\n   *     showLine: false,\n   *     axisX: {\n   *       labelInterpolationFnc: function(value) {\n   *         // Will return M, T, W etc. on small screens\n   *         return value[0];\n   *       }\n   *     }\n   *   }]\n   * ];\n   *\n   * new Chartist.Line('.ct-chart', data, null, responsiveOptions);\n   *\n   */\n  function Line(query, data, options, responsiveOptions) {\n    Chartist.Line.super.constructor.call(this,\n      query,\n      data,\n      defaultOptions,\n      Chartist.extend({}, defaultOptions, options),\n      responsiveOptions);\n  }\n\n  // Creating line chart type in Chartist namespace\n  Chartist.Line = Chartist.Base.extend({\n    constructor: Line,\n    createChart: createChart\n  });\n\n}(window, document, Chartist));\n;/**\n * The bar chart module of Chartist that can be used to draw unipolar or bipolar bar and grouped bar charts.\n *\n * @module Chartist.Bar\n */\n/* global Chartist */\n(function(window, document, Chartist){\n  'use strict';\n\n  /**\n   * Default options in bar charts. Expand the code view to see a detailed list of options with comments.\n   *\n   * @memberof Chartist.Bar\n   */\n  var defaultOptions = {\n    // Options for X-Axis\n    axisX: {\n      // The offset of the chart drawing area to the border of the container\n      offset: 30,\n      // Position where labels are placed. Can be set to `start` or `end` where `start` is equivalent to left or top on vertical axis and `end` is equivalent to right or bottom on horizontal axis.\n      position: 'end',\n      // Allows you to correct label positioning on this axis by positive or negative x and y offset.\n      labelOffset: {\n        x: 0,\n        y: 0\n      },\n      // If labels should be shown or not\n      showLabel: true,\n      // If the axis grid should be drawn or not\n      showGrid: true,\n      // Interpolation function that allows you to intercept the value from the axis label\n      labelInterpolationFnc: Chartist.noop,\n      // This value specifies the minimum width in pixel of the scale steps\n      scaleMinSpace: 30,\n      // Use only integer values (whole numbers) for the scale steps\n      onlyInteger: false\n    },\n    // Options for Y-Axis\n    axisY: {\n      // The offset of the chart drawing area to the border of the container\n      offset: 40,\n      // Position where labels are placed. Can be set to `start` or `end` where `start` is equivalent to left or top on vertical axis and `end` is equivalent to right or bottom on horizontal axis.\n      position: 'start',\n      // Allows you to correct label positioning on this axis by positive or negative x and y offset.\n      labelOffset: {\n        x: 0,\n        y: 0\n      },\n      // If labels should be shown or not\n      showLabel: true,\n      // If the axis grid should be drawn or not\n      showGrid: true,\n      // Interpolation function that allows you to intercept the value from the axis label\n      labelInterpolationFnc: Chartist.noop,\n      // This value specifies the minimum height in pixel of the scale steps\n      scaleMinSpace: 20,\n      // Use only integer values (whole numbers) for the scale steps\n      onlyInteger: false\n    },\n    // Specify a fixed width for the chart as a string (i.e. '100px' or '50%')\n    width: undefined,\n    // Specify a fixed height for the chart as a string (i.e. '100px' or '50%')\n    height: undefined,\n    // Overriding the natural high of the chart allows you to zoom in or limit the charts highest displayed value\n    high: undefined,\n    // Overriding the natural low of the chart allows you to zoom in or limit the charts lowest displayed value\n    low: undefined,\n    // Use only integer values (whole numbers) for the scale steps\n    onlyInteger: false,\n    // Padding of the chart drawing area to the container element and labels as a number or padding object {top: 5, right: 5, bottom: 5, left: 5}\n    chartPadding: {\n      top: 15,\n      right: 15,\n      bottom: 5,\n      left: 10\n    },\n    // Specify the distance in pixel of bars in a group\n    seriesBarDistance: 15,\n    // If set to true this property will cause the series bars to be stacked and form a total for each series point. This will also influence the y-axis and the overall bounds of the chart. In stacked mode the seriesBarDistance property will have no effect.\n    stackBars: false,\n    // Inverts the axes of the bar chart in order to draw a horizontal bar chart. Be aware that you also need to invert your axis settings as the Y Axis will now display the labels and the X Axis the values.\n    horizontalBars: false,\n    // If set to true then each bar will represent a series and the data array is expected to be a one dimensional array of data values rather than a series array of series. This is useful if the bar chart should represent a profile rather than some data over time.\n    distributeSeries: false,\n    // If true the whole data is reversed including labels, the series order as well as the whole series data arrays.\n    reverseData: false,\n    // Override the class names that get used to generate the SVG structure of the chart\n    classNames: {\n      chart: 'ct-chart-bar',\n      horizontalBars: 'ct-horizontal-bars',\n      label: 'ct-label',\n      labelGroup: 'ct-labels',\n      series: 'ct-series',\n      bar: 'ct-bar',\n      grid: 'ct-grid',\n      gridGroup: 'ct-grids',\n      vertical: 'ct-vertical',\n      horizontal: 'ct-horizontal',\n      start: 'ct-start',\n      end: 'ct-end'\n    }\n  };\n\n  /**\n   * Creates a new chart\n   *\n   */\n  function createChart(options) {\n    var seriesGroups = [];\n    var data = Chartist.getDataArray(this.data, options.reverseData);\n    var normalizedData = options.distributeSeries ? data.map(function(value) {\n      return [value];\n    }) : Chartist.normalizeDataArray(data, this.data.labels.length);\n    var highLow;\n\n    // Create new svg element\n    this.svg = Chartist.createSvg(\n      this.container,\n      options.width,\n      options.height,\n      options.classNames.chart + (options.horizontalBars ? ' ' + options.classNames.horizontalBars : '')\n    );\n\n    if(options.stackBars) {\n      // If stacked bars we need to calculate the high low from stacked values from each series\n      var serialSums = Chartist.serialMap(normalizedData, function serialSums() {\n        return Array.prototype.slice.call(arguments).reduce(Chartist.sum, 0);\n      });\n\n      highLow = Chartist.getHighLow([serialSums], options);\n    } else {\n      highLow = Chartist.getHighLow(normalizedData, options);\n    }\n    // Overrides of high / low from settings\n    highLow.high = +options.high || (options.high === 0 ? 0 : highLow.high);\n    highLow.low = +options.low || (options.low === 0 ? 0 : highLow.low);\n\n    var chartRect = Chartist.createChartRect(this.svg, options, defaultOptions.padding);\n\n    var valueAxis,\n      labelAxisStepCount,\n      labelAxis,\n      axisX,\n      axisY;\n\n    // We need to set step count based on some options combinations\n    if(options.distributeSeries && !options.stackBars) {\n      // If distributed series are enabled but stacked bars aren't, we need to use the one dimensional series array\n      // length as step count for the label axis\n      labelAxisStepCount = normalizedData.length;\n    } else if(options.distributeSeries && options.stackBars) {\n      // If distributed series are enabled and bars need to be stacked, we'll only have one bar and therefore set step\n      // count to 1\n      labelAxisStepCount = 1;\n    } else {\n      // If we are drawing a regular bar chart with two dimensional series data, we just use the labels array length\n      // as the bars are normalized\n      labelAxisStepCount = this.data.labels.length;\n    }\n\n    // Set labelAxis and valueAxis based on the horizontalBars setting. This setting will flip the axes if necessary.\n    if(options.horizontalBars) {\n      labelAxis = axisY = new Chartist.StepAxis(Chartist.Axis.units.y, chartRect, {\n        stepCount: labelAxisStepCount\n      });\n\n      valueAxis = axisX = new Chartist.LinearScaleAxis(Chartist.Axis.units.x, chartRect, {\n        highLow: highLow,\n        scaleMinSpace: options.axisX.scaleMinSpace,\n        onlyInteger: options.axisX.onlyInteger,\n        referenceValue: 0\n      });\n    } else {\n      labelAxis = axisX = new Chartist.StepAxis(Chartist.Axis.units.x, chartRect, {\n        stepCount: labelAxisStepCount\n      });\n\n      valueAxis = axisY = new Chartist.LinearScaleAxis(Chartist.Axis.units.y, chartRect, {\n        highLow: highLow,\n        scaleMinSpace: options.axisY.scaleMinSpace,\n        onlyInteger: options.axisY.onlyInteger,\n        referenceValue: 0\n      });\n    }\n\n    // Start drawing\n    var labelGroup = this.svg.elem('g').addClass(options.classNames.labelGroup),\n      gridGroup = this.svg.elem('g').addClass(options.classNames.gridGroup),\n      // Projected 0 point\n      zeroPoint = options.horizontalBars ? (chartRect.x1 + valueAxis.projectValue(0).pos) : (chartRect.y1 - valueAxis.projectValue(0).pos),\n      // Used to track the screen coordinates of stacked bars\n      stackedBarValues = [];\n\n    Chartist.createAxis(\n      labelAxis,\n      this.data.labels,\n      chartRect,\n      gridGroup,\n      labelGroup,\n      this.supportsForeignObject,\n      options,\n      this.eventEmitter\n    );\n\n    Chartist.createAxis(\n      valueAxis,\n      valueAxis.bounds.values,\n      chartRect,\n      gridGroup,\n      labelGroup,\n      this.supportsForeignObject,\n      options,\n      this.eventEmitter\n    );\n\n    // Draw the series\n    this.data.series.forEach(function(series, seriesIndex) {\n      // Calculating bi-polar value of index for seriesOffset. For i = 0..4 biPol will be -1.5, -0.5, 0.5, 1.5 etc.\n      var biPol = seriesIndex - (this.data.series.length - 1) / 2,\n      // Half of the period width between vertical grid lines used to position bars\n        periodHalfLength;\n\n      // We need to set periodHalfLength based on some options combinations\n      if(options.distributeSeries && !options.stackBars) {\n        // If distributed series are enabled but stacked bars aren't, we need to use the length of the normaizedData array\n        // which is the series count and divide by 2\n        periodHalfLength = labelAxis.axisLength / normalizedData.length / 2;\n      } else if(options.distributeSeries && options.stackBars) {\n        // If distributed series and stacked bars are enabled we'll only get one bar so we should just divide the axis\n        // length by 2\n        periodHalfLength = labelAxis.axisLength / 2;\n      } else {\n        // On regular bar charts we should just use the series length\n        periodHalfLength = labelAxis.axisLength / normalizedData[seriesIndex].length / 2;\n      }\n\n      seriesGroups[seriesIndex] = this.svg.elem('g');\n\n      // Write attributes to series group element. If series name or meta is undefined the attributes will not be written\n      seriesGroups[seriesIndex].attr({\n        'series-name': series.name,\n        'meta': Chartist.serialize(series.meta)\n      }, Chartist.xmlNs.uri);\n\n      // Use series class from series data or if not set generate one\n      seriesGroups[seriesIndex].addClass([\n        options.classNames.series,\n        (series.className || options.classNames.series + '-' + Chartist.alphaNumerate(seriesIndex))\n      ].join(' '));\n\n      normalizedData[seriesIndex].forEach(function(value, valueIndex) {\n        var projected,\n          bar,\n          previousStack,\n          labelAxisValueIndex;\n\n        // We need to set labelAxisValueIndex based on some options combinations\n        if(options.distributeSeries && !options.stackBars) {\n          // If distributed series are enabled but stacked bars aren't, we can use the seriesIndex for later projection\n          // on the step axis for label positioning\n          labelAxisValueIndex = seriesIndex;\n        } else if(options.distributeSeries && options.stackBars) {\n          // If distributed series and stacked bars are enabled, we will only get one bar and therefore always use\n          // 0 for projection on the label step axis\n          labelAxisValueIndex = 0;\n        } else {\n          // On regular bar charts we just use the value index to project on the label step axis\n          labelAxisValueIndex = valueIndex;\n        }\n\n        // We need to transform coordinates differently based on the chart layout\n        if(options.horizontalBars) {\n          projected = {\n            x: chartRect.x1 + valueAxis.projectValue(value || 0, valueIndex, normalizedData[seriesIndex]).pos,\n            y: chartRect.y1 - labelAxis.projectValue(value || 0, labelAxisValueIndex, normalizedData[seriesIndex]).pos\n          };\n        } else {\n          projected = {\n            x: chartRect.x1 + labelAxis.projectValue(value || 0, labelAxisValueIndex, normalizedData[seriesIndex]).pos,\n            y: chartRect.y1 - valueAxis.projectValue(value || 0, valueIndex, normalizedData[seriesIndex]).pos\n          }\n        }\n\n        // Offset to center bar between grid lines\n        projected[labelAxis.units.pos] += periodHalfLength * (options.horizontalBars ? -1 : 1);\n        // Using bi-polar offset for multiple series if no stacked bars or series distribution is used\n        projected[labelAxis.units.pos] += (options.stackBars || options.distributeSeries) ? 0 : biPol * options.seriesBarDistance * (options.horizontalBars ? -1 : 1);\n\n        // Enter value in stacked bar values used to remember previous screen value for stacking up bars\n        previousStack = stackedBarValues[valueIndex] || zeroPoint;\n        stackedBarValues[valueIndex] = previousStack - (zeroPoint - projected[labelAxis.counterUnits.pos]);\n\n        // Skip if value is undefined\n        if(value === undefined) {\n          return;\n        }\n\n        var positions = {};\n        positions[labelAxis.units.pos + '1'] = projected[labelAxis.units.pos];\n        positions[labelAxis.units.pos + '2'] = projected[labelAxis.units.pos];\n        // If bars are stacked we use the stackedBarValues reference and otherwise base all bars off the zero line\n        positions[labelAxis.counterUnits.pos + '1'] = options.stackBars ? previousStack : zeroPoint;\n        positions[labelAxis.counterUnits.pos + '2'] = options.stackBars ? stackedBarValues[valueIndex] : projected[labelAxis.counterUnits.pos];\n\n        bar = seriesGroups[seriesIndex].elem('line', positions, options.classNames.bar).attr({\n          'value': value,\n          'meta': Chartist.getMetaData(series, valueIndex)\n        }, Chartist.xmlNs.uri);\n\n        this.eventEmitter.emit('draw', Chartist.extend({\n          type: 'bar',\n          value: value,\n          index: valueIndex,\n          meta: Chartist.getMetaData(series, valueIndex),\n          series: series,\n          seriesIndex: seriesIndex,\n          chartRect: chartRect,\n          group: seriesGroups[seriesIndex],\n          element: bar\n        }, positions));\n      }.bind(this));\n    }.bind(this));\n\n    this.eventEmitter.emit('created', {\n      bounds: valueAxis.bounds,\n      chartRect: chartRect,\n      axisX: axisX,\n      axisY: axisY,\n      svg: this.svg,\n      options: options\n    });\n  }\n\n  /**\n   * This method creates a new bar chart and returns API object that you can use for later changes.\n   *\n   * @memberof Chartist.Bar\n   * @param {String|Node} query A selector query string or directly a DOM element\n   * @param {Object} data The data object that needs to consist of a labels and a series array\n   * @param {Object} [options] The options object with options that override the default options. Check the examples for a detailed list.\n   * @param {Array} [responsiveOptions] Specify an array of responsive option arrays which are a media query and options object pair => [[mediaQueryString, optionsObject],[more...]]\n   * @return {Object} An object which exposes the API for the created chart\n   *\n   * @example\n   * // Create a simple bar chart\n   * var data = {\n   *   labels: ['Mon', 'Tue', 'Wed', 'Thu', 'Fri'],\n   *   series: [\n   *     [5, 2, 4, 2, 0]\n   *   ]\n   * };\n   *\n   * // In the global name space Chartist we call the Bar function to initialize a bar chart. As a first parameter we pass in a selector where we would like to get our chart created and as a second parameter we pass our data object.\n   * new Chartist.Bar('.ct-chart', data);\n   *\n   * @example\n   * // This example creates a bipolar grouped bar chart where the boundaries are limitted to -10 and 10\n   * new Chartist.Bar('.ct-chart', {\n   *   labels: [1, 2, 3, 4, 5, 6, 7],\n   *   series: [\n   *     [1, 3, 2, -5, -3, 1, -6],\n   *     [-5, -2, -4, -1, 2, -3, 1]\n   *   ]\n   * }, {\n   *   seriesBarDistance: 12,\n   *   low: -10,\n   *   high: 10\n   * });\n   *\n   */\n  function Bar(query, data, options, responsiveOptions) {\n    Chartist.Bar.super.constructor.call(this,\n      query,\n      data,\n      defaultOptions,\n      Chartist.extend({}, defaultOptions, options),\n      responsiveOptions);\n  }\n\n  // Creating bar chart type in Chartist namespace\n  Chartist.Bar = Chartist.Base.extend({\n    constructor: Bar,\n    createChart: createChart\n  });\n\n}(window, document, Chartist));\n;/**\n * The pie chart module of Chartist that can be used to draw pie, donut or gauge charts\n *\n * @module Chartist.Pie\n */\n/* global Chartist */\n(function(window, document, Chartist) {\n  'use strict';\n\n  /**\n   * Default options in line charts. Expand the code view to see a detailed list of options with comments.\n   *\n   * @memberof Chartist.Pie\n   */\n  var defaultOptions = {\n    // Specify a fixed width for the chart as a string (i.e. '100px' or '50%')\n    width: undefined,\n    // Specify a fixed height for the chart as a string (i.e. '100px' or '50%')\n    height: undefined,\n    // Padding of the chart drawing area to the container element and labels as a number or padding object {top: 5, right: 5, bottom: 5, left: 5}\n    chartPadding: 5,\n    // Override the class names that are used to generate the SVG structure of the chart\n    classNames: {\n      chartPie: 'ct-chart-pie',\n      chartDonut: 'ct-chart-donut',\n      series: 'ct-series',\n      slicePie: 'ct-slice-pie',\n      sliceDonut: 'ct-slice-donut',\n      label: 'ct-label'\n    },\n    // The start angle of the pie chart in degrees where 0 points north. A higher value offsets the start angle clockwise.\n    startAngle: 0,\n    // An optional total you can specify. By specifying a total value, the sum of the values in the series must be this total in order to draw a full pie. You can use this parameter to draw only parts of a pie or gauge charts.\n    total: undefined,\n    // If specified the donut CSS classes will be used and strokes will be drawn instead of pie slices.\n    donut: false,\n    // Specify the donut stroke width, currently done in javascript for convenience. May move to CSS styles in the future.\n    donutWidth: 60,\n    // If a label should be shown or not\n    showLabel: true,\n    // Label position offset from the standard position which is half distance of the radius. This value can be either positive or negative. Positive values will position the label away from the center.\n    labelOffset: 0,\n    // This option can be set to 'inside', 'outside' or 'center'. Positioned with 'inside' the labels will be placed on half the distance of the radius to the border of the Pie by respecting the 'labelOffset'. The 'outside' option will place the labels at the border of the pie and 'center' will place the labels in the absolute center point of the chart. The 'center' option only makes sense in conjunction with the 'labelOffset' option.\n    labelPosition: 'inside',\n    // An interpolation function for the label value\n    labelInterpolationFnc: Chartist.noop,\n    // Label direction can be 'neutral', 'explode' or 'implode'. The labels anchor will be positioned based on those settings as well as the fact if the labels are on the right or left side of the center of the chart. Usually explode is useful when labels are positioned far away from the center.\n    labelDirection: 'neutral',\n    // If true the whole data is reversed including labels, the series order as well as the whole series data arrays.\n    reverseData: false\n  };\n\n  /**\n   * Determines SVG anchor position based on direction and center parameter\n   *\n   * @param center\n   * @param label\n   * @param direction\n   * @return {string}\n   */\n  function determineAnchorPosition(center, label, direction) {\n    var toTheRight = label.x > center.x;\n\n    if(toTheRight && direction === 'explode' ||\n      !toTheRight && direction === 'implode') {\n      return 'start';\n    } else if(toTheRight && direction === 'implode' ||\n      !toTheRight && direction === 'explode') {\n      return 'end';\n    } else {\n      return 'middle';\n    }\n  }\n\n  /**\n   * Creates the pie chart\n   *\n   * @param options\n   */\n  function createChart(options) {\n    var seriesGroups = [],\n      chartRect,\n      radius,\n      labelRadius,\n      totalDataSum,\n      startAngle = options.startAngle,\n      dataArray = Chartist.getDataArray(this.data, options.reverseData);\n\n    // Create SVG.js draw\n    this.svg = Chartist.createSvg(this.container, options.width, options.height,options.donut ? options.classNames.chartDonut : options.classNames.chartPie);\n    // Calculate charting rect\n    chartRect = Chartist.createChartRect(this.svg, options, defaultOptions.padding);\n    // Get biggest circle radius possible within chartRect\n    radius = Math.min(chartRect.width() / 2, chartRect.height() / 2);\n    // Calculate total of all series to get reference value or use total reference from optional options\n    totalDataSum = options.total || dataArray.reduce(function(previousValue, currentValue) {\n      return previousValue + currentValue;\n    }, 0);\n\n    // If this is a donut chart we need to adjust our radius to enable strokes to be drawn inside\n    // Unfortunately this is not possible with the current SVG Spec\n    // See this proposal for more details: http://lists.w3.org/Archives/Public/www-svg/2003Oct/0000.html\n    radius -= options.donut ? options.donutWidth / 2  : 0;\n\n    // If labelPosition is set to `outside` or a donut chart is drawn then the label position is at the radius,\n    // if regular pie chart it's half of the radius\n    if(options.labelPosition === 'outside' || options.donut) {\n      labelRadius = radius;\n    } else if(options.labelPosition === 'center') {\n      // If labelPosition is center we start with 0 and will later wait for the labelOffset\n      labelRadius = 0;\n    } else {\n      // Default option is 'inside' where we use half the radius so the label will be placed in the center of the pie\n      // slice\n      labelRadius = radius / 2;\n    }\n    // Add the offset to the labelRadius where a negative offset means closed to the center of the chart\n    labelRadius += options.labelOffset;\n\n    // Calculate end angle based on total sum and current data value and offset with padding\n    var center = {\n      x: chartRect.x1 + chartRect.width() / 2,\n      y: chartRect.y2 + chartRect.height() / 2\n    };\n\n    // Check if there is only one non-zero value in the series array.\n    var hasSingleValInSeries = this.data.series.filter(function(val) {\n      return val.hasOwnProperty('value') ? val.value !== 0 : val !== 0;\n    }).length === 1;\n\n    // Draw the series\n    // initialize series groups\n    for (var i = 0; i < this.data.series.length; i++) {\n      var series = this.data.series[i];\n      seriesGroups[i] = this.svg.elem('g', null, null, true);\n\n      // If the series is an object and contains a name or meta data we add a custom attribute\n      seriesGroups[i].attr({\n        'series-name': series.name\n      }, Chartist.xmlNs.uri);\n\n      // Use series class from series data or if not set generate one\n      seriesGroups[i].addClass([\n        options.classNames.series,\n        (series.className || options.classNames.series + '-' + Chartist.alphaNumerate(i))\n      ].join(' '));\n\n      var endAngle = startAngle + dataArray[i] / totalDataSum * 360;\n      // If we need to draw the arc for all 360 degrees we need to add a hack where we close the circle\n      // with Z and use 359.99 degrees\n      if(endAngle - startAngle === 360) {\n        endAngle -= 0.01;\n      }\n\n      var start = Chartist.polarToCartesian(center.x, center.y, radius, startAngle - (i === 0 || hasSingleValInSeries ? 0 : 0.2)),\n        end = Chartist.polarToCartesian(center.x, center.y, radius, endAngle);\n\n      // Create a new path element for the pie chart. If this isn't a donut chart we should close the path for a correct stroke\n      var path = new Chartist.Svg.Path(!options.donut)\n        .move(end.x, end.y)\n        .arc(radius, radius, 0, endAngle - startAngle > 180, 0, start.x, start.y);\n\n      // If regular pie chart (no donut) we add a line to the center of the circle for completing the pie\n      if(!options.donut) {\n        path.line(center.x, center.y);\n      }\n\n      // Create the SVG path\n      // If this is a donut chart we add the donut class, otherwise just a regular slice\n      var pathElement = seriesGroups[i].elem('path', {\n        d: path.stringify()\n      }, options.donut ? options.classNames.sliceDonut : options.classNames.slicePie);\n\n      // Adding the pie series value to the path\n      pathElement.attr({\n        'value': dataArray[i],\n        'meta': Chartist.serialize(series.meta)\n      }, Chartist.xmlNs.uri);\n\n      // If this is a donut, we add the stroke-width as style attribute\n      if(options.donut) {\n        pathElement.attr({\n          'style': 'stroke-width: ' + (+options.donutWidth) + 'px'\n        });\n      }\n\n      // Fire off draw event\n      this.eventEmitter.emit('draw', {\n        type: 'slice',\n        value: dataArray[i],\n        totalDataSum: totalDataSum,\n        index: i,\n        meta: series.meta,\n        series: series,\n        group: seriesGroups[i],\n        element: pathElement,\n        path: path.clone(),\n        center: center,\n        radius: radius,\n        startAngle: startAngle,\n        endAngle: endAngle\n      });\n\n      // If we need to show labels we need to add the label for this slice now\n      if(options.showLabel) {\n        // Position at the labelRadius distance from center and between start and end angle\n        var labelPosition = Chartist.polarToCartesian(center.x, center.y, labelRadius, startAngle + (endAngle - startAngle) / 2),\n          interpolatedValue = options.labelInterpolationFnc(this.data.labels ? this.data.labels[i] : dataArray[i], i);\n\n        if(interpolatedValue || interpolatedValue === 0) {\n          var labelElement = seriesGroups[i].elem('text', {\n            dx: labelPosition.x,\n            dy: labelPosition.y,\n            'text-anchor': determineAnchorPosition(center, labelPosition, options.labelDirection)\n          }, options.classNames.label).text('' + interpolatedValue);\n\n          // Fire off draw event\n          this.eventEmitter.emit('draw', {\n            type: 'label',\n            index: i,\n            group: seriesGroups[i],\n            element: labelElement,\n            text: '' + interpolatedValue,\n            x: labelPosition.x,\n            y: labelPosition.y\n          });\n        }\n      }\n\n      // Set next startAngle to current endAngle. Use slight offset so there are no transparent hairline issues\n      // (except for last slice)\n      startAngle = endAngle;\n    }\n\n    this.eventEmitter.emit('created', {\n      chartRect: chartRect,\n      svg: this.svg,\n      options: options\n    });\n  }\n\n  /**\n   * This method creates a new pie chart and returns an object that can be used to redraw the chart.\n   *\n   * @memberof Chartist.Pie\n   * @param {String|Node} query A selector query string or directly a DOM element\n   * @param {Object} data The data object in the pie chart needs to have a series property with a one dimensional data array. The values will be normalized against each other and don't necessarily need to be in percentage. The series property can also be an array of value objects that contain a value property and a className property to override the CSS class name for the series group.\n   * @param {Object} [options] The options object with options that override the default options. Check the examples for a detailed list.\n   * @param {Array} [responsiveOptions] Specify an array of responsive option arrays which are a media query and options object pair => [[mediaQueryString, optionsObject],[more...]]\n   * @return {Object} An object with a version and an update method to manually redraw the chart\n   *\n   * @example\n   * // Simple pie chart example with four series\n   * new Chartist.Pie('.ct-chart', {\n   *   series: [10, 2, 4, 3]\n   * });\n   *\n   * @example\n   * // Drawing a donut chart\n   * new Chartist.Pie('.ct-chart', {\n   *   series: [10, 2, 4, 3]\n   * }, {\n   *   donut: true\n   * });\n   *\n   * @example\n   * // Using donut, startAngle and total to draw a gauge chart\n   * new Chartist.Pie('.ct-chart', {\n   *   series: [20, 10, 30, 40]\n   * }, {\n   *   donut: true,\n   *   donutWidth: 20,\n   *   startAngle: 270,\n   *   total: 200\n   * });\n   *\n   * @example\n   * // Drawing a pie chart with padding and labels that are outside the pie\n   * new Chartist.Pie('.ct-chart', {\n   *   series: [20, 10, 30, 40]\n   * }, {\n   *   chartPadding: 30,\n   *   labelOffset: 50,\n   *   labelDirection: 'explode'\n   * });\n   *\n   * @example\n   * // Overriding the class names for individual series as well as a name and meta data.\n   * // The name will be written as ct:series-name attribute and the meta data will be serialized and written\n   * // to a ct:meta attribute.\n   * new Chartist.Pie('.ct-chart', {\n   *   series: [{\n   *     value: 20,\n   *     name: 'Series 1',\n   *     className: 'my-custom-class-one',\n   *     meta: 'Meta One'\n   *   }, {\n   *     value: 10,\n   *     name: 'Series 2',\n   *     className: 'my-custom-class-two',\n   *     meta: 'Meta Two'\n   *   }, {\n   *     value: 70,\n   *     name: 'Series 3',\n   *     className: 'my-custom-class-three',\n   *     meta: 'Meta Three'\n   *   }]\n   * });\n   */\n  function Pie(query, data, options, responsiveOptions) {\n    Chartist.Pie.super.constructor.call(this,\n      query,\n      data,\n      defaultOptions,\n      Chartist.extend({}, defaultOptions, options),\n      responsiveOptions);\n  }\n\n  // Creating pie chart type in Chartist namespace\n  Chartist.Pie = Chartist.Base.extend({\n    constructor: Pie,\n    createChart: createChart,\n    determineAnchorPosition: determineAnchorPosition\n  });\n\n}(window, document, Chartist));\n\nreturn Chartist;\n\n}));\n"]}